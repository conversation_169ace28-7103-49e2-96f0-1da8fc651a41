# Test API Cập N<PERSON><PERSON>t <PERSON>ơn Hàng

## Endpoint
```
PATCH /v1/user/orders/:id
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Test Cases

### 1. Cập nhật trạng thái đơn hàng
```json
{
  "orderStatus": "confirmed",
  "shippingStatus": "preparing"
}
```

### 2. Cập nhật thông tin hóa đơn
```json
{
  "billInfo": {
    "subtotal": 350000,
    "shippingFee": 35000,
    "discount": 15000,
    "paymentMethod": "BANK_TRANSFER",
    "paymentStatus": "PAID"
  }
}
```

### 3. Cập nhật thông tin vận chuyển
```json
{
  "logisticInfo": {
    "shippingMethod": "Nhanh",
    "carrier": "GHN",
    "shippingNote": "Giao hàng trong gi<PERSON> hành ch<PERSON>",
    "deliveryAddress": {
      "existingAddressId": 5
    }
  }
}
```

### 4. <PERSON><PERSON><PERSON> nhật nguồn đơn hàng
```json
{
  "source": "mobile_app"
}
```

### 5. Cập nhật hasShipping
```json
{
  "hasShipping": false
}
```

## Expected Response
```json
{
  "success": true,
  "message": "Cập nhật đơn hàng thành công",
  "data": {
    "id": 123,
    "userConvertCustomerId": 456,
    "userId": 789,
    "productInfo": {...},
    "billInfo": {...},
    "hasShipping": true,
    "shippingStatus": "preparing",
    "logisticInfo": {...},
    "orderStatus": "confirmed",
    "source": "mobile_app",
    "createdAt": *************,
    "updatedAt": *************
  }
}
```

## Error Cases

### 1. Đơn hàng không tồn tại (404)
```json
{
  "success": false,
  "message": "Không tìm thấy đơn hàng với ID 999"
}
```

### 2. Không có quyền truy cập (403)
```json
{
  "success": false,
  "message": "Bạn không có quyền cập nhật đơn hàng này"
}
```

### 3. Đơn hàng đã hoàn thành/hủy (400)
```json
{
  "success": false,
  "message": "Không thể cập nhật đơn hàng đã hoàn thành hoặc đã hủy"
}
```

## Validation Rules
- Chỉ có thể cập nhật đơn hàng chưa hoàn thành hoặc chưa hủy
- Người dùng chỉ có thể cập nhật đơn hàng của mình
- Các trường trong DTO đều optional, cho phép cập nhật từng phần
- orderStatus phải là enum hợp lệ: pending, confirmed, processing, completed, cancelled
- shippingStatus phải là enum hợp lệ
- source không được vượt quá 45 ký tự
