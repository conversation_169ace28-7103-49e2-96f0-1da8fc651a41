const axios = require('axios');

// Test API tạo custom field với select type - Input từ FE
async function testSelectCustomField() {
  try {
    const response = await axios.post('http://localhost:3000/v1/user/custom-fields', {
      config: {
        id: "gioitinh-test",
        label: "Gioi tinh",
        displayName: "Gioi tinh",
        type: "select",
        required: false,
        validation: {},
        options: [
          {
            label: "a|1\nb|2",
            value: "a|1_b|2"
          }
        ]
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // Thay bằng token thực
      }
    });

    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    // Kiểm tra cấu trúc options
    const configJson = response.data.result.configJson;
    if (configJson.validation && configJson.validation.options) {
      console.log('\n✅ Options được parse đúng:');
      configJson.validation.options.forEach((option, index) => {
        console.log(`  ${index + 1}. label: "${option.label}", value: "${option.value}"`);
      });
    } else {
      console.log('\n❌ Options không được parse đúng');
    }

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Test với nhiều options
async function testMultipleOptions() {
  try {
    const response = await axios.post('http://localhost:3000/v1/user/custom-fields', {
      config: {
        id: "kich-thuoc-test",
        label: "Kích thước",
        type: "select",
        required: true,
        validation: "XS|xs\nS|s\nM|m\nL|l\nXL|xl"
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // Thay bằng token thực
      }
    });

    console.log('\n=== Test Multiple Options ===');
    console.log('Response Status:', response.status);
    
    const configJson = response.data.result.configJson;
    if (configJson.validation && configJson.validation.options) {
      console.log('\n✅ Multiple options được parse đúng:');
      configJson.validation.options.forEach((option, index) => {
        console.log(`  ${index + 1}. label: "${option.label}", value: "${option.value}"`);
      });
    } else {
      console.log('\n❌ Multiple options không được parse đúng');
    }

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Chạy test
console.log('🧪 Testing Custom Field API with Select Type...\n');
testSelectCustomField();
setTimeout(() => testMultipleOptions(), 2000);
