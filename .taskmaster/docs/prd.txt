# Product Requirements Document (PRD)
## Implement Update Agent System API Logic

### Project Overview
Implement the complete logic for updating agent system in NestJS application following Layered Architecture, DDD, and Repository Pattern.

### Current State
- Controller method exists with proper Swagger documentation
- UpdateAgentSystemDto is complete with validation
- Response type updated to: `Promise<ApiResponseDto<{ id: string; avatarUrlUpload?: string }>>`
- Service method is commented out and needs implementation
- Repository methods exist for validation and updates

### Requirements

#### 1. Service Layer Implementation
- Implement AdminAgentSystemService.update() method
- Handle all business logic and validation
- Return proper response structure with id and optional avatarUrlUpload
- Use @Transactional decorator for data consistency
- Proper error handling with AppException

#### 2. Validation Requirements
- Validate agent system exists by ID
- Validate nameCode uniqueness (if changed)
- Validate supervisor uniqueness (if isSupervisor changed to true)
- Validate system model exists (if modelId provided)
- Validate vector store exists (if vectorStoreId provided)
- Validate MCP systems exist (if mcpId array provided)

#### 3. Update Logic
- Update agents table: name, modelConfig, instruction, avatar key
- Update agents_system table: nameCode, description, systemModelId, updatedBy
- Handle MCP systems linking/unlinking
- Generate S3 presigned URL for avatar upload if avatarMimeType provided

#### 4. Avatar Upload Handling
- Generate S3 key using generateS3Key helper
- Create presigned URL using S3Service
- Update agent.avatar field with S3 key
- Return upload URL in response

#### 5. Error Handling
- Use AGENT_ERROR_CODES for specific errors
- Handle database transaction failures
- Validate all foreign key references
- Proper logging for debugging

#### 6. Response Structure
```typescript
{
  id: string;           // Agent system ID
  avatarUrlUpload?: string;  // S3 presigned URL if avatar update requested
}
```

### Technical Constraints
- Follow existing code patterns in the service
- Use existing repository methods
- Maintain transaction integrity
- Follow Vietnamese commenting standards
- Use proper TypeScript typing

### Success Criteria
- API successfully updates agent system data
- All validations work correctly
- Avatar upload URL generation works
- Proper error responses for invalid data
- Transaction rollback on failures
- Code follows project architecture standards
