# Product Requirements Document (PRD) - User Models API Implementation

## Project Overview
Triển khai 3 API mới cho module Models trong hệ thống NestJS để quản lý models cho user:
1. API danh sách model theo key LLM theo user_id từ bảng user_models với thông tin đầy đủ từ model_registry
2. API danh sách model từ bảng system_models với active = true
3. API danh sách user_data_fine_tune theo user_id

## Goals and Objectives
- Primary goal: Tạo 3 API endpoints mới cho user để quản lý models và fine-tune data
- Secondary goals: 
  - Đảm bảo tính nhất quán với kiến trúc hiện tại (Layered Architecture, DDD, Repository Pattern)
  - Implement proper pagination, filtering, và sorting
  - Sử dụng DTOs và mappers phù hợp
  - Xử lý lỗi bằng AppException
  - Tuân thủ security với JwtUserGuard

## User Stories
- As a user, I want to get list of models available through my LLM keys so that I can see which models I can use
- As a user, I want to get list of active system models so that I can see available system-provided models
- As a user, I want to get list of my fine-tune datasets so that I can manage my training data

## Technical Requirements

### API 1: User Models by LLM Keys
- Endpoint: GET /api/user/models/user-models-by-keys
- Lấy models từ bảng user_models theo user_id thông qua user_model_key_llm mapping
- Join với model_registry để lấy thông tin đầy đủ
- Hỗ trợ pagination, search, filtering theo provider
- Response bao gồm: id, modelId, provider, modelNamePattern, inputModalities, outputModalities, samplingParameters, features, basePricing, fineTunePricing, trainingPricing

### API 2: Active System Models  
- Endpoint: GET /api/user/models/system-models
- Lấy models từ bảng system_models với active = true
- Join với model_registry để lấy thông tin đầy đủ
- Hỗ trợ pagination, search, filtering theo provider
- Response tương tự API 1

### API 3: User Fine-Tune Datasets
- Endpoint: GET /api/user/models/fine-tune-datasets
- Lấy datasets từ bảng user_data_fine_tune theo user_id
- Hỗ trợ pagination, search, filtering theo status
- Response: id, name, description, status, createdAt, trainDataset, validDataset

### Technical Implementation
- Tạo UserModelsController mới trong user folder
- Tạo UserModelsService với business logic
- Sử dụng existing repositories: UserModelsRepository, SystemModelsRepository, UserDataFineTuneRepository
- Tạo DTOs: UserModelsQueryDto, UserModelsResponseDto, SystemModelsQueryDto, SystemModelsResponseDto
- Tạo mappers để convert entities sang DTOs
- Sử dụng ApiResponseDto.paginated() cho responses
- Implement proper error handling với AppException
- Thêm hasItems boolean field để phân biệt empty search results vs no data

### Database Queries Optimization
- Sử dụng QueryBuilder với createBaseQuery() method
- Tối ưu N+1 query problems bằng proper joins
- Chỉ select các fields cần thiết cho DTOs
- Sử dụng batch queries thay vì individual queries trong loops

### Security & Validation
- Sử dụng JwtUserGuard cho authentication
- Thêm @ApiBearerAuth('JWT-auth') cho Swagger
- Validate input với class-validator
- Transform data với class-transformer

## Success Criteria
- 3 API endpoints hoạt động đúng với pagination, filtering, sorting
- Code tuân thủ architecture patterns hiện tại
- Unit tests pass cho tất cả components
- API documentation đầy đủ với Swagger
- Performance tốt với database queries tối ưu
- Response time < 500ms cho queries thông thường
- Proper error handling và logging

## Timeline
- Phase 1: Tạo controller, service, DTOs, mappers (2-3 hours)
- Phase 2: Implement business logic và repository methods (2-3 hours)  
- Phase 3: Testing và documentation (1-2 hours)

## Dependencies
- Existing entities: UserModels, SystemModels, UserDataFineTune, ModelRegistry
- Existing repositories: UserModelsRepository, SystemModelsRepository, UserDataFineTuneRepository
- Common DTOs và response patterns từ @common/dto và @common/response
- Authentication system với JwtUserGuard
