# Product Requirements Document: Model Auto-Discovery và Sync System

## 1. Tổng quan dự án

### 1.1 M<PERSON>c tiêu
Xây dựng hệ thống tự động khám phá và đồng bộ hóa models từ các AI providers khi user/admin connect API key vào hệ thống.

### 1.2 Phạm vi
- Tự động lấy danh sách models từ providers khi connect key
- Lọc models theo model_name_pattern từ model_registry
- Lưu models phù hợp vào system_models/user_models
- Tạo mapping với model_registry_id
- Cập nhật key mapping trong system_model_key_llm/user_model_key_llm
- Tạo API reload models theo key cho user

## 2. <PERSON><PERSON><PERSON> cầu chức năng

### 2.1 Auto Model Discovery khi Connect Key
**Mô tả**: Khi admin/user thực hiện connect API key vào hệ thống, tự động:
- <PERSON><PERSON><PERSON> danh sách models từ provider thông qua AI Provider Helper
- <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả model_name_pattern từ model_registry
- Lọc models từ provider theo các pattern
- <PERSON><PERSON><PERSON> models phù hợp vào system_models (admin) hoặc user_models (user)
- Tạo mapping với model_registry_id tương ứng
- Cập nhật/tạo mới mapping trong system_model_key_llm/user_model_key_llm

**Input**: 
- API Key (đã được mã hóa)
- Provider type
- User ID (cho user keys) hoặc Employee ID (cho system keys)

**Output**:
- Danh sách models đã được lưu
- Số lượng models mới/đã tồn tại
- Thông tin mapping đã tạo

### 2.2 Model Pattern Matching Logic
**Mô tả**: Logic lọc models theo pattern
- Sử dụng model_name_pattern từ model_registry
- Hỗ trợ wildcard matching (LIKE pattern)
- Ưu tiên pattern chính xác nhất (độ dài ngắn nhất)
- Chỉ lưu models match với ít nhất 1 pattern

### 2.3 Duplicate Handling
**Mô tả**: Xử lý models đã tồn tại
- Kiểm tra model_id đã tồn tại trong system_models/user_models
- Nếu đã tồn tại: chỉ cập nhật mapping trong *_model_key_llm
- Nếu chưa tồn tại: tạo mới record và mapping

### 2.4 User Model Reload API
**Mô tả**: API cho user reload models từ key đã connect
- Endpoint: POST /user/models/reload-from-key
- Input: key_llm_id
- Thực hiện lại quá trình discovery cho key cụ thể
- Trả về danh sách models mới được thêm

## 3. Yêu cầu kỹ thuật

### 3.1 Architecture
- Sử dụng existing AI Provider Helper để lấy models
- Tận dụng ModelRegistryRepository.findByPatternMatch()
- Implement trong ModelsModule với services riêng biệt
- Sử dụng @Transactional() cho data consistency

### 3.2 Database Operations
- Bulk insert cho performance
- Upsert logic cho model records
- Batch mapping creation
- Proper error handling và rollback

### 3.3 Error Handling
- Provider connection failures
- Pattern matching errors
- Database constraint violations
- Partial success scenarios

### 3.4 Performance
- Batch operations cho large model lists
- Async processing cho multiple providers
- Caching cho model_registry patterns
- Rate limiting cho provider APIs

## 4. Acceptance Criteria

### 4.1 Admin System Key Connect
- Khi admin tạo system key mới, tự động discover models
- Models được lưu vào system_models với đúng model_registry_id
- Mapping được tạo trong system_model_key_llm
- Log chi tiết quá trình discovery

### 4.2 User Key Connect
- Khi user tạo user key mới, tự động discover models
- Models được lưu vào user_models với đúng model_registry_id
- Mapping được tạo trong user_model_key_llm
- User có thể reload models từ key

### 4.3 Pattern Matching
- Chỉ models match pattern mới được lưu
- Mapping với model_registry_id chính xác
- Xử lý multiple patterns cho 1 model

### 4.4 Duplicate Prevention
- Không tạo duplicate model records
- Cập nhật mapping cho existing models
- Maintain data integrity

## 5. Technical Implementation

### 5.1 Services cần tạo
- ModelDiscoveryService: Core logic discovery
- SystemModelSyncService: Sync cho system models
- UserModelSyncService: Sync cho user models

### 5.2 Repositories cần mở rộng
- SystemModelsRepository: Bulk operations
- UserModelsRepository: Bulk operations
- SystemModelKeyLlmRepository: Mapping operations
- UserModelKeyLlmRepository: Mapping operations

### 5.3 DTOs cần tạo
- ModelDiscoveryResultDto
- ModelSyncRequestDto
- ModelReloadRequestDto
- ModelSyncResponseDto

### 5.4 Integration Points
- Tích hợp vào existing key creation flow
- Hook vào AI Provider Helper
- Extend existing controllers

## 6. Testing Requirements

### 6.1 Unit Tests
- Pattern matching logic
- Duplicate handling
- Error scenarios
- Service methods

### 6.2 Integration Tests
- End-to-end key connect flow
- Database operations
- Provider integration
- API endpoints

## 7. Deliverables

1. ModelDiscoveryService với core logic
2. SystemModelSyncService và UserModelSyncService
3. Extended repositories với bulk operations
4. DTOs cho request/response
5. Integration vào existing key creation flow
6. User reload models API
7. Unit và integration tests
8. Documentation và comments

## 8. Success Metrics

- Models được tự động discover khi connect key
- Không có duplicate models trong database
- Pattern matching hoạt động chính xác
- User có thể reload models thành công
- Performance tốt với large model lists
- Error handling robust và informative
