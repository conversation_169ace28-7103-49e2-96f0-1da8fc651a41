import { ConfigType } from '@/config';
import { CategoryFolderEnum, generateS3Key } from '@/shared/utils';
import { AppException, ErrorCode } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import { ConfigService } from '@config/config.service';
import { FacebookConfig } from '@config/interfaces';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { CdnService } from '../cdn.service';
import { S3Service } from '../s3.service';
import {
  FacebookAuthResponse,
  FacebookLongLivedTokenResponse,
  FacebookMessageResponse,
  FacebookPageInfo,
  FacebookPictureResponse,
  FacebookUserInfo,
} from './interfaces/facebook.interface';

/**
 * Service để tương tác với Facebook API
 */
@Injectable()
export class FacebookService {
  private readonly logger = new Logger(FacebookService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {
    const servicesConfig = this.configService.getConfig<FacebookConfig>(ConfigType.Facebook);
    this.facebookConfig = servicesConfig;

    // Log cấu hình Facebook khi khởi tạo service
    this.logger.log(`[DEBUG] FacebookService initialized with config:`, {
      hasAppId: !!this.facebookConfig?.appId,
      hasAppSecret: !!this.facebookConfig?.appSecret,
      appId: this.facebookConfig?.appId ? `${this.facebookConfig.appId.substring(0, 15)}...` : 'null',
      appSecret: this.facebookConfig?.appSecret ? `${this.facebookConfig.appSecret.substring(0, 15)}...` : 'null',
      graphApiVersion: this.facebookConfig?.graphApiVersion,
      redirectUri: this.facebookConfig?.redirectUri,
      isMockConfig: this.facebookConfig?.appId?.includes('mock') || this.facebookConfig?.appSecret?.includes('mock'),
    });
  }

  /**
     * Lấy avatar của trang Facebook, upload lên S3 và trả về key
     * @param pageId ID của trang Facebook
     * @param accessToken Access token của trang hoặc người dùng có quyền truy cập trang
     * @returns S3 key của avatar đã upload
     */
  async getPageAvatarAndUploadToS3(pageId: string, accessToken: string, keyOld?: string): Promise<{ key: string }> {
    try {
      this.logger.log(`Bắt đầu lấy avatar của trang Facebook ${pageId}`);

      // Lấy trực tiếp ảnh avatar của trang Facebook với kích thước lớn
      const pictureResponse = await firstValueFrom(
        this.httpService.get<FacebookPictureResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/picture`,
          {
            params: {
              access_token: accessToken,
              type: 'large',
              redirect: 'false'
            }
          }
        )
      );

      // Tải avatar từ URL
      const response = await firstValueFrom(
        this.httpService.get(pictureResponse.data.data.url, { responseType: 'arraybuffer' })
      );

      // Tạo buffer từ dữ liệu nhận được
      const buffer = Buffer.from(response.data);

      // Xác định content type từ header hoặc mặc định là image/jpeg
      const contentType = response.headers['content-type'] || 'image/jpeg';

      // Tạo S3 key cho avatar
      const key = generateS3Key({
        baseFolder: 'facebook',
        categoryFolder: CategoryFolderEnum.PROFILE,
        fileName: `page-${pageId}-avatar.jpg`,
        useTimeFolder: true,
      });

      // Upload avatar lên S3
      await this.s3Service.uploadFile(keyOld || key, buffer, contentType);

      this.logger.log(`Đã upload avatar của trang Facebook ${pageId} lên S3 với key: ${key}`);

      return { key };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy và upload avatar của trang Facebook ${pageId}: ${error.message}`,
        error.stack
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy và upload avatar của trang Facebook',
        { pageId }
      );
    }
  }

  /**
   * Tạo URL xác thực Facebook
   * @param redirectUri URI callback sau khi xác thực
   * @param state Trạng thái để xác thực callback
   * @param scopes Danh sách quyền cần yêu cầu
   * @returns URL xác thực Facebook
   */
  createAuthUrl(redirectUri: string, state: string, scopes: string[] = []): string {
    if (!this.facebookConfig?.appId) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Thiếu cấu hình Facebook App ID',
      );
    }

    // Nếu không có scopes, sử dụng mặc định
    if (scopes.length === 0) {
      scopes = [
        'pages_show_list',
        'pages_messaging',
        'pages_manage_metadata',
        'pages_read_engagement',
      ];
    }

    return `https://www.facebook.com/${this.facebookConfig.graphApiVersion}/dialog/oauth?client_id=${this.facebookConfig.appId
      }&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${scopes.join(
        ',',
      )}&state=${state}`;
  }

  /**
   * Xử lý callback từ Facebook để lấy access token
   * @param code Code từ Facebook callback
   * @param redirectUri URI callback đã sử dụng khi tạo auth URL
   * @returns Access token và thông tin hết hạn
   */
  async handleCallback(code: string, redirectUri: string): Promise<FacebookAuthResponse> {
    this.logger.log(`[DEBUG] FacebookService.handleCallback started`);
    this.logger.log(`[DEBUG] Input parameters:`, {
      code: code ? `${code.substring(0, 20)}...` : 'null',
      redirectUri,
    });

    try {
      // Kiểm tra cấu hình Facebook
      this.logger.log(`[DEBUG] Kiểm tra cấu hình Facebook:`, {
        hasAppId: !!this.facebookConfig?.appId,
        hasAppSecret: !!this.facebookConfig?.appSecret,
        appId: this.facebookConfig?.appId ? `${this.facebookConfig.appId.substring(0, 10)}...` : 'null',
        graphApiVersion: this.facebookConfig?.graphApiVersion,
      });

      if (!this.facebookConfig?.appId || !this.facebookConfig?.appSecret) {
        this.logger.error(`[DEBUG] Thiếu cấu hình Facebook App`);
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App',
        );
      }

      // Kiểm tra xem có phải là mock config không
      if (this.facebookConfig.appId.includes('mock') || this.facebookConfig.appSecret.includes('mock')) {
        this.logger.error(`[DEBUG] Đang sử dụng cấu hình Facebook mock:`, {
          appId: this.facebookConfig.appId,
          appSecret: this.facebookConfig.appSecret.substring(0, 10) + '...',
        });
      }

      // Chuẩn bị request URL và params
      const requestUrl = `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/oauth/access_token`;
      const requestParams = {
        client_id: this.facebookConfig.appId,
        redirect_uri: redirectUri,
        client_secret: this.facebookConfig.appSecret,
        code: code,
      };

      this.logger.log(`[DEBUG] Gọi Facebook API:`, {
        url: requestUrl,
        params: {
          ...requestParams,
          client_secret: '***HIDDEN***',
          code: code ? `${code.substring(0, 20)}...` : 'null',
        },
      });

      // Gọi API để lấy access token
      const response = await firstValueFrom(
        this.httpService.get<FacebookAuthResponse>(requestUrl, {
          params: requestParams,
        }),
      );

      this.logger.log(`[DEBUG] Facebook API response:`, {
        status: response.status,
        hasAccessToken: !!response.data?.access_token,
        tokenType: response.data?.token_type,
        expiresIn: response.data?.expires_in,
      });

      if (!response.data.access_token) {
        this.logger.error(`[DEBUG] Facebook API không trả về access token:`, response.data);
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy access token từ Facebook',
        );
      }

      this.logger.log(`[DEBUG] FacebookService.handleCallback completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(`[DEBUG] FacebookService.handleCallback error:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: error.config ? {
          url: error.config.url,
          method: error.config.method,
          params: {
            ...error.config.params,
            client_secret: '***HIDDEN***',
          },
        } : 'No config',
      });

      // Kiểm tra loại lỗi cụ thể
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        this.logger.error(`[DEBUG] Facebook API 400 Error Details:`, errorData);

        if (errorData?.error?.message) {
          // Xử lý các lỗi Facebook cụ thể
          const facebookError = errorData.error;

          if (facebookError.code === 100 && facebookError.error_subcode === 36009) {
            // Authorization code đã được sử dụng
            throw new AppException(
              INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_AUTH_CODE_USED,
              'Mã xác thực Facebook đã được sử dụng. Vui lòng thực hiện lại quá trình xác thực.',
            );
          }

          if (facebookError.code === 100) {
            // OAuth Exception khác
            throw new AppException(
              ErrorCode.EXTERNAL_SERVICE_ERROR,
              `Lỗi xác thực Facebook: ${facebookError.message}`,
            );
          }

          // Lỗi Facebook khác
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            `Facebook API Error: ${facebookError.message}`,
          );
        }
      }

      this.logger.error(`Error handling Facebook callback: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xử lý callback từ Facebook',
      );
    }
  }

  /**
   * Lấy thông tin người dùng Facebook
   * @param accessToken Access token của người dùng
   * @returns Thông tin người dùng Facebook
   */
  async getUserInfo(accessToken: string): Promise<FacebookUserInfo> {
    try {
      // Gọi API để lấy thông tin người dùng
      const response = await firstValueFrom(
        this.httpService.get<FacebookUserInfo>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/me`,
          {
            params: {
              access_token: accessToken,
              fields: 'id,name,email,picture',
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting Facebook user info: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin người dùng Facebook',
      );
    }
  }

  /**
   * Lấy danh sách trang Facebook mà người dùng quản lý
   * @param accessToken Access token của người dùng
   * @param fields Các trường cần lấy, mặc định là 'id,name,access_token,category,tasks,picture'
   * @returns Danh sách trang Facebook
   */
  async getUserPages(
    accessToken: string,
    fields: string = 'id,name,access_token,category,tasks,picture',
  ): Promise<FacebookPageInfo[]> {
    try {
      // Gọi API để lấy danh sách trang
      const response = await firstValueFrom(
        this.httpService.get<{ data: FacebookPageInfo[] }>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/me/accounts`,
          {
            params: {
              access_token: accessToken,
              fields,
            },
          },
        ),
      );

      return response.data.data || [];
    } catch (error) {
      this.logger.error(`Error getting Facebook pages: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách trang Facebook',
      );
    }
  }

  /**
   * Lấy danh sách trang Facebook được gán cho người dùng
   * @param accessToken Access token của người dùng
   * @param fields Các trường cần lấy, mặc định là 'id,name,access_token,category,tasks,picture'
   * @returns Danh sách trang Facebook được gán
   */
  async getUserAssignedPages(
    accessToken: string,
    fields: string = 'id,name,access_token,category,tasks,picture',
  ): Promise<FacebookPageInfo[]> {
    try {
      // Gọi API để lấy danh sách trang được gán
      const response = await firstValueFrom(
        this.httpService.get<{ data: FacebookPageInfo[] }>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/me/assigned_pages`,
          {
            params: {
              access_token: accessToken,
              fields,
            },
          },
        ),
      );

      return response.data.data || [];
    } catch (error) {
      this.logger.error(`Error getting assigned Facebook pages: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách trang Facebook được gán',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của trang Facebook
   * @param pageId ID của trang Facebook
   * @param accessToken Access token của trang
   * @param fields Các trường cần lấy, mặc định là 'id,name,access_token,category,tasks,picture'
   * @returns Thông tin chi tiết của trang
   */
  async getPageDetails(
    pageId: string,
    accessToken: string,
    fields: string = 'id,name,access_token,category,tasks,picture'
  ): Promise<FacebookPageInfo> {
    try {
      // Gọi API để lấy thông tin trang
      const response = await firstValueFrom(
        this.httpService.get<FacebookPageInfo>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}`,
          {
            params: {
              access_token: accessToken,
              fields,
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting Facebook page details: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin chi tiết trang Facebook',
      );
    }
  }

  /**
   * Gửi tin nhắn đến người dùng thông qua trang Facebook
   * @param pageId ID của trang Facebook
   * @param pageAccessToken Access token của trang
   * @param userId ID của người dùng Facebook
   * @param message Nội dung tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendMessage(
    pageId: string,
    pageAccessToken: string,
    userId: string,
    message: string,
  ): Promise<FacebookMessageResponse> {
    try {
      // Gọi API để gửi tin nhắn
      const response = await firstValueFrom(
        this.httpService.post<FacebookMessageResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/messages`,
          {
            recipient: { id: userId },
            message: { text: message },
            messaging_type: 'RESPONSE',
          },
          {
            params: {
              access_token: pageAccessToken,
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error sending Facebook message: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn Facebook',
      );
    }
  }

  /**
   * Chuyển đổi short-lived access token thành long-lived access token
   * @param accessToken Short-lived access token cần chuyển đổi
   * @returns Long-lived access token và thông tin hết hạn
   */
  async getLongLivedToken(accessToken: string): Promise<FacebookLongLivedTokenResponse> {
    this.logger.log(`[DEBUG] getLongLivedToken started`);
    this.logger.log(`[DEBUG] Input accessToken:`, {
      hasToken: !!accessToken,
      tokenLength: accessToken?.length,
      tokenPrefix: accessToken ? `${accessToken.substring(0, 20)}...` : 'null',
    });

    try {
      if (!this.facebookConfig?.appId || !this.facebookConfig?.appSecret) {
        this.logger.error(`[DEBUG] Thiếu cấu hình Facebook App cho getLongLivedToken`);
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App',
        );
      }

      const requestUrl = `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/oauth/access_token`;
      const requestParams = {
        grant_type: 'fb_exchange_token',
        client_id: this.facebookConfig.appId,
        client_secret: this.facebookConfig.appSecret,
        fb_exchange_token: accessToken,
      };

      this.logger.log(`[DEBUG] Gọi Facebook API để lấy long-lived token:`, {
        url: requestUrl,
        params: {
          ...requestParams,
          client_secret: '***HIDDEN***',
          fb_exchange_token: accessToken ? `${accessToken.substring(0, 20)}...` : 'null',
        },
      });

      // Gọi API để lấy long-lived access token
      const response = await firstValueFrom(
        this.httpService.get<FacebookLongLivedTokenResponse>(requestUrl, {
          params: requestParams,
        }),
      );

      this.logger.log(`[DEBUG] Facebook long-lived token API response:`, {
        status: response.status,
        hasAccessToken: !!response.data?.access_token,
        tokenType: response.data?.token_type,
        expiresIn: response.data?.expires_in,
        fullResponseData: response.data,
      });

      if (!response.data.access_token) {
        this.logger.error(`[DEBUG] Facebook long-lived token response không có access_token:`, response.data);
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy long-lived access token từ Facebook',
        );
      }

      this.logger.log(`[DEBUG] getLongLivedToken completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(`[DEBUG] getLongLivedToken error:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });

      this.logger.error(`Error getting long-lived token: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy long-lived access token từ Facebook',
      );
    }
  }

  /**
   * Kiểm tra thông tin của access token
   * @param accessToken Access token cần kiểm tra
   * @returns Thông tin chi tiết của access token
   */
  async getTokenInfo(accessToken: string): Promise<FacebookLongLivedTokenResponse> {
    try {
      // Gọi API để lấy thông tin token
      const response = await firstValueFrom(
        this.httpService.get<FacebookLongLivedTokenResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/debug_token`,
          {
            params: {
              input_token: accessToken,
              access_token: `${this.facebookConfig.appId}|${this.facebookConfig.appSecret}`,
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting token info: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin access token từ Facebook',
      );
    }
  }

  /**
   * Đăng ký webhook cho ứng dụng Facebook
   * @param pageId ID của trang Facebook
   * @param pageAccessToken Access token của trang
   * @param fields Các trường cần đăng ký nhận webhook (messages, messaging_postbacks, message_deliveries, etc.)
   * @returns Kết quả đăng ký webhook
   */
  async subscribeApp(
    pageId: string,
    pageAccessToken: string,
    fields: string[] = ['messages', 'messaging_postbacks', 'message_deliveries']
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Bắt đầu đăng ký webhook cho trang Facebook ${pageId}`);

      if (!this.facebookConfig?.appId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App ID',
        );
      }

      // Gọi API để đăng ký webhook
      const response = await firstValueFrom(
        this.httpService.post(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/subscribed_apps`,
          {},
          {
            params: {
              access_token: pageAccessToken,
              subscribed_fields: fields.join(','),
            },
          },
        ),
      );

      if (response.data && response.data.success) {
        this.logger.log(`Đã đăng ký webhook thành công cho trang Facebook ${pageId}`);
        return { success: true };
      } else {
        throw new Error('Facebook API không trả về kết quả thành công');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi đăng ký webhook cho trang Facebook ${pageId}: ${error.message}`,
        error.stack
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi đăng ký webhook cho trang Facebook',
        { pageId }
      );
    }
  }

  /**
   * Hủy đăng ký webhook cho ứng dụng Facebook
   * @param pageId ID của trang Facebook
   * @param pageAccessToken Access token của trang
   * @returns Kết quả hủy đăng ký webhook
   */
  async unsubscribeApp(
    pageId: string,
    pageAccessToken: string
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Bắt đầu hủy đăng ký webhook cho trang Facebook ${pageId}`);

      if (!this.facebookConfig?.appId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App ID',
        );
      }

      // Gọi API để hủy đăng ký webhook
      const response = await firstValueFrom(
        this.httpService.delete(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/subscribed_apps`,
          {
            params: {
              access_token: pageAccessToken,
              app_id: this.facebookConfig.appId,
            },
          },
        ),
      );

      if (response.data && response.data.success) {
        this.logger.log(`Đã hủy đăng ký webhook thành công cho trang Facebook ${pageId}`);
        return { success: true };
      } else {
        throw new Error('Facebook API không trả về kết quả thành công');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi hủy đăng ký webhook cho trang Facebook ${pageId}: ${error.message}`,
        error.stack
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi hủy đăng ký webhook cho trang Facebook',
        { pageId }
      );
    }
  }
}

