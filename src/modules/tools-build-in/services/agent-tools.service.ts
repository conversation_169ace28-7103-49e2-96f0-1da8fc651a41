import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { AppException } from '@common/exceptions';
import { TOOLS_BUILD_IN_ERROR_CODES } from '../exceptions';
import { AgentToolsResponseDto, ToolResponseDto, AgentToolsNewResponseDto, NewToolResponseDto, ToolTypeEnum } from '../dto';

/**
 * Service xử lý các thao tác liên quan đến tool của agent
 */
@Injectable()
export class AgentToolsService {
  private readonly logger = new Logger(AgentToolsService.name);

  constructor(private readonly dataSource: DataSource) { }

  /**
   * Lấy danh sách tool của agent theo agent_id
   * @param agentId ID của agent
   * @returns Danh sách tool của agent
   */
  async getAgentTools(agentId: string): Promise<AgentToolsResponseDto | AgentToolsNewResponseDto> {
    try {
      // Ki<PERSON>m tra agent có tồn tại không
      const agent = await this.dataSource
        .createQueryBuilder()
        .select(['id', 'name'])
        .from('agents', 'agent')
        .where('agent.id = :agentId', { agentId })
        .getRawOne();

      if (!agent) {
        throw new AppException(
          TOOLS_BUILD_IN_ERROR_CODES.AGENT_NOT_FOUND,
          `Không tìm thấy agent với ID ${agentId}`,
        );
      }

      // Lấy thông tin type_id của agent từ bảng agents_user
      const agentUser = await this.dataSource
        .createQueryBuilder()
        .select(['type_id'])
        .from('agents_user', 'agent_user')
        .where('agent_user.id = :agentId', { agentId })
        .getRawOne();

      if (!agentUser || !agentUser.type_id) {
        throw new AppException(
          TOOLS_BUILD_IN_ERROR_CODES.AGENT_TYPE_NOT_FOUND,
          `Agent không có loại agent`,
        );
      }

      // Group tool đã bị loại bỏ, trả về danh sách rỗng
      const tools: ToolResponseDto[] = [];
      const newFormatTools: NewToolResponseDto[] = [];

      // Kiểm tra xem có yêu cầu định dạng mới không
      const useNewFormat = true; // Có thể thay đổi logic này để xác định khi nào sử dụng định dạng mới

      if (useNewFormat) {
        return {
          tools: newFormatTools
        };
      }

      // Tạo response theo định dạng cũ
      return {
        agentId: agent.id,
        agentName: agent.name,
        tools: tools,
        totalTools: 0,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách tool của agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TOOLS_BUILD_IN_ERROR_CODES.AGENT_TOOLS_FETCH_FAILED,
        `Lỗi khi lấy danh sách tool của agent: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tool của loại agent (đã loại bỏ group tool)
   * @param typeAgentId ID của loại agent
   * @returns Danh sách tool (trống vì đã loại bỏ group tool)
   */
  private async getToolsByTypeAgentId(
    typeAgentId: number,
  ): Promise<ToolResponseDto[]> {
    try {
      // Group tool đã bị loại bỏ, trả về mảng rỗng
      return [];
    } catch (error) {
      this.logger.error(`Error getting tools: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Lấy danh sách tool trong nhóm (deprecated - đã loại bỏ group tool)
   * @param groupId ID của nhóm tool
   * @returns Danh sách tool (trống)
   */
  private async getToolsByGroupId(groupId: number): Promise<ToolResponseDto[]> {
    try {
      // Group tool đã bị loại bỏ, trả về mảng rỗng
      return [];
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách tool trong nhóm: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }



  /**
   * Lấy phiên bản mới nhất của tool
   * @param toolId ID của tool
   * @returns Thông tin phiên bản mới nhất
   */
  private async getLatestToolVersion(toolId: string): Promise<any> {
    try {
      return await this.dataSource
        .createQueryBuilder()
        .select([
          'id',
          'tool_id',
          'version_number',
          'tool_name',
          'tool_description',
          'parameters',
          'status'
        ])
        .from('admin_tool_versions', 'atv')
        .where('atv.tool_id = :toolId', { toolId })
        .andWhere('atv.status != :status', { status: 'DEPRECATED' })
        .orderBy('atv.version_number', 'DESC')
        .limit(1)
        .getRawOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy phiên bản mới nhất của tool: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }



  /**
   * Chuyển đổi danh sách tool sang định dạng mới (đã loại bỏ group tool)
   * @param tools Danh sách tool
   * @returns Danh sách tool theo định dạng mới
   */
  private async convertToNewFormat(tools: ToolResponseDto[]): Promise<NewToolResponseDto[]> {
    const newFormatTools: NewToolResponseDto[] = [];

    // Lặp qua từng tool
    for (const tool of tools) {
      try {
        // Kiểm tra và đảm bảo tool.parameters tồn tại
        const parameters = tool.parameters || {
          type: 'object',
          properties: {},
          required: [],
        };

        // Tạo đối tượng tool mới theo định dạng mới
        let newTool: NewToolResponseDto;

        // Xử lý khác nhau dựa trên loại tool
        if (tool.type === 'USER') {
          // Đối với user tool, sử dụng endpoint và method trực tiếp, không cần headers
          newTool = {
            parameter: {
              name: tool.name,
              description: tool.description,
              inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
            },
            extra: {
              url: tool.endpoint || '',
              method: tool.method || 'GET',
            },
            typeTool: ToolTypeEnum.IN_SYSTEM, // User tool thuộc hệ thống
          };
        } else if (tool.type === 'CUSTOM') {
          // Đối với custom tool, lấy api-key từ dữ liệu nếu có
          const extra: any = {
            url: this.getToolUrl(tool),
            method: this.getToolMethod(tool),
          };

          // Nếu tool có thông tin API key
          if (tool.apiKeyInfo && tool.apiKeyInfo.paramName && tool.apiKeyInfo.apiKey) {
            // Sử dụng thông tin API key từ tool
            const { paramName, apiKey, schemeName } = tool.apiKeyInfo;

            // Nếu có scheme name, sử dụng định dạng "Scheme-Name param-value"
            if (schemeName && schemeName !== 'default') {
              extra.headers = {
                ...(extra.headers || {}),
                [paramName]: `${apiKey}`
              };
              this.logger.debug(`Sử dụng API key với scheme: ${schemeName} ${apiKey}`);
            } else {
              // Nếu không có scheme name hoặc là 'default', chỉ sử dụng giá trị
              extra.headers = {
                ...(extra.headers || {}),
                [paramName]: apiKey
              };
              this.logger.debug(`Sử dụng API key không có scheme: ${apiKey}`);
            }
          }

          newTool = {
            parameter: {
              name: tool.name,
              description: tool.description,
              inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
            },
            extra: extra,
            typeTool: ToolTypeEnum.OUT_SYSTEM, // Custom tool ngoài hệ thống
          };
        } else {
          // Đối với admin tool, không cần headers
          newTool = {
            parameter: {
              name: tool.name,
              description: tool.description,
              inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
            },
            extra: {
              url: this.getToolUrl(tool),
              method: this.getToolMethod(tool),
            },
            typeTool: ToolTypeEnum.IN_SYSTEM, // Admin tool thuộc hệ thống
          };
        }

        newFormatTools.push(newTool);
      } catch (error) {
        this.logger.error(`Lỗi khi chuyển đổi tool ${tool.id} sang định dạng mới: ${error.message}`);
        // Bỏ qua tool này và tiếp tục với tool tiếp theo
        continue;
      }
    }

    return newFormatTools;
  }

  /**
   * Chuyển đổi parameters của tool sang inputSchema
   * @param parameters Parameters của tool
   * @param toolType Loại tool (không sử dụng trong phiên bản hiện tại)
   * @returns InputSchema
   */
  private convertParametersToInputSchema(parameters: any, _toolType: string): any {
    if (!parameters) {
      return {
        type: 'object',
        properties: {},
        required: [],
      };
    }

    // Tạo cấu trúc inputSchema mới
    const inputSchema = {
      type: 'object',
      required: parameters.required || [],
      properties: {
        query_param: {
          type: 'object',
          description: 'Tham số truy vấn',
          properties: {},
        },
        path_param: {
          type: 'object',
          description: 'Tham số đường dẫn',
          properties: {},
        },
        body: {
          type: 'object',
          description: 'Tham số body',
          properties: {},
        },
      },
      description: parameters.description || 'Tham số đầu vào cho công cụ',
    };

    // Xử lý các tham số theo cấu trúc mới
    if (parameters.properties) {
      // Xử lý query_param
      if (parameters.properties.query_param) {
        inputSchema.properties.query_param.properties = this.processParameterProperties(parameters.properties.query_param);
      }

      // Xử lý path_param
      if (parameters.properties.path_param) {
        inputSchema.properties.path_param.properties = this.processParameterProperties(parameters.properties.path_param);
      }

      // Xử lý body
      if (parameters.properties.body) {
        inputSchema.properties.body.properties = this.processParameterProperties(parameters.properties.body);
      }
    }

    return inputSchema;
  }

  /**
   * Xử lý các thuộc tính của tham số
   * @param paramProperties Thuộc tính của tham số
   * @returns Thuộc tính đã xử lý
   */
  private processParameterProperties(paramProperties: any): any {
    const result = {};

    if (!paramProperties) return result;

    // Nếu paramProperties là một đối tượng có thuộc tính properties
    if (paramProperties.properties) {
      return paramProperties.properties;
    }

    // Nếu paramProperties là một đối tượng thuộc tính trực tiếp
    Object.keys(paramProperties).forEach(key => {
      const property = paramProperties[key];

      result[key] = {
        type: property.type || 'string',
        description: property.description || '',
      };

      // Nếu là mảng, thêm thuộc tính items
      if (property.type === 'array' && property.items) {
        result[key].items = property.items;
      }

      // Thêm các thuộc tính khác nếu có
      if (property.example) {
        result[key].example = property.example;
      }
    });

    return result;
  }

  /**
   * Lấy URL đầy đủ của tool
   * @param tool Thông tin tool
   * @returns URL đầy đủ
   */
  private getToolUrl(tool: ToolResponseDto): string {
    // Nếu là tool tích hợp, sử dụng thông tin tích hợp
    if (tool.integration) {
      return `${tool.integration.baseUrl}${tool.integration.endpoint}`;
    }

    // Nếu là user tool với endpoint
    if (tool.type === 'USER' && tool.endpoint) {
      return tool.endpoint;
    }

    // Mặc định sử dụng URL cố định
    return `http://localhost:3003/api/v1/agent`;
  }

  /**
   * Lấy phương thức HTTP của tool
   * @param tool Thông tin tool
   * @returns Phương thức HTTP
   */
  private getToolMethod(tool: ToolResponseDto): string {
    // Nếu là tool tích hợp, sử dụng thông tin tích hợp
    if (tool.integration) {
      return tool.integration.method;
    }

    // Nếu là user tool với method
    if (tool.type === 'USER' && tool.method) {
      return tool.method;
    }

    // Mặc định sử dụng GET
    return 'GET';
  }

  /**
   * Trích xuất các tham số path từ URL
   * @param url URL cần phân tích
   * @returns Danh sách các tham số path
   */
  private extractPathParamsFromUrl(url: string): string[] {
    const pathParams: string[] = [];
    const regex = /{([^}]+)}/g;
    let match: RegExpExecArray | null;

    while ((match = regex.exec(url)) !== null) {
      pathParams.push(match[1]);
    }

    return pathParams;
  }
}
