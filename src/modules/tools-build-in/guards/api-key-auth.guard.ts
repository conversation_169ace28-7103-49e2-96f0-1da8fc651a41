import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { AppException } from '@common/exceptions';
import { TOOLS_BUILD_IN_API_KEY_ERROR_CODES } from '../exceptions';
import { API_KEY_AUTH_KEY } from '../decorators';
import { ApiKeyHeaderEnum } from '../constants';
import { ApiKeyUtil } from '../utils';
import { DataSource } from 'typeorm';

/**
 * Guard xác thực API Key
 * Kiểm tra API Key trong header của request
 */
@Injectable()
export class ApiKeyAuthGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyAuthGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly apiKeyUtil: Api<PERSON><PERSON><PERSON>til,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> tra xem request có thể tiếp tục hay không
   * @param context Context của request
   * @returns true nếu request có thể tiếp tục, false nếu không
   */
  async canActivate(
    context: ExecutionContext,
  ): Promise<boolean> {
    // Kiểm tra xem endpoint có yêu cầu API Key không
    const isApiKeyRequired = this.reflector.getAllAndOverride<boolean>(
      API_KEY_AUTH_KEY,
      [context.getHandler(), context.getClass()],
    );

    // Nếu endpoint không yêu cầu API Key, cho phép request tiếp tục
    if (!isApiKeyRequired) {
      return true;
    }

    // Lấy request từ context
    const request = context.switchToHttp().getRequest<Request>();

    // Lấy API Key từ header
    const apiKey = this.extractApiKey(request);

    this.logger.warn(`API Key: ${apiKey}`);

    // Kiểm tra xem API Key có được cung cấp không
    if (!apiKey) {
      throw new AppException(
        TOOLS_BUILD_IN_API_KEY_ERROR_CODES.API_KEY_MISSING,
        'API Key không được cung cấp trong header',
      );
    }

    // Giải mã API Key để lấy Agent ID và User ID
    const decodedKey = this.apiKeyUtil.decodeApiKey(apiKey);
    if (!decodedKey) {
      this.logger.warn(`API Key không hợp lệ hoặc không thể giải mã: ${this.maskApiKey(apiKey)}`);
      throw new AppException(
        TOOLS_BUILD_IN_API_KEY_ERROR_CODES.API_KEY_INVALID,
        'API Key không hợp lệ hoặc không thể giải mã',
      );
    }

    // Kiểm tra xem Agent có tồn tại không
    const agent = await this.checkAgentExists(decodedKey.agentId);
    if (!agent) {
      this.logger.warn(`Agent không tồn tại: ${decodedKey.agentId}`);
      throw new AppException(
        TOOLS_BUILD_IN_API_KEY_ERROR_CODES.AGENT_NOT_FOUND,
        'Agent không tồn tại',
      );
    }

    // Kiểm tra xem Agent còn hoạt động không
    if (!agent.isActive) {
      this.logger.warn(`Agent không hoạt động: ${decodedKey.agentId}`);
      throw new AppException(
        TOOLS_BUILD_IN_API_KEY_ERROR_CODES.AGENT_INACTIVE,
        'Agent không hoạt động',
      );
    }

    // Lưu thông tin Agent và User vào request để sử dụng sau này
    request['agent'] = {
      id: decodedKey.agentId,
      userId: decodedKey.userId,
    };

    // API Key hợp lệ, cho phép request tiếp tục
    return true;
  }

  /**
   * Trích xuất API Key từ header của request
   * @param request Request
   * @returns API Key hoặc undefined nếu không tìm thấy
   */
  private extractApiKey(request: Request): string | undefined {
    return request.headers[ApiKeyHeaderEnum.API_KEY.toLowerCase()] as string;
  }

  /**
   * Kiểm tra xem Agent có tồn tại không và có hoạt động không
   * @param agentId ID của Agent
   * @returns Thông tin Agent nếu tồn tại, null nếu không tồn tại
   */
  private async checkAgentExists(agentId: string): Promise<{ id: string; isActive: boolean } | null> {
    try {
      // Kiểm tra xem Agent có tồn tại không
      const agent = await this.dataSource
        .createQueryBuilder()
        .select(['id', 'status'])
        .from('agents', 'agent')
        .where('agent.id = :agentId', { agentId })
        .andWhere('agent.deleted_at IS NULL')
        .getRawOne();

      if (!agent) {
        return null;
      }

      // Kiểm tra xem Agent có hoạt động không
      const isActive = agent.status !== 'INACTIVE' && agent.status !== 'DELETED';

      return {
        id: agent.id,
        isActive,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra Agent: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Che giấu API Key để log an toàn
   * @param apiKey API Key cần che giấu
   * @returns API Key đã che giấu
   */
  private maskApiKey(apiKey: string): string {
    if (!apiKey || apiKey.length < 8) {
      return '********';
    }

    // Chỉ hiển thị 4 ký tự cuối của API Key
    return '********' + apiKey.slice(-4);
  }
}
