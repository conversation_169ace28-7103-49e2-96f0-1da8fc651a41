import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin địa chỉ
 */
export class AddressResponseDto {
  @ApiProperty({
    description: 'ID của địa chỉ',
    example: 1,
    nullable: true
  })
  id?: number;

  @ApiProperty({
    description: 'Tên người nhận/liên hệ',
    example: 'Nguyễn Văn A'
  })
  contactName: string;

  @ApiProperty({
    description: 'Số điện thoại liên hệ',
    example: '0987654321'
  })
  phone: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: 'Số 123, Đường ABC'
  })
  addressLine: string;

  @ApiProperty({
    description: 'Phường/Xã',
    example: 'Phường XYZ',
    nullable: true
  })
  ward?: string;

  @ApiProperty({
    description: 'Quận/Huyện',
    example: 'Quận 1'
  })
  district: string;

  @ApiProperty({
    description: 'Tỉnh/Thành phố',
    example: 'TP. Hồ Chí <PERSON>'
  })
  province: string;

  @ApiProperty({
    description: 'Mã bưu chính',
    example: '700000',
    nullable: true
  })
  postalCode?: string;

  @ApiProperty({
    description: 'Loại địa chỉ',
    example: 'home',
    nullable: true
  })
  addressType?: string;

  @ApiProperty({
    description: 'Ghi chú thêm',
    example: 'Giao hàng giờ hành chính',
    nullable: true
  })
  notes?: string;

  @ApiProperty({
    description: 'Có phải địa chỉ mặc định không',
    example: true,
    nullable: true
  })
  isDefault?: boolean;
}

/**
 * DTO cho việc trả về thông tin vận chuyển
 */
export class ShippingResponseDto {
  @ApiProperty({
    description: 'Địa chỉ lấy hàng',
    type: AddressResponseDto
  })
  pickupAddress: AddressResponseDto;

  @ApiProperty({
    description: 'Địa chỉ giao hàng',
    type: AddressResponseDto
  })
  deliveryAddress: AddressResponseDto;

  @ApiProperty({
    description: 'Phương thức vận chuyển',
    example: 'GHTK'
  })
  shippingMethod: string;

  @ApiProperty({
    description: 'Phí vận chuyển',
    example: 25000
  })
  shippingFee: number;

  @ApiProperty({
    description: 'Thời gian dự kiến giao hàng',
    example: '2023-12-31T12:00:00Z',
    nullable: true
  })
  estimatedDeliveryTime?: string;

  @ApiProperty({
    description: 'Mã đơn vận chuyển',
    example: 'GHTK123456789',
    nullable: true
  })
  trackingCode?: string;
}

/**
 * DTO cho việc trả về kết quả tìm kiếm địa chỉ
 */
export class AddressSearchResponseDto {
  @ApiProperty({
    description: 'Danh sách các địa chỉ tìm thấy',
    type: [AddressResponseDto]
  })
  addresses: AddressResponseDto[];

  @ApiProperty({
    description: 'Tổng số kết quả',
    example: 10
  })
  total: number;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'Hồ Chí Minh'
  })
  keyword: string;
}
