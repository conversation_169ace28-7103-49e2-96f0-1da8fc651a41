import { ApiProperty } from '@nestjs/swagger';
import { PriceTypeEnum, UserProductStatusEnum } from '../constants';

/**
 * DTO cho việc trả về thông tin sản phẩm
 */
export class ProductResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 1,
    nullable: true
  })
  id?: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam'
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%',
    nullable: true
  })
  description?: string;

  @ApiProperty({
    description: 'Giá sản phẩm',
    example: {
      amount: 150000,
      currency: 'VND',
      discount: 10,
      finalAmount: 135000
    }
  })
  price: Record<string, any>;

  @ApiProperty({
    description: 'Kiểu giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE
  })
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Hình ảnh sản phẩm',
    example: {
      main: 'https://example.com/image1.jpg',
      gallery: ['https://example.com/image2.jpg', 'https://example.com/image3.jpg']
    },
    nullable: true
  })
  images?: Record<string, any>;

  @ApiProperty({
    description: 'Các tag của sản phẩm',
    example: {
      categories: ['thời trang', 'nam'],
      colors: ['đen', 'trắng', 'xanh']
    },
    nullable: true
  })
  tags?: Record<string, any>;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    nullable: true
  })
  shipmentConfig?: {
    widthCm: number;
    heightCm: number;
    lengthCm: number;
    weightGram: number;
  };

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: UserProductStatusEnum,
    example: UserProductStatusEnum.PUBLISHED,
    nullable: true
  })
  status?: UserProductStatusEnum;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1672531200000,
    nullable: true
  })
  createdAt?: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1672531200000,
    nullable: true
  })
  updatedAt?: number;
}

/**
 * DTO cho việc trả về thông tin danh mục sản phẩm
 */
export class ProductCategoryResponseDto {
  @ApiProperty({
    description: 'ID của danh mục',
    example: 1,
    nullable: true
  })
  id?: number;

  @ApiProperty({
    description: 'Tên danh mục',
    example: 'Thời trang nam'
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả danh mục',
    example: 'Các sản phẩm thời trang dành cho nam giới',
    nullable: true
  })
  description?: string;

  @ApiProperty({
    description: 'Danh mục cha',
    example: 2,
    nullable: true
  })
  parentId?: number;

  @ApiProperty({
    description: 'Thứ tự hiển thị',
    example: 1,
    nullable: true
  })
  displayOrder?: number;
}

/**
 * DTO cho việc trả về kết quả tìm kiếm sản phẩm
 */
export class ProductSearchResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm tìm thấy',
    type: [ProductResponseDto]
  })
  products: ProductResponseDto[];

  @ApiProperty({
    description: 'Tổng số kết quả',
    example: 50
  })
  total: number;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'áo thun',
    nullable: true
  })
  keyword?: string;

  @ApiProperty({
    description: 'Bộ lọc đã áp dụng',
    example: {
      priceRange: { min: 100000, max: 500000 },
      categories: [1, 2, 3]
    },
    nullable: true
  })
  filters?: Record<string, any>;
}
