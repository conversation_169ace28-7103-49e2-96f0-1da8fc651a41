import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin website
 */
export class WebsiteInfoResponseDto {
  @ApiProperty({
    description: 'URL của website',
    example: 'https://example.com'
  })
  url: string;

  @ApiProperty({
    description: 'Tiêu đề của website',
    example: 'Example Website',
    nullable: true
  })
  title?: string;

  @ApiProperty({
    description: 'Mô tả của website',
    example: 'This is an example website',
    nullable: true
  })
  description?: string;

  @ApiProperty({
    description: 'URL favicon của website',
    example: 'https://example.com/favicon.ico',
    nullable: true
  })
  favicon?: string;
}

/**
 * DTO cho việc trả về kết quả phân tích website
 */
export class WebsiteAnalysisResponseDto {
  @ApiProperty({
    description: 'Thông tin cơ bản của website',
    type: WebsiteInfoResponseDto
  })
  info: WebsiteInfoResponseDto;

  @ApiProperty({
    description: 'Các meta tag của website',
    example: {
      'og:title': 'Example Website',
      'og:description': 'This is an example website'
    },
    nullable: true
  })
  metaTags?: Record<string, string>;

  @ApiProperty({
    description: 'Các script được sử dụng trên website',
    example: ['jQuery', 'Bootstrap', 'Google Analytics'],
    nullable: true
  })
  scripts?: string[];

  @ApiProperty({
    description: 'Các công nghệ được phát hiện trên website',
    example: ['React', 'Node.js', 'MongoDB'],
    nullable: true
  })
  technologies?: string[];
}

/**
 * DTO cho việc trả về thông tin SEO của website
 */
export class WebsiteSeoResponseDto {
  @ApiProperty({
    description: 'Tiêu đề SEO',
    example: 'Example Website - Best Example on the Web'
  })
  title: string;

  @ApiProperty({
    description: 'Mô tả SEO',
    example: 'This is the best example website on the web with amazing features'
  })
  description: string;

  @ApiProperty({
    description: 'Các từ khóa SEO',
    example: ['example', 'website', 'best']
  })
  keywords: string[];

  @ApiProperty({
    description: 'Điểm SEO tổng thể (0-100)',
    example: 85
  })
  score: number;

  @ApiProperty({
    description: 'Các vấn đề SEO được phát hiện',
    example: ['Missing alt tags on images', 'Slow page load time'],
    nullable: true
  })
  issues?: string[];
}
