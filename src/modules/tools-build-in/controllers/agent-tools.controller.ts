import { ApiResponseDto } from '@common/response';
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { ApiKeyAuth, CurrentAgent } from '../decorators';
import { ApiKeyAuthGuard } from '../guards';
import { AgentToolsResponseDto, AgentToolsNewResponseDto } from '../dto';
import { AgentToolsService } from '../services';

/**
 * Controller xử lý các API liên quan đến tool của agent
 */
@ApiTags('Agent Tools')
@Controller('agent-tools')
@UseGuards(ApiKeyAuthGuard)
@ApiKeyAuth()
@ApiSecurity('api-key')
export class AgentToolsController {
  constructor(private readonly agentToolsService: AgentToolsService) { }

  /**
   * L<PERSON>y danh sách tool của agent
   * @returns Danh sách tool của agent
   */
  @Get()
  @ApiOperation({ summary: 'L<PERSON>y danh sách tool của agent' })
  @ApiResponse({
    status: 200,
    description: 'L<PERSON>y danh sách tool thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy agent',
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  async getAgentTools(@CurrentAgent('id') agentId: string) {
    console.log('Agent ID: ', agentId);

    const result = await this.agentToolsService.getAgentTools(agentId);
    return ApiResponseDto.success(
      result,
      'Lấy danh sách tool của agent thành công',
    );
  }
}
