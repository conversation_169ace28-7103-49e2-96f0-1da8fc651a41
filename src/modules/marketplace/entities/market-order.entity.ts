import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng market_order trong cơ sở dữ liệu
 * Đơn hàng trong chợ
 */
@Entity('market_order')
export class MarketOrder {
  /**
   * Mã định danh đơn hàng
   */
  @PrimaryGeneratedColumn('identity', { name: 'id' })
  id: number;

  /**
   * Mã người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tổng số R-Point thanh toán
   */
  @Column({ name: 'total_point', type: 'bigint' })
  totalPoint: number;

  /**
   * Thời gian tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Các dòng chi tiết đơn hàng
   * Thuộc tính này không được lưu trong cơ sở dữ liệu
   * Được sử dụng để lưu trữ kết quả join từ MarketOrderLine
   */
  orderLines?: any[];
}
