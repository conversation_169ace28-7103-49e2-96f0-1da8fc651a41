import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { SwaggerApiTag } from '@common/swagger/swagger.tags';
import { CartUserService } from '../services/cart-user.service';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { AddToCartDto, CartResponseDto, QueryCartDto, UpdateCartItemDto, RemoveMultipleCartItemsDto } from '../dto';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';

@ApiTags(SwaggerApiTag.USER_MARKETPLACE_CART)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@ApiExtraModels(ApiResponseDto, CartResponseDto, AddToCartDto, UpdateCartItemDto, RemoveMultipleCartItemsDto)
@Controller('user/marketplace/cart')
export class CartUserController {
  constructor(private readonly cartUserService: CartUserService) {}

  /**
   * Lấy thông tin giỏ hàng của người dùng hiện tại
   * @param user Thông tin người dùng từ JWT
   * @param queryDto Tham số truy vấn
   * @returns Thông tin giỏ hàng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy thông tin giỏ hàng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin giỏ hàng thành công',
    schema: ApiResponseDto.getSchema(CartResponseDto)
  })
  // Sử dụng decorator tùy chỉnh để hiển thị lỗi trong Swagger
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.CART_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.CART_RETRIEVAL_FAILED
  )
  async getCart(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryCartDto
  ): Promise<ApiResponseDto<CartResponseDto>> {
    const cart = await this.cartUserService.getCart(user.id, queryDto);
    return ApiResponseDto.success(cart, 'Lấy thông tin giỏ hàng thành công');
  }

  /**
   * Thêm sản phẩm vào giỏ hàng
   * @param user Thông tin người dùng từ JWT
   * @param addToCartDto Thông tin sản phẩm cần thêm
   * @returns Thông tin giỏ hàng sau khi thêm
   */
  @Post()
  @ApiOperation({ summary: 'Thêm sản phẩm vào giỏ hàng' })
  @ApiBody({ type: AddToCartDto })
  @ApiResponse({
    status: 200,
    description: 'Thêm sản phẩm vào giỏ hàng thành công',
    schema: ApiResponseDto.getSchema(CartResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_APPROVED,
    MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED
  )
  async addToCart(
    @CurrentUser() user: JwtPayload,
    @Body() addToCartDto: AddToCartDto
  ): Promise<ApiResponseDto<CartResponseDto>> {
    const cart = await this.cartUserService.addToCart(user.id, addToCartDto);
    return ApiResponseDto.success(cart, 'Thêm sản phẩm vào giỏ hàng thành công');
  }

  /**
   * Cập nhật số lượng sản phẩm trong giỏ hàng
   * @param user Thông tin người dùng từ JWT
   * @param cartItemId ID của cart item
   * @param updateCartItemDto Thông tin cập nhật (chỉ cần truyền số lượng cần thêm, sẽ được cộng vào số lượng hiện có)
   * @returns Thông tin giỏ hàng sau khi cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Thêm số lượng sản phẩm vào giỏ hàng (cộng thêm vào số lượng hiện có)' })
  @ApiParam({ name: 'id', description: 'ID của cart item', type: 'number' })
  @ApiBody({ type: UpdateCartItemDto })
  @ApiResponse({
    status: 200,
    description: 'Thêm số lượng sản phẩm vào giỏ hàng thành công',
    schema: ApiResponseDto.getSchema(CartResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.CART_ITEM_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async updateCartItem(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) cartItemId: number,
    @Body() updateCartItemDto: UpdateCartItemDto
  ): Promise<ApiResponseDto<CartResponseDto>> {
    const cart = await this.cartUserService.updateCartItem(user.id, cartItemId, updateCartItemDto);
    return ApiResponseDto.success(cart, 'Thêm số lượng sản phẩm vào giỏ hàng thành công');
  }

  /**
   * Xóa nhiều sản phẩm khỏi giỏ hàng
   * @param user Thông tin người dùng từ JWT
   * @param removeMultipleCartItemsDto Danh sách ID của các cart item cần xóa
   * @returns Thông tin giỏ hàng sau khi xóa
   */
  @Delete('batch')
  @ApiOperation({ summary: 'Xóa nhiều sản phẩm khỏi giỏ hàng' })
  @ApiBody({ type: RemoveMultipleCartItemsDto })
  @ApiResponse({
    status: 200,
    description: 'Xóa nhiều sản phẩm khỏi giỏ hàng thành công',
    schema: ApiResponseDto.getSchema(CartResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async removeMultipleCartItems(
    @CurrentUser() user: JwtPayload,
    @Body() removeMultipleCartItemsDto: RemoveMultipleCartItemsDto
  ): Promise<ApiResponseDto<CartResponseDto>> {
    const cart = await this.cartUserService.removeMultipleCartItems(user.id, removeMultipleCartItemsDto.cartItemIds);
    return ApiResponseDto.success(cart, 'Xóa nhiều sản phẩm khỏi giỏ hàng thành công');
  }
}
