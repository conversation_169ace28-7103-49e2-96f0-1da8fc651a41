import { Body, Controller, Delete, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { PaymentService } from '../services/payment.service';
import { PaymentDto, PaymentResponseDto, BulkDeletePaymentDto, BulkDeletePaymentResponseDto } from '../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến thanh toán sản phẩm
 */
@ApiTags(SWAGGER_API_TAGS.USER_MARKETPLACE_PAYMENT)
@Controller('user/marketplace/payment')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  /**
   * Thanh toán sản phẩm
   * @param user Thông tin người dùng từ JWT
   * @param paymentDto Thông tin thanh toán
   * @returns Thông tin thanh toán thành công
   */
  @Post()
  @ApiOperation({
    summary: 'Thanh toán sản phẩm',
    description: 'Thanh toán sản phẩm bằng R-Point'
  })
  @ApiResponse({
    status: 200,
    description: 'Thanh toán thành công',
    type: ApiResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ hoặc không đủ R-Point'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy sản phẩm'
  })
  async processPayment(
    @CurrentUser() user: JwtPayload,
    @Body() paymentDto: PaymentDto
  ): Promise<ApiResponseDto<PaymentResponseDto>> {
    const result = await this.paymentService.processPayment(user.id, paymentDto);
    return ApiResponseDto.success(result, 'Thanh toán thành công');
  }

  /**
   * Xóa nhiều đơn hàng thanh toán
   * @param user Thông tin người dùng từ JWT
   * @param bulkDeleteDto Thông tin các đơn hàng cần xóa
   * @returns Kết quả xóa nhiều đơn hàng
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều đơn hàng thanh toán',
    description: 'Xóa nhiều đơn hàng thanh toán của người dùng'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa đơn hàng thành công',
    type: ApiResponseDto<BulkDeletePaymentResponseDto>
  })
  @ApiResponse({
    status: 207,
    description: 'Một số đơn hàng không thể xóa',
    type: ApiResponseDto<BulkDeletePaymentResponseDto>
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền xóa đơn hàng'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy đơn hàng'
  })
  async bulkDeletePayments(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeletePaymentDto
  ): Promise<ApiResponseDto<BulkDeletePaymentResponseDto>> {
    const result = await this.paymentService.bulkDeletePayments(user.id, bulkDeleteDto);
    return ApiResponseDto.success(result, result.message);
  }
}