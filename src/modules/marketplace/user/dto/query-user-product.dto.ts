import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { ProductStatus } from '@modules/marketplace/enums';
import { QueryProductDto } from './query-product.dto';

/**
 * DTO cho các tham số truy vấn danh sách sản phẩm của người dùng
 * Mở rộng từ QueryProductDto và thêm lọc theo trạng thái
 */
export class QueryUserProductDto extends QueryProductDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductStatus)
  @Type(() => String)
  status?: ProductStatus;
}
