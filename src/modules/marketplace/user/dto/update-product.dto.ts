import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  Min,
  <PERSON><PERSON>ength,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho thông tin cơ bản của sản phẩm khi cập nhật
 */
export class ProductInfoDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: '<PERSON>i<PERSON> niêm yết',
    example: 1200,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discountedPrice: number;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'A ready-to-use chatbot template for customer service',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

/**
 * DTO cho thao tác với ảnh sản phẩm khi cập nhật
 */
export class ImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác',
    enum: ['ADD', 'DELETE'],
    example: 'ADD',
  })
  @IsString()
  operation: 'ADD' | 'DELETE';

  @ApiProperty({
    description: 'Chỉ số ảnh (không bắt buộc cho ADD, hệ thống sẽ tự gán)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  index?: number;

  @ApiProperty({
    description: 'Vị trí ảnh cần xóa (cho DELETE)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  position?: number;

  @ApiProperty({
    description: 'Loại MIME (cho ADD)',
    example: 'image/png',
    required: false,
  })
  @IsOptional()
  @IsString()
  mimeType?: string;

  @ApiProperty({
    description: 'Khóa tệp (cho DELETE, không bắt buộc nếu đã có position)',
    example: 'marketplace/KNOWLEDGEFILE/2025/4/1/12-wqe.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  key?: string;
}

/**
 * Enum cho các tùy chọn khi cập nhật sản phẩm
 */
export enum UpdateProductOption {
  /** Lưu nháp */
  SAVE_DRAFT = 'SAVE_DRAFT',
  /** Gửi duyệt */
  SUBMIT_FOR_APPROVAL = 'SUBMIT_FOR_APPROVAL'
}

/**
 * DTO cho việc cập nhật sản phẩm
 */
export class UpdateProductDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm',
    type: ProductInfoDto,
  })
  @ValidateNested()
  @Type(() => ProductInfoDto)
  @IsOptional()
  productInfo?: ProductInfoDto;

  @ApiProperty({
    description: 'Danh sách thao tác ảnh',
    type: [ImageOperationDto],
  })
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  @IsOptional()
  images?: ImageOperationDto[];

  @ApiProperty({
    description: 'Cờ chỉ định chỉnh sửa chi tiết',
    example: true,
  })
  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  detailEdited?: boolean;

  @ApiProperty({
    description: 'Cờ chỉ định cập nhật hướng dẫn sử dụng',
    example: true,
  })
  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  userManual?: boolean;

  @ApiProperty({
    description: 'Tùy chọn khi cập nhật sản phẩm: lưu nháp hoặc gửi duyệt',
    enum: UpdateProductOption,
    example: UpdateProductOption.SAVE_DRAFT,
    default: UpdateProductOption.SAVE_DRAFT
  })
  @IsOptional()
  @IsEnum(UpdateProductOption)
  @Type(() => String)
  updateOption?: UpdateProductOption;
}
