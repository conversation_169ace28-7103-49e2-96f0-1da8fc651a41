import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu thanh toán sản phẩm
 */
export class PaymentDto {
  @ApiProperty({
    description: 'Danh sách ID sản phẩm cần thanh toán',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1, { message: '<PERSON>ải có ít nhất một sản phẩm để thanh toán' })
  @ArrayUnique({ message: 'Danh sách sản phẩm không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID sản phẩm phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: '<PERSON>h sách sản phẩm không được để trống' })
  productIds: number[];
}

/**
 * DTO cho response thanh toán thành công
 */
export class PaymentResponseDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 123
  })
  orderId: number;

  @ApiProperty({
    description: 'Tổng số R-Point đã thanh toán',
    example: 2500
  })
  totalPoint: number;

  @ApiProperty({
    description: 'Số dư R-Point còn lại sau khi thanh toán',
    example: 7500
  })
  remainingBalance: number;

  @ApiProperty({
    description: 'Thời gian tạo đơn hàng',
    example: 1625097600000
  })
  createdAt: number;
}
