import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product, Cart, CartItem, MarketOrder, MarketOrderLine } from '../entities';
import { ProductRepository, CartRepository, CartItemRepository, MarketOrderRepository, MarketOrderLineRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { RedisService } from '@shared/services/redis.service';
import { CdnService } from '@shared/services/cdn.service';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { User } from '@modules/user/entities';
import { Product<PERSON><PERSON><PERSON>, Validation<PERSON><PERSON>per, <PERSON>t<PERSON>elper, OrderHelper, MediaHelper } from '../helpers';
import { EmployeeRepository } from '@modules/employee/repositories/employee.repository';
import { Employee } from '@modules/employee/entities';
import { SqlHelper } from '@common/helpers/sql.helper';
// Import entities và repositories cho validation
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { Agent } from '@modules/agent/entities';
import { UserDataFineTune, AdminDataFineTune } from '@modules/models/entities';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AgentRepository } from '@modules/agent/repositories';
import { UserDataFineTuneRepository, AdminDataFineTuneRepository } from '@modules/models/repositories';
import {
  ProductAdminController,
  OrderAdminController,
  CartAdminController
} from './controllers';
import {
  ProductAdminService,
  OrderAdminService,
  CartAdminService
} from './services';

/**
 * Module quản lý chức năng marketplace cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      Cart,
      CartItem,
      MarketOrder,
      MarketOrderLine,
      User,
      Employee,
      KnowledgeFile,
      Agent,
      UserDataFineTune,
      AdminDataFineTune,
    ]),
  ],
  controllers: [
    ProductAdminController,
    OrderAdminController,
    CartAdminController
  ],
  providers: [
    // Services
    ProductAdminService,
    OrderAdminService,
    CartAdminService,

    // Repositories
    ProductRepository,
    CartRepository,
    CartItemRepository,
    MarketOrderRepository,
    MarketOrderLineRepository,
    UserRepository,
    EmployeeRepository,
    KnowledgeFileRepository,
    AgentRepository,
    UserDataFineTuneRepository,
    AdminDataFineTuneRepository,

    // Shared services
    S3Service,
    RedisService,
    CdnService,
    SqlHelper,

    // Helpers
    ProductHelper,
    CartHelper,
    OrderHelper,
    ValidationHelper,
    MediaHelper
  ],
  exports: [
    ProductAdminService,
    OrderAdminService,
    CartAdminService
  ],
})
export class MarketplaceAdminModule {}
