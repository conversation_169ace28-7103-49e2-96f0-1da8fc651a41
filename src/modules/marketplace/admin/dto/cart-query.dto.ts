import { QueryDto, SortDirection } from '@/common/dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * Enum cho các trường sắp xếp giỏ hàng
 */
export enum CartSortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  TOTAL_VALUE = 'totalValue',
  NAME = 'name',
  PRICE = 'price',
}

/**
 * DTO cho các tham số truy vấn danh sách giỏ hàng
 */
export class CartQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo loại sản phẩm',
    example: 'AGENT',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo ID người dùng',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: CartSortField,
    default: CartSortField.UPDATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CartSortField)
  sortBy?: CartSortField = CartSortField.UPDATED_AT;

  constructor() {
    super();
    this.sortBy = CartSortField.UPDATED_AT;
    this.sortDirection = SortDirection.DESC;
  }
}
