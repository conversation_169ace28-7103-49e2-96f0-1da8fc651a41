import { QueryDto } from "@common/dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsArray, IsBoolean, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OwnerType } from '../../constants/owner-type.enum';

export class FindAllUrlAdminDto extends QueryDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm URL, tìm kiếm theo title, content và url',
    example: 'google',
    required: false,
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: 'Loại URL cần lọc',
    example: 'web',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Các thẻ cần lọc',
    example: ['nestjs', 'tutorial'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    // Nếu là string, chuyển đổi thành mảng
    if (typeof value === 'string') {
      return value.split(',').map(tag => tag.trim());
    }
    // Nếu đã là mảng, giữ nguyên
    return value;
  })
  tags?: string[];

  @ApiProperty({
    description: 'Lọc theo ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiProperty({
    description: 'Loại người sở hữu URL (ADMIN hoặc USER)',
    example: OwnerType.USER,
    enum: OwnerType,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnerType)
  ownedByEnum?: OwnerType;
}
