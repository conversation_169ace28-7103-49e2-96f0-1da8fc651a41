import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho thông tin cấu hình hệ thống
 */
export class SystemConfigurationDto {
  @ApiProperty({
    description: 'ID của cấu hình',
    example: 1
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB',
    required: false
  })
  @IsOptional()
  @IsString()
  bankCode?: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********',
    required: false
  })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({
    description: 'Tên tài khoản',
    example: 'NGUYEN VAN A',
    required: false
  })
  @IsOptional()
  @IsString()
  accountName?: string;

  @ApiProperty({
    description: '<PERSON><PERSON>ch hoạt hay không',
    example: true,
    default: true
  })
  @IsBoolean()
  active: boolean;

  @ApiProperty({
    description: 'Phần trăm phí sàn',
    example: 2.5,
    required: false
  })
  @IsOptional()
  @IsNumber()
  feePercentage?: number;

  @ApiProperty({
    description: 'Link mẫu hóa đơn đầu vào',
    example: 'https://example.com/invoice-template.pdf',
    required: false
  })
  @IsOptional()
  @IsString()
  purchaseInvoiceTemplate?: string;
}

/**
 * DTO cho việc cập nhật cấu hình hệ thống
 */
export class UpdateSystemConfigurationDto {
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB',
    required: false
  })
  @IsOptional()
  @IsString()
  bankCode?: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********',
    required: false
  })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({
    description: 'Tên tài khoản',
    example: 'NGUYEN VAN A',
    required: false
  })
  @IsOptional()
  @IsString()
  accountName?: string;

  @ApiProperty({
    description: 'Kích hoạt hay không',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiProperty({
    description: 'Phần trăm phí sàn',
    example: 2.5,
    required: false
  })
  @IsOptional()
  @IsNumber()
  feePercentage?: number;

  @ApiProperty({
    description: 'Link mẫu hóa đơn đầu vào',
    example: 'https://example.com/invoice-template.pdf',
    required: false
  })
  @IsOptional()
  @IsString()
  purchaseInvoiceTemplate?: string;
}
