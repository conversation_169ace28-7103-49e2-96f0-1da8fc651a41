import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfiguration } from './entities/system-configuration.entity';
import { SystemConfigurationService } from './admin/service/system-configuration.service';
import { RedisService } from '@/shared/services/redis.service';
import { ContractTemplateService } from './services/contract-template.service';
import { S3Service } from '@/shared/services/s3.service';
import { SystemConfigurationRepository } from './repositories/system-configuration.repository';

@Module({
    imports: [
        TypeOrmModule.forFeature([SystemConfiguration]),
    ],
    providers: [
        SystemConfigurationRepository, // Thêm repository vào providers
        SystemConfigurationService,
        RedisService,
        ContractTemplateService,
        S3Service
    ],
    exports: [
        SystemConfigurationService,
        ContractTemplateService,
        SystemConfigurationRepository // Export repository để các module kh<PERSON><PERSON> có thể sử dụng
    ],
})
export class SystemConfigurationModule {}
