import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfiguration } from '../entities/system-configuration.entity';
import { SystemConfigurationRepository } from '../repositories';
import { SystemConfigurationService } from './service';
import { SystemConfigurationAdminController } from './controller/system-configuration-admin.controller';

/**
 * Module quản lý các chức năng admin của module system-configuration
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([SystemConfiguration]),
  ],
  controllers: [
    SystemConfigurationAdminController,
  ],
  providers: [
    SystemConfigurationRepository,
    SystemConfigurationService,
  ],
  exports: [
    SystemConfigurationService,
  ],
})
export class SystemConfigurationAdminModule {}