import { Injectable, Logger } from '@nestjs/common';
import { SystemConfigurationRepository } from '../../repositories';
import { SystemConfiguration } from '../../entities/system-configuration.entity';
import { UpdateSystemConfigurationDto } from '../../dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '@/shared/services/redis.service';

@Injectable()
export class SystemConfigurationService {
  private readonly logger = new Logger(SystemConfigurationService.name);
  private readonly CACHE_KEY = 'system:configuration';
  private readonly CACHE_TTL = 24 * 60 * 60; // 1 ngày tính bằng giây

  constructor(
    private readonly systemConfigurationRepository: SystemConfigurationRepository,
    @InjectRepository(SystemConfiguration)
    private readonly systemConfigRepository: Repository<SystemConfiguration>,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Lấy cấu hình đang active
   * @returns Thông tin cấu hình đang active
   */
  async getActiveConfiguration(): Promise<SystemConfiguration> {
    try {
      const configuration = await this.systemConfigurationRepository.findActive();
      
      if (!configuration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy cấu hình hệ thống đang active'
        );
      }
      
      return configuration;
    } catch (error) {
      this.logger.error(`Error getting active configuration: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống'
      );
    }
  }

  /**
   * Lấy tất cả cấu hình
   * @returns Danh sách cấu hình
   */
  async getAllConfigurations(): Promise<SystemConfiguration[]> {
    try {
      return await this.systemConfigurationRepository.findAll();
    } catch (error) {
      this.logger.error(`Error getting all configurations: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách cấu hình hệ thống'
      );
    }
  }

  /**
   * Lấy cấu hình theo ID
   * @param id ID của cấu hình
   * @returns Thông tin cấu hình
   */
  async getConfigurationById(id: number): Promise<SystemConfiguration> {
    try {
      const configuration = await this.systemConfigurationRepository.findById(id);
      
      if (!configuration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình hệ thống với ID ${id}`
        );
      }
      
      return configuration;
    } catch (error) {
      this.logger.error(`Error getting configuration by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống'
      );
    }
  }

  /**
   * Cập nhật cấu hình
   * @param id ID của cấu hình
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async updateConfiguration(id: number, updateDto: UpdateSystemConfigurationDto): Promise<SystemConfiguration> {
    try {
      const updatedConfiguration = await this.systemConfigurationRepository.update(id, updateDto);
      
      if (!updatedConfiguration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình hệ thống với ID ${id}`
        );
      }

      // Nếu cấu hình vừa cập nhật là active, cập nhật lại cache redis
      if (updatedConfiguration.active) {
        // Lưu cấu hình mới vào cache
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(updatedConfiguration),
          this.CACHE_TTL,
        );
      } else {
        // Nếu không active, xóa cache để lần sau lấy lại từ DB
        await this.redisService.del(this.CACHE_KEY);
      }
      
      return updatedConfiguration;
    } catch (error) {
      this.logger.error(`Error updating configuration: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật cấu hình hệ thống'
      );
    }
  }

  /**
   * Tạo cấu hình mới
   * @param configuration Thông tin cấu hình
   * @returns Cấu hình đã tạo
   */
  async createConfiguration(configuration: Partial<SystemConfiguration>): Promise<SystemConfiguration> {
    try {
      const createdConfig = await this.systemConfigurationRepository.create(configuration);
      // Nếu cấu hình vừa tạo là active, cập nhật cache redis
      if (createdConfig.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(createdConfig),
          this.CACHE_TTL,
        );
      }
      return createdConfig;
    } catch (error) {
      this.logger.error(`Error creating configuration: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo cấu hình hệ thống'
      );
    }
  }

  /**
   * Tạo URL QR thanh toán
   * @param amount Số tiền cần thanh toán
   * @param orderId Mã đơn hàng
   * @returns URL QR thanh toán
   */
  async generateQRPaymentUrl(amount: number, orderId: string): Promise<string> {
    try {
      // Lấy cấu hình đang active
      const configuration = await this.getActiveConfiguration();
      
      if (!configuration.bankCode || !configuration.accountNumber) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu thông tin ngân hàng trong cấu hình hệ thống'
        );
      }
      
      // Tạo URL QR thanh toán theo công thức
      return this.generateQRPayment(
        configuration.bankCode,
        configuration.accountNumber,
        amount,
        orderId
      );
    } catch (error) {
      this.logger.error(`Error generating QR payment URL: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo URL QR thanh toán'
      );
    }
  }

  generateDescription = (orderId: string) => {
    return `REDAI${orderId}SEPAY`;
  }

  /**
   * Tạo URL QR thanh toán theo công thức
   * @param bankCode Mã ngân hàng
   * @param bankAccount Số tài khoản
   * @param amount Số tiền cần thanh toán
   * @param orderId Mã đơn hàng
   * @returns URL QR thanh toán
   */
  private generateQRPayment(
    bankCode: string,
    bankAccount: string,
    amount: number,
    orderId: string
  ): string {
    return `https://qr.sepay.vn/img?bank=${bankCode}&acc=${bankAccount}&template=compact&amount=${amount.toFixed(2)}&des=REDAI${orderId}SEPAY`;
  }

  /**
   * Lấy cấu hình hệ thống từ cache hoặc database
   * @returns Promise<SystemConfiguration> Cấu hình hệ thống
   */
  async getSystemConfiguration(): Promise<SystemConfiguration> {
    try {
      // Thử lấy từ cache trước
      const cachedConfig = await this.redisService.get(this.CACHE_KEY);
      if (cachedConfig) {
        return JSON.parse(cachedConfig);
      }

      // Nếu không có trong cache, lấy từ database
      const config = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!config) {
        throw new Error('Không tìm thấy cấu hình hệ thống');
      }

      // Lưu vào cache
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(config),
        this.CACHE_TTL,
      );

      return config;
    } catch (error) {
      this.logger.error('Lỗi khi lấy cấu hình hệ thống:', error);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình hệ thống
   * @param config Cấu hình mới
   * @returns Promise<SystemConfiguration> Cấu hình đã cập nhật
   */
  async updateSystemConfiguration(config: Partial<SystemConfiguration>): Promise<SystemConfiguration> {
    try {
      // Cập nhật thời gian
      const now = Math.floor(Date.now() / 1000);
      config.updatedAt = now;

      // Cập nhật vào database
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!existingConfig) {
        throw new Error('Không tìm thấy cấu hình hệ thống để cập nhật');
      }

      const updatedConfig = await this.systemConfigRepository.save({
        ...existingConfig,
        ...config,
      });

      // Xóa cache cũ
      await this.redisService.del(this.CACHE_KEY);

      // Lưu cấu hình mới vào cache
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(updatedConfig),
        this.CACHE_TTL,
      );

      return updatedConfig;
    } catch (error) {
      this.logger.error('Lỗi khi cập nhật cấu hình hệ thống:', error);
      throw error;
    }
  }

  /**
   * Xóa cache cấu hình hệ thống
   */
  async clearCache(): Promise<void> {
    try {
      await this.redisService.del(this.CACHE_KEY);
      this.logger.log('Đã xóa cache cấu hình hệ thống');
    } catch (error) {
      this.logger.error('Lỗi khi xóa cache cấu hình hệ thống:', error);
      throw error;
    }
  }
}
