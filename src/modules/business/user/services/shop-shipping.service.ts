import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserShopInfoService } from './user-shop-info.service';
import { AddressValidationService, ShopShippingInfo } from './address-validation.service';

/**
 * Service xử lý thông tin shipping từ shop
 */
@Injectable()
export class ShopShippingService {
  private readonly logger = new Logger(ShopShippingService.name);

  constructor(
    private readonly userShopInfoService: UserShopInfoService,
    private readonly addressValidationService: AddressValidationService
  ) {}

  /**
   * Lấy thông tin shipping từ shop
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Thông tin shipping đầy đủ
   */
  async getShopShippingInfo(shopId: number, userId: number): Promise<ShopShippingInfo> {
    try {
      this.logger.log(`Getting shipping info for shop ${shopId}, user ${userId}`);

      // Lấy thông tin shop
      const shop = await this.userShopInfoService.getShopById(shopId, userId);
      if (!shop) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Shop với ID ${shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // Validate và parse địa chỉ shop
      const fullShopAddress = this.buildFullAddress(shop);
      const parsedAddress = await this.addressValidationService.validateAndParseAddress(fullShopAddress);

      if (!parsedAddress.isValid) {
        this.logger.warn(`Shop address parsing has low confidence: ${parsedAddress.confidence}`, {
          shopId,
          address: fullShopAddress,
          parsed: parsedAddress
        });
      }

      // Build shipping info
      const shippingInfo: ShopShippingInfo = {
        fromDistrictId: parsedAddress.districtId || 1442,
        fromWardCode: parsedAddress.wardCode || '21211',
        fromProvinceName: parsedAddress.province,
        fromDistrictName: parsedAddress.district,
        fromWardName: parsedAddress.ward,
        fromAddress: fullShopAddress,
        fromPhone: shop.shopPhone || '0123456789',
        fromName: shop.shopName || 'Shop'
      };

      this.logger.log(`Shop shipping info resolved:`, shippingInfo);
      return shippingInfo;
    } catch (error) {
      this.logger.error(`Error getting shop shipping info: ${error.message}`);
      throw error;
    }
  }

  /**
   * Build địa chỉ đầy đủ từ thông tin shop
   */
  private buildFullAddress(shop: any): string {
    const addressParts = [
      shop.shopAddress,
      shop.shopWard,
      shop.shopDistrict,
      shop.shopProvince
    ].filter(Boolean);

    return addressParts.join(', ');
  }

  /**
   * Validate thông tin shop có đầy đủ cho shipping không
   */
  async validateShopForShipping(shopId: number, userId: number): Promise<void> {
    try {
      const shop = await this.userShopInfoService.getShopById(shopId, userId);
      
      if (!shop) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Shop với ID ${shopId} không tồn tại`
        );
      }

      const missingFields: string[] = [];

      if (!shop.shopAddress) missingFields.push('địa chỉ');
      if (!shop.shopWard) missingFields.push('phường/xã');
      if (!shop.shopDistrict) missingFields.push('quận/huyện');
      if (!shop.shopProvince) missingFields.push('tỉnh/thành phố');
      if (!shop.shopPhone) missingFields.push('số điện thoại');
      if (!shop.shopName) missingFields.push('tên shop');

      if (missingFields.length > 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Thông tin shop chưa đầy đủ để tạo vận đơn. Thiếu: ${missingFields.join(', ')}. Vui lòng cập nhật thông tin shop trước khi tạo đơn hàng.`
        );
      }

      this.logger.log(`Shop ${shopId} validation passed for shipping`);
    } catch (error) {
      this.logger.error(`Shop validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get thông tin shop cho GHN API
   */
  async getGHNShopInfo(shopId: number, userId: number): Promise<{
    fromDistrictId: number;
    fromWardCode: string;
    fromName: string;
    fromPhone: string;
    fromAddress: string;
    fromWardName: string;
    fromDistrictName: string;
    fromProvinceName: string;
    returnPhone: string;
    returnAddress: string;
    returnDistrictId: number;
    returnWardCode: string;
  }> {
    const shippingInfo = await this.getShopShippingInfo(shopId, userId);
    
    return {
      fromDistrictId: shippingInfo.fromDistrictId,
      fromWardCode: shippingInfo.fromWardCode,
      fromName: shippingInfo.fromName,
      fromPhone: shippingInfo.fromPhone,
      fromAddress: shippingInfo.fromAddress,
      fromWardName: shippingInfo.fromWardName,
      fromDistrictName: shippingInfo.fromDistrictName,
      fromProvinceName: shippingInfo.fromProvinceName,
      // Return info (same as from info)
      returnPhone: shippingInfo.fromPhone,
      returnAddress: shippingInfo.fromAddress,
      returnDistrictId: shippingInfo.fromDistrictId,
      returnWardCode: shippingInfo.fromWardCode
    };
  }

  /**
   * Get thông tin shop cho GHTK API
   */
  async getGHTKShopInfo(shopId: number, userId: number): Promise<{
    pickName: string;
    pickAddress: string;
    pickProvince: string;
    pickDistrict: string;
    pickWard: string;
    pickTel: string;
  }> {
    const shippingInfo = await this.getShopShippingInfo(shopId, userId);
    
    return {
      pickName: shippingInfo.fromName,
      pickAddress: shippingInfo.fromAddress,
      pickProvince: shippingInfo.fromProvinceName,
      pickDistrict: shippingInfo.fromDistrictName,
      pickWard: shippingInfo.fromWardName,
      pickTel: shippingInfo.fromPhone
    };
  }

  /**
   * Cache shop shipping info để tránh query nhiều lần
   */
  private shopInfoCache = new Map<string, { data: ShopShippingInfo; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get cached shop shipping info
   */
  async getCachedShopShippingInfo(shopId: number, userId: number): Promise<ShopShippingInfo> {
    const cacheKey = `${userId}-${shopId}`;
    const cached = this.shopInfoCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
      this.logger.log(`Using cached shop shipping info for ${cacheKey}`);
      return cached.data;
    }

    const shippingInfo = await this.getShopShippingInfo(shopId, userId);
    
    // Cache the result
    this.shopInfoCache.set(cacheKey, {
      data: shippingInfo,
      timestamp: Date.now()
    });

    return shippingInfo;
  }

  /**
   * Clear cache cho shop
   */
  clearShopCache(shopId: number, userId: number): void {
    const cacheKey = `${userId}-${shopId}`;
    this.shopInfoCache.delete(cacheKey);
    this.logger.log(`Cleared cache for shop ${cacheKey}`);
  }

  /**
   * Clear all cache
   */
  clearAllCache(): void {
    this.shopInfoCache.clear();
    this.logger.log('Cleared all shop shipping cache');
  }
}
