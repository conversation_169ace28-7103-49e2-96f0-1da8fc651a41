import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Param,
  Query,
  Body,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { UserOrderAdminService } from '@modules/business/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryUserOrderDto,
  UserOrderResponseDto,
  UserOrderDetailResponseDto,
  UpdateOrderStatusDto,
  UpdateShippingStatusDto,
  AdminCancelOrderDto,
  AdminConfirmOrderDto,
  UpdatePaymentStatusDto,
  BulkUpdateOrderStatusDto,
  OrderStatusUpdateResponseDto,
  BulkOrderUpdateResponseDto,
  OrderAnalyticsQueryDto,
  OrderOverviewStatsDto,
  RevenueReportDto
} from '../dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến đơn hàng của người dùng cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS)
@ApiExtraModels(
  ApiResponseDto,
  UserOrderResponseDto,
  UserOrderDetailResponseDto,
  QueryUserOrderDto,
  UpdateOrderStatusDto,
  UpdateShippingStatusDto,
  AdminCancelOrderDto,
  AdminConfirmOrderDto,
  UpdatePaymentStatusDto,
  BulkUpdateOrderStatusDto,
  OrderStatusUpdateResponseDto,
  BulkOrderUpdateResponseDto,
  OrderAnalyticsQueryDto,
  OrderOverviewStatsDto,
  RevenueReportDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/user-orders')
export class UserOrderAdminController {
  constructor(private readonly userOrderAdminService: UserOrderAdminService) {}

  /**
   * Lấy danh sách đơn hàng với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách đơn hàng phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng',
    description: 'Lấy danh sách đơn hàng với phân trang, tìm kiếm, lọc và sắp xếp',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách đơn hàng',
    schema: ApiResponseDto.getPaginatedSchema(UserOrderResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getAllUserOrders(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryUserOrderDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserOrderResponseDto>>> {
    const result = await this.userOrderAdminService.getUserOrders(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách đơn hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết đơn hàng theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param orderId ID của đơn hàng
   * @returns Thông tin chi tiết đơn hàng
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết đơn hàng theo ID',
    description: 'Lấy thông tin chi tiết của một đơn hàng cụ thể bao gồm thông tin khách hàng',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của đơn hàng',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(UserOrderDetailResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
  )
  async getUserOrderById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) orderId: number,
  ): Promise<ApiResponseDto<UserOrderDetailResponseDto>> {
    const result = await this.userOrderAdminService.getUserOrderById(employeeId, orderId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết đơn hàng thành công');
  }

  // ==================== ORDER MANAGEMENT ENDPOINTS ====================

  /**
   * Cập nhật trạng thái đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Kết quả cập nhật
   */
  @Put('status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật trạng thái đơn hàng',
    description: 'Cập nhật trạng thái đơn hàng (pending → confirmed → processing → completed)',
  })
  @ApiBody({ type: UpdateOrderStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái thành công',
    schema: ApiResponseDto.getSchema(OrderStatusUpdateResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
  )
  async updateOrderStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() updateDto: UpdateOrderStatusDto,
  ): Promise<ApiResponseDto<OrderStatusUpdateResponseDto>> {
    const result = await this.userOrderAdminService.updateOrderStatus(employeeId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái đơn hàng thành công');
  }

  /**
   * Cập nhật trạng thái vận chuyển
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Kết quả cập nhật
   */
  @Put('shipping-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật trạng thái vận chuyển',
    description: 'Cập nhật trạng thái vận chuyển đơn hàng',
  })
  @ApiBody({ type: UpdateShippingStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái vận chuyển thành công',
    schema: ApiResponseDto.getSchema(OrderStatusUpdateResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
  )
  async updateShippingStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() updateDto: UpdateShippingStatusDto,
  ): Promise<ApiResponseDto<OrderStatusUpdateResponseDto>> {
    const result = await this.userOrderAdminService.updateShippingStatus(employeeId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái vận chuyển thành công');
  }

  /**
   * Xác nhận đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param confirmDto DTO chứa thông tin xác nhận
   * @returns Kết quả xác nhận
   */
  @Post('confirm')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xác nhận đơn hàng',
    description: 'Xác nhận đơn hàng từ trạng thái pending sang confirmed',
  })
  @ApiBody({ type: AdminConfirmOrderDto })
  @ApiResponse({
    status: 200,
    description: 'Xác nhận đơn hàng thành công',
    schema: ApiResponseDto.getSchema(OrderStatusUpdateResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
  )
  async confirmOrder(
    @CurrentEmployee('id') employeeId: number,
    @Body() confirmDto: AdminConfirmOrderDto,
  ): Promise<ApiResponseDto<OrderStatusUpdateResponseDto>> {
    const result = await this.userOrderAdminService.confirmOrder(employeeId, confirmDto);
    return ApiResponseDto.success(result, 'Xác nhận đơn hàng thành công');
  }

  /**
   * Hủy đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param cancelDto DTO chứa thông tin hủy đơn
   * @returns Kết quả hủy đơn
   */
  @Post('cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hủy đơn hàng',
    description: 'Hủy đơn hàng với lý do và thông tin hoàn tiền',
  })
  @ApiBody({ type: AdminCancelOrderDto })
  @ApiResponse({
    status: 200,
    description: 'Hủy đơn hàng thành công',
    schema: ApiResponseDto.getSchema(OrderStatusUpdateResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
  )
  async cancelOrder(
    @CurrentEmployee('id') employeeId: number,
    @Body() cancelDto: AdminCancelOrderDto,
  ): Promise<ApiResponseDto<OrderStatusUpdateResponseDto>> {
    const result = await this.userOrderAdminService.cancelOrder(employeeId, cancelDto);
    return ApiResponseDto.success(result, 'Hủy đơn hàng thành công');
  }

  /**
   * Cập nhật trạng thái thanh toán
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateDto DTO chứa thông tin cập nhật thanh toán
   * @returns Kết quả cập nhật
   */
  @Put('payment-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật trạng thái thanh toán',
    description: 'Cập nhật trạng thái thanh toán của đơn hàng',
  })
  @ApiBody({ type: UpdatePaymentStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái thanh toán thành công',
    schema: ApiResponseDto.getSchema(OrderStatusUpdateResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
  )
  async updatePaymentStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() updateDto: UpdatePaymentStatusDto,
  ): Promise<ApiResponseDto<OrderStatusUpdateResponseDto>> {
    const result = await this.userOrderAdminService.updatePaymentStatus(employeeId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái thanh toán thành công');
  }

  /**
   * Cập nhật hàng loạt trạng thái đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param bulkUpdateDto DTO chứa thông tin cập nhật hàng loạt
   * @returns Kết quả cập nhật hàng loạt
   */
  @Put('bulk-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật hàng loạt trạng thái đơn hàng',
    description: 'Cập nhật trạng thái cho nhiều đơn hàng cùng lúc',
  })
  @ApiBody({ type: BulkUpdateOrderStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật hàng loạt thành công',
    schema: ApiResponseDto.getSchema(BulkOrderUpdateResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
  )
  async bulkUpdateOrderStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() bulkUpdateDto: BulkUpdateOrderStatusDto,
  ): Promise<ApiResponseDto<BulkOrderUpdateResponseDto>> {
    const result = await this.userOrderAdminService.bulkUpdateOrderStatus(employeeId, bulkUpdateDto);
    return ApiResponseDto.success(result, 'Cập nhật hàng loạt thành công');
  }

  // ==================== ANALYTICS & REPORTING ENDPOINTS ====================

  /**
   * Lấy thống kê tổng quan đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Thống kê tổng quan
   */
  @Get('analytics/overview')
  @ApiOperation({
    summary: 'Lấy thống kê tổng quan đơn hàng',
    description: 'Lấy thống kê tổng quan về đơn hàng, doanh thu, tỷ lệ hoàn thành',
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê tổng quan đơn hàng',
    schema: ApiResponseDto.getSchema(OrderOverviewStatsDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getOrderOverviewStats(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: OrderAnalyticsQueryDto,
  ): Promise<ApiResponseDto<OrderOverviewStatsDto>> {
    const result = await this.userOrderAdminService.getOrderOverviewStats(employeeId, queryDto);
    return ApiResponseDto.success(result, 'Lấy thống kê tổng quan thành công');
  }

  /**
   * Lấy báo cáo doanh thu
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Báo cáo doanh thu
   */
  @Get('analytics/revenue')
  @ApiOperation({
    summary: 'Lấy báo cáo doanh thu',
    description: 'Lấy báo cáo doanh thu chi tiết với thống kê theo thời gian',
  })
  @ApiResponse({
    status: 200,
    description: 'Báo cáo doanh thu',
    schema: ApiResponseDto.getSchema(RevenueReportDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getRevenueReport(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: OrderAnalyticsQueryDto,
  ): Promise<ApiResponseDto<RevenueReportDto>> {
    const result = await this.userOrderAdminService.getRevenueReport(employeeId, queryDto);
    return ApiResponseDto.success(result, 'Lấy báo cáo doanh thu thành công');
  }
}
