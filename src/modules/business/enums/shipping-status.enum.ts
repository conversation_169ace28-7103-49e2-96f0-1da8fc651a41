/**
 * Enum cho trạng thái giao hàng
 */
export enum ShippingStatusEnum {
  /**
   * Chờ xử lý
   */
  PENDING = 'pending',

  /**
   * <PERSON><PERSON> chuẩn bị
   */
  PREPARING = 'preparing',

  /**
   * Đã giao cho đơn vị vận chuyển
   */
  SHIPPED = 'shipped',

  /**
   * Đang vận chuyển/giao hàng
   */
  IN_TRANSIT = 'in_transit',

  /**
   * Đang phân loại
   */
  SORTING = 'sorting',

  /**
   * Đã giao thành công
   */
  DELIVERED = 'delivered',

  /**
   * Giao hàng thất bại
   */
  DELIVERY_FAILED = 'delivery_failed',

  /**
   * Đang trả lại
   */
  RETURNING = 'returning',

  /**
   * Đã hủy
   */
  CANCELLED = 'cancelled',

  /**
   * Lỗi khi tạo vận đơn - cần xử lý thủ công
   */
  SHIPPING_FAILED = 'shipping_failed'
}

/**
 * <PERSON><PERSON> tả trạng thái vận chuyển bằng tiếng Việt
 */
export const SHIPPING_STATUS_DESCRIPTIONS = {
  [ShippingStatusEnum.PENDING]: 'Chờ xử lý',
  [ShippingStatusEnum.PREPARING]: 'Đang chuẩn bị',
  [ShippingStatusEnum.SHIPPED]: 'Đã giao cho đơn vị vận chuyển',
  [ShippingStatusEnum.IN_TRANSIT]: 'Đang vận chuyển/giao hàng',
  [ShippingStatusEnum.SORTING]: 'Đang phân loại',
  [ShippingStatusEnum.DELIVERED]: 'Đã giao thành công',
  [ShippingStatusEnum.DELIVERY_FAILED]: 'Giao hàng thất bại',
  [ShippingStatusEnum.RETURNING]: 'Đang trả lại',
  [ShippingStatusEnum.CANCELLED]: 'Đã hủy',
  [ShippingStatusEnum.SHIPPING_FAILED]: 'Lỗi tạo vận đơn - cần xử lý thủ công'
};
