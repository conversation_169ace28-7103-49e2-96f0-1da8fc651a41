import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, In, FindManyOptions, Like, FindOperator } from 'typeorm';
import { UserProduct } from '@modules/business/entities';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryUserProductDto } from '@modules/business/admin/dto';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * Repository xử lý truy vấn dữ liệu cho entity UserProduct trong module admin
 */
@Injectable()
export class UserProductAdminRepository extends Repository<UserProduct> {
  protected readonly logger = new Logger(UserProductAdminRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserProduct, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho UserProduct
   * @returns SelectQueryBuilder<UserProduct>
   */
  protected createBaseQuery(): SelectQueryBuilder<UserProduct> {
    return this.createQueryBuilder('userProduct')
      .select([
        'userProduct.id',
        'userProduct.name',
        'userProduct.price',
        'userProduct.typePrice',
        'userProduct.productType',
        'userProduct.description',
        'userProduct.images',
        'userProduct.tags',
        'userProduct.createdBy',
        'userProduct.createdAt',
        'userProduct.updatedAt',
        'userProduct.shipmentConfig',
        'userProduct.status'
      ]);
  }

  /**
   * Tìm kiếm sản phẩm với các tham số lọc
   * @param queryParams Tham số lọc, có thể bao gồm userId để lọc theo người dùng cụ thể
   * @returns Danh sách sản phẩm phân trang
   */
  async findProducts(queryParams: QueryUserProductDto): Promise<PaginatedResult<UserProduct>> {
    this.logger.log(`Tìm kiếm sản phẩm với các tham số: ${JSON.stringify(queryParams)}`);

    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt', // Mặc định sắp xếp theo thời gian tạo
      sortDirection = 'DESC',
      userId,
      status,
    } = queryParams;

    const skip = (page - 1) * limit;

    // Tạo query builder cơ bản
    const query = this.createBaseQuery();
    this.logger.log(`Đã tạo query builder cơ bản`);

    // Thêm điều kiện tìm kiếm
    if (search) {
      this.logger.log(`Thêm điều kiện tìm kiếm với từ khóa: ${search}`);
      query.andWhere('(userProduct.name ILIKE :search OR userProduct.description ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Lọc theo người dùng (sử dụng trường created_by)
    // Thay thế cho API getUserProducts trước đây
    if (userId) {
      this.logger.log(`Lọc theo người dùng: ${userId}`);
      query.andWhere('userProduct.created_by = :userId', { userId });
    }

    // Lọc theo trạng thái sản phẩm
    if (status) {
      this.logger.log(`Lọc theo trạng thái: ${status}`);
      query.andWhere('userProduct.status = :status', { status });
    }

    // Sắp xếp
    this.logger.log(`Sắp xếp theo ${sortBy} ${sortDirection}`);
    // Xử lý trường hợp đặc biệt khi client gửi 'createdAt' hoặc 'createAt'
    let sortColumn = sortBy;

    // Trong database, cột là created_at và được ánh xạ đến thuộc tính createdAt trong entity
    if (sortBy === 'createAt') {
      sortColumn = 'createdAt';
      this.logger.log(`Đã chuyển đổi sortBy từ 'createAt' sang 'createdAt'`);
    }

    this.logger.log(`Áp dụng sắp xếp theo cột ${sortColumn}`);
    query.orderBy(`userProduct.${sortColumn}`, sortDirection as 'ASC' | 'DESC');

    // Phân trang
    this.logger.log(`Phân trang: skip ${skip}, limit ${limit}`);
    query.skip(skip).take(limit);

    // Thực hiện truy vấn
    this.logger.log(`Đang thực hiện truy vấn`);
    const [items, total] = await query.getManyAndCount();
    this.logger.log(`Đã tìm thấy ${items.length}/${total} sản phẩm`);

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm sản phẩm theo ID
   * @param id ID sản phẩm
   * @returns Sản phẩm hoặc null
   */
  async findProductById(id: number): Promise<UserProduct | null> {
    this.logger.log(`Tìm sản phẩm với ID: ${id}`);
    try {
      // Sử dụng findOne với các tùy chọn để đảm bảo lấy đầy đủ dữ liệu
      const product = await this.findOne({
        where: { id },
        select: [
          'id', 'name', 'price', 'typePrice', 'productType', 'description',
          'images', 'tags', 'createdBy', 'createdAt', 'updatedAt',
          'shipmentConfig', 'status'
        ]
      });

      if (!product) {
        this.logger.log(`Không tìm thấy sản phẩm với ID: ${id}`);
        return null;
      }

      this.logger.log(`Đã tìm thấy sản phẩm: ${product.name}`);
      return product;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm với ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // findProductsByUserId method has been removed and integrated into findProducts method with userId filter

  /**
   * Cập nhật trạng thái của nhiều sản phẩm(**NOTE: Admin)
   * @param productIds Danh sách ID sản phẩm
   * @param status Trạng thái mới
   * @returns Số lượng sản phẩm đã cập nhật
   */
  async updateProductsStatus(productIds: number[], status: EntityStatusEnum): Promise<number> {
    this.logger.log(`Cập nhật trạng thái ${status} cho ${productIds.length} sản phẩm`);

    const result = await this.update(
      { id: In(productIds) },
      {
        status: status,
        updatedAt: Date.now()
      }
    );

    this.logger.log(`Đã cập nhật trạng thái cho ${result.affected || 0} sản phẩm`);
    return result.affected || 0;
  }

  /**
   * Kiểm tra trạng thái hiện tại của sản phẩm
   * @param productId ID sản phẩm
   * @returns Trạng thái hiện tại của sản phẩm hoặc null nếu không tìm thấy
   */
  async getProductStatus(productId: number): Promise<EntityStatusEnum | null> {
    this.logger.log(`Kiểm tra trạng thái của sản phẩm ID: ${productId}`);

    const product = await this.findOne({
      where: { id: productId },
      select: ['id', 'status']
    });

    if (!product) {
      this.logger.log(`Không tìm thấy sản phẩm với ID: ${productId}`);
      return null;
    }

    this.logger.log(`Sản phẩm ID ${productId} có trạng thái: ${product.status}`);
    return product.status;
  }
}
