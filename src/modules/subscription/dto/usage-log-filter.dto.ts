import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import {QueryDto} from "@common/dto";

export class UsageLogFilterDto extends QueryDto{
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bắt đầu (Unix timestamp)',
    required: false,
    example: 1632474086123,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  startDate?: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc (Unix timestamp)',
    required: false,
    example: 1635066086123,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endDate?: number;
}
