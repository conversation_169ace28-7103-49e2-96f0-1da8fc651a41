import { ApiProperty } from '@nestjs/swagger';
import { PlanPricing } from '../entities/plan-pricing.entity';

export class PlanPricingResponseDto {
  @ApiProperty({
    description: 'ID của tùy chọn giá',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của gói dịch vụ',
    example: 1,
  })
  planId: number;

  @ApiProperty({
    description: 'Chu kỳ thanh toán',
    example: 'MONTHLY',
  })
  billingCycle: string;

  @ApiProperty({
    description: 'Giá',
    example: 9.99,
  })
  price: number;

  @ApiProperty({
    description: 'Giới hạn sử dụng',
    example: 1000,
  })
  usageLimit: number;

  @ApiProperty({
    description: 'Đơn vị sử dụng',
    example: 'API_CALLS',
  })
  usageUnit: string;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1632474086123,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1632474086123,
  })
  updatedAt: number;

  constructor(planPricing: PlanPricing) {
    this.id = planPricing.id;
    this.planId = planPricing.planId;
    this.billingCycle = planPricing.billingCycle;
    this.price = planPricing.price;
    this.usageLimit = planPricing.usageLimit;
    this.usageUnit = planPricing.usageUnit;
    this.isActive = planPricing.isActive;
    this.createdAt = planPricing.createdAt;
    this.updatedAt = planPricing.updatedAt;
  }
}
