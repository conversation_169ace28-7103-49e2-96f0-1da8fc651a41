import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';

export class SubscriptionCreateDto {
  @ApiProperty({
    description: 'ID của tùy chọn giá',
    example: 1,
  })
  @IsNumber()
  planPricingId: number;

  @ApiProperty({
    description: 'Tự động gia hạn',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean = true;
}
