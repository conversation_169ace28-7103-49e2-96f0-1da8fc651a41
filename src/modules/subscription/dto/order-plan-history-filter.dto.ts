import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for filtering order plan history
 */
export class OrderPlanHistoryFilterDto {
  /**
   * Page number (1-based)
   */
  @ApiProperty({
    description: 'Số trang',
    example: 1,
    required: false,
    default: 1
  })
  @IsInt()
  @Min(1)
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  /**
   * Number of items per page
   */
  @ApiProperty({
    description: 'Số lượng mục trên mỗi trang',
    example: 10,
    required: false,
    default: 10
  })
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10;
}
