import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrderPlanHistory } from '@modules/subscription/entities';
import { OrderPlanHistoryRepository } from '@modules/subscription/repositories';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { QueryDto, SortDirection } from '@common/dto';
import { AdminOrderHistoryFilterDto } from '../dto';
import { OrderStatus } from '@modules/subscription/enums/order-status.enum';

@Injectable()
export class AdminOrderHistoryService {
  private readonly logger = new Logger(AdminOrderHistoryService.name);

  constructor(
    @InjectRepository(OrderPlanHistory)
    private readonly orderHistoryRepository: Repository<OrderPlanHistory>,
    private readonly orderHistoryCustomRepository: OrderPlanHistoryRepository
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách lịch sử đơn hàng với bộ lọc
   * @param filterDto Bộ lọc
   * @returns Danh sách lịch sử đơn hàng đã phân trang
   */
  async findOrderHistories(filterDto: AdminOrderHistoryFilterDto): Promise<PaginatedResult<OrderPlanHistory>> {
    try {
      const { userId, planId, status, startDate, endDate, page = 1, limit = 10, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = filterDto;

      // Tạo tham số phân trang
      const paginationParams: QueryDto = {
        page,
        limit,
        sortBy,
        sortDirection
      };

      // Tạo điều kiện tìm kiếm
      const where: any = {};

      if (userId) {
        where.userId = userId;
      }

      if (planId) {
        where.planId = planId;
      }

      if (status) {
        where.status = status;
      }

      if (startDate && endDate) {
        where.createdAt = { gte: startDate, lte: endDate };
      } else if (startDate) {
        where.createdAt = { gte: startDate };
      } else if (endDate) {
        where.createdAt = { lte: endDate };
      }

      // Lấy danh sách lịch sử đơn hàng
      const result = await this.orderHistoryCustomRepository.findOrderHistories(paginationParams);
      return result;
    } catch (error) {
      this.logger.error(`Error finding order histories: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách lịch sử đơn hàng'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết lịch sử đơn hàng
   * @param id ID của lịch sử đơn hàng
   * @returns Thông tin chi tiết lịch sử đơn hàng
   */
  async findOrderHistoryById(id: number): Promise<OrderPlanHistory> {
    try {
      const orderHistory = await this.orderHistoryRepository.findOne({ where: { id: id.toString() } });

      if (!orderHistory) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy lịch sử đơn hàng với ID ${id}`
        );
      }

      return orderHistory;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding order history by ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin lịch sử đơn hàng'
      );
    }
  }

  /**
   * Lấy thống kê doanh thu
   * @param startDate Thời gian bắt đầu
   * @param endDate Thời gian kết thúc
   * @returns Thống kê doanh thu
   */
  async getRevenueStats(startDate?: number, endDate?: number): Promise<{
    totalRevenue: number;
    totalOrders: number;
    completedOrders: number;
    averageOrderValue: number;
  }> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const start = startDate || now - 30 * 24 * 60 * 60; // Mặc định 30 ngày trước
      const end = endDate || now;

      // Tạo điều kiện tìm kiếm
      const where: any = {
        createdAt: { gte: start, lte: end }
      };

      // Lấy tổng số đơn hàng
      const totalOrders = await this.orderHistoryRepository.count(where);

      // Lấy số đơn hàng đã hoàn thành
      const completedOrders = await this.orderHistoryRepository.count({
        ...where,
        status: OrderStatus.COMPLETED
      });

      // Lấy tổng doanh thu (sử dụng trường point thay vì amount)
      const revenueResult = await this.orderHistoryRepository
        .createQueryBuilder('orderHistory')
        .select('SUM(CAST(orderHistory.point AS numeric))', 'totalRevenue')
        .where('orderHistory.createdAt >= :start', { start })
        .andWhere('orderHistory.createdAt <= :end', { end })
        .andWhere('orderHistory.status = :status', { status: OrderStatus.COMPLETED })
        .getRawOne();

      const totalRevenue = parseFloat(revenueResult.totalRevenue) || 0;

      // Tính giá trị đơn hàng trung bình
      const averageOrderValue = completedOrders > 0 ? totalRevenue / completedOrders : 0;

      return {
        totalRevenue,
        totalOrders,
        completedOrders,
        averageOrderValue
      };
    } catch (error) {
      this.logger.error(`Error getting revenue stats: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thống kê doanh thu'
      );
    }
  }
}
