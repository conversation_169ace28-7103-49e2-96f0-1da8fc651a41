import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { BillingCycle } from '@modules/subscription/enums';
import { QueryDto } from '@common/dto';

export class AdminPlanPricingFilterDto extends QueryDto {
  @ApiProperty({
    description: 'ID của gói dịch vụ',
    required: false,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  planId?: number;

  @ApiProperty({
    description: 'Chu kỳ thanh toán',
    enum: BillingCycle,
    required: false,
    example: BillingCycle.MONTHLY
  })
  @IsOptional()
  @IsEnum(BillingCycle)
  billingCycle?: BillingCycle;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    required: false,
    example: true
  })
  @IsOptional()
  @Type(() => Boolean)
  isActive?: boolean;
}
