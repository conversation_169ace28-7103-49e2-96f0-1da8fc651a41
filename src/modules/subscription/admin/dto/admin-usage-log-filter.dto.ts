import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';

export class AdminUsageLogFilterDto extends QueryDto {
  @ApiProperty({
    description: 'ID của đăng ký',
    required: false,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  subscriptionId?: number;

  @ApiProperty({
    description: 'Tính năng sử dụng',
    required: false,
    example: 'API_CALL'
  })
  @IsOptional()
  @IsString()
  feature?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    required: false,
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  startDate?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp)',
    required: false,
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  endDate?: number;
}
