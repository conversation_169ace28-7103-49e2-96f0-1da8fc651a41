import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as services from './services';
import * as controllers from './controllers';
import { UserRole } from '@modules/user/entities';

/**
 * Module quản lý subscription cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities), UserRole]),
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
  ],
  exports: [
    ...Object.values(services),
  ],
})
export class SubscriptionAdminModule {}
