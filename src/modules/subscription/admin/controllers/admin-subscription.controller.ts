import { Body, Controller, Get, Param, ParseIntPipe, Patch, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AdminSubscriptionService } from '../services';
import { AdminSubscriptionFilterDto, AdminUsageLogFilterDto, UpdateSubscriptionStatusDto } from '../dto';
import { Subscription, UsageLog } from '@modules/subscription/entities';

@ApiTags(SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/subscription/subscriptions')
export class AdminSubscriptionController {
  constructor(private readonly adminSubscriptionService: AdminSubscriptionService) {}

  /**
   * Lấy danh sách đăng ký
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách đăng ký' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách đăng ký thành công'
  })
  async findSubscriptions(
    @Query() filterDto: AdminSubscriptionFilterDto
  ): Promise<ApiResponseDto<PaginatedResult<Subscription>>> {
    const subscriptions = await this.adminSubscriptionService.findSubscriptions(filterDto);
    return ApiResponseDto.success(subscriptions, 'Lấy danh sách đăng ký thành công');
  }

  /**
   * Lấy thông tin chi tiết đăng ký
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết đăng ký thành công'
  })
  async findSubscriptionById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<Subscription>> {
    const subscription = await this.adminSubscriptionService.findSubscriptionById(id);
    return ApiResponseDto.success(subscription, 'Lấy thông tin chi tiết đăng ký thành công');
  }

  /**
   * Cập nhật trạng thái đăng ký
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Cập nhật trạng thái đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký' })
  @ApiBody({ type: UpdateSubscriptionStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái đăng ký thành công'
  })
  async updateSubscriptionStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdateSubscriptionStatusDto
  ): Promise<ApiResponseDto<Subscription>> {
    const subscription = await this.adminSubscriptionService.updateSubscriptionStatus(id, updateStatusDto);
    return ApiResponseDto.success(subscription, 'Cập nhật trạng thái đăng ký thành công');
  }

  /**
   * Lấy danh sách nhật ký sử dụng
   */
  @Get(':id/usage-logs')
  @ApiOperation({ summary: 'Lấy danh sách nhật ký sử dụng của đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách nhật ký sử dụng thành công'
  })
  async findUsageLogs(
    @Param('id', ParseIntPipe) id: number,
    @Query() filterDto: AdminUsageLogFilterDto
  ): Promise<ApiResponseDto<PaginatedResult<UsageLog>>> {
    // Đảm bảo lọc theo đăng ký hiện tại
    filterDto.subscriptionId = id;
    
    const usageLogs = await this.adminSubscriptionService.findUsageLogs(filterDto);
    return ApiResponseDto.success(usageLogs, 'Lấy danh sách nhật ký sử dụng thành công');
  }

  /**
   * Lấy thống kê sử dụng của đăng ký
   */
  @Get(':id/usage-stats')
  @ApiOperation({ summary: 'Lấy thống kê sử dụng của đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê sử dụng thành công'
  })
  async getSubscriptionUsageStats(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<{ totalUsage: number; usageByFeature: { feature: string; count: number }[] }>> {
    const stats = await this.adminSubscriptionService.getSubscriptionUsageStats(id);
    return ApiResponseDto.success(stats, 'Lấy thống kê sử dụng thành công');
  }
}
