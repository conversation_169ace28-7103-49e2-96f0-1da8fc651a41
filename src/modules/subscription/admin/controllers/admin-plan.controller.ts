import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AdminPlanService } from '../services';
import { AdminPlanFilterDto, CreatePlanDto, UpdatePlanDto } from '../dto';
import { Plan } from '@modules/subscription/entities';

@ApiTags(SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PLAN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/subscription/plans')
export class AdminPlanController {
  constructor(private readonly adminPlanService: AdminPlanService) {}

  /**
   * <PERSON><PERSON>y danh sách gói dịch vụ
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách gói dịch vụ' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách gói dịch vụ thành công'
  })
  async findPlans(
    @Query() filterDto: AdminPlanFilterDto
  ): Promise<ApiResponseDto<PaginatedResult<Plan>>> {
    const plans = await this.adminPlanService.findPlans(filterDto);
    return ApiResponseDto.success(plans, 'Lấy danh sách gói dịch vụ thành công');
  }

  /**
   * Lấy thông tin chi tiết gói dịch vụ
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết gói dịch vụ' })
  @ApiParam({ name: 'id', description: 'ID của gói dịch vụ' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết gói dịch vụ thành công'
  })
  async findPlanById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<Plan>> {
    const plan = await this.adminPlanService.findPlanById(id);
    return ApiResponseDto.success(plan, 'Lấy thông tin chi tiết gói dịch vụ thành công');
  }

  /**
   * Tạo gói dịch vụ mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo gói dịch vụ mới' })
  @ApiBody({ type: CreatePlanDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo gói dịch vụ mới thành công'
  })
  async createPlan(
    @Body() createPlanDto: CreatePlanDto
  ): Promise<ApiResponseDto<Plan>> {
    const plan = await this.adminPlanService.createPlan(createPlanDto);
    return ApiResponseDto.success(plan, 'Tạo gói dịch vụ mới thành công');
  }

  /**
   * Cập nhật thông tin gói dịch vụ
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin gói dịch vụ' })
  @ApiParam({ name: 'id', description: 'ID của gói dịch vụ' })
  @ApiBody({ type: UpdatePlanDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin gói dịch vụ thành công'
  })
  async updatePlan(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePlanDto: UpdatePlanDto
  ): Promise<ApiResponseDto<Plan>> {
    const plan = await this.adminPlanService.updatePlan(id, updatePlanDto);
    return ApiResponseDto.success(plan, 'Cập nhật thông tin gói dịch vụ thành công');
  }

  /**
   * Xóa gói dịch vụ
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa gói dịch vụ' })
  @ApiParam({ name: 'id', description: 'ID của gói dịch vụ' })
  @ApiResponse({
    status: 200,
    description: 'Xóa gói dịch vụ thành công'
  })
  async deletePlan(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<null>> {
    await this.adminPlanService.deletePlan(id);
    return ApiResponseDto.success(null, 'Xóa gói dịch vụ thành công');
  }
}
