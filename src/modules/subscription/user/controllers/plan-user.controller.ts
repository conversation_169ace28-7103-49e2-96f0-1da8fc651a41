import { <PERSON>, Get, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiResponseDto } from '@common/response/api-response-dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse as ApiResponseDoc,
  ApiTags,
} from '@nestjs/swagger';
import { PlanUserService } from '../services/plan-user.service';
import { PlanResponseDto } from '@modules/subscription/dto';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { PlanPricingResponseDto } from '../../dto';
import { JwtUserGuard } from '@modules/auth/guards';

@ApiTags(SWAGGER_API_TAGS.USER_SUBSCRIPTIONS)
@Controller('user/plans')
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
export class PlanUserController {
  constructor(private readonly planUserService: PlanUserService) {}

  @ApiOperation({ summary: 'L<PERSON><PERSON> danh sách gói dịch vụ' })
  @ApiQuery({ name: 'page', description: 'Trang hiện tại', type: Number, required: false })
  @ApiQuery({ name: 'limit', description: 'Số lượng bản ghi trên mỗi trang', type: Number, required: false })
  @Get()
  async findAll(@Query('page') page = 1, @Query('limit') limit = 10) {
    const result = await this.planUserService.findPlans(+page, +limit);

    const responseData = {
      items: result.items.map(item => new PlanResponseDto(item)),
      meta: result.meta
    };

    return ApiResponseDto.success(responseData);
  }

  @ApiOperation({ summary: 'Lấy chi tiết gói dịch vụ' })
  @ApiParam({ name: 'id', description: 'ID của gói dịch vụ', type: Number })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy gói dịch vụ' })
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const plan = await this.planUserService.findPlanById(id);

    return ApiResponseDto.success(new PlanResponseDto(plan));
  }

  @ApiOperation({ summary: 'Lấy danh sách tùy chọn giá của gói dịch vụ' })
  @ApiParam({ name: 'planId', description: 'ID của gói dịch vụ', type: Number })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách tùy chọn giá đã được lấy thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: { type: 'object' }
            }
          }
        }
      }
    }
  })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy gói dịch vụ' })
  @Get(':planId/pricing')
  async findPlanPricing(@Param('planId', ParseIntPipe) planId: number) {
    const planPricing = await this.planUserService.findPlanPricingByPlanId(planId);

    const responseData = {
      items: planPricing.map(item => new PlanPricingResponseDto(item))
    };

    return ApiResponseDto.success(responseData);
  }
}
