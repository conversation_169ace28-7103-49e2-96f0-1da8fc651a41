import {
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  ApiBearerAuth,
  Api<PERSON>peration,
  ApiParam,
  ApiQuery,
  ApiResponse as ApiResponseDoc,
  ApiTags,
} from '@nestjs/swagger';
import { PlanPricingUserService } from '@modules/subscription/user/services';
import {
  PlanPricingFilterDto,
  PlanPricingResponseDto,
} from '@modules/subscription/dto';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { JwtUserGuard } from '@modules/auth/guards';
import { PlanPricing } from '@modules/subscription/entities';

@ApiTags(SWAGGER_API_TAGS.USER_SUBSCRIPTIONS)
@Controller('user/plan-pricing')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
export class PlanPricingUserController {
  constructor(
    private readonly planPricingUserService: PlanPricingUserService,
  ) {}

  @ApiOperation({ summary: 'Lấy danh sách tùy chọn giá với bộ lọc' })
  @ApiQuery({ type: PlanPricingFilterDto })
  @Get()
  async findAll(
    @Query() filterDto: PlanPricingFilterDto,
  ): Promise<ApiResponseDto<PaginatedResult<PlanPricing>>> {
    const { billingCycle, page, limit } = filterDto;

    return await this.planPricingUserService.findPlanPricing(
      billingCycle || null,
      page,
      limit,
    );
  }

  @ApiOperation({ summary: 'Lấy chi tiết tùy chọn giá theo ID' })
  @ApiParam({ name: 'id', description: 'ID của tùy chọn giá', type: 'number' })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy tùy chọn giá' })
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const planPricing =
      await this.planPricingUserService.findPlanPricingById(id);

    if (!planPricing) {
      throw new NotFoundException(`PlanPricing with ID ${id} not found`);
    }

    return ApiResponseDto.success(new PlanPricingResponseDto(planPricing));
  }
}
