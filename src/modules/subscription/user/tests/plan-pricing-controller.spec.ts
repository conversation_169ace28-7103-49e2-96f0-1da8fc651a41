import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';

// Mock interfaces
interface PlanPricing {
  id: number;
  planId: number;
  billingCycle: string;
  price: number;
  usageLimit: number;
  usageUnit: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
}

// Mock service
class MockPlanPricingUserService {
  async findPlanPricing(billingCycle?: string | null, page: number = 1, limit: number = 10): Promise<PaginatedResult<PlanPricing>> {
    const mockPlanPricing: PlanPricing = {
      id: 1,
      planId: 1,
      billingCycle: 'MONTHLY',
      price: 9.99,
      usageLimit: 1000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const mockPlanPricingList: PlanPricing[] = [
      mockPlanPricing,
      {
        ...mockPlanPricing,
        id: 2,
        billingCycle: 'YEARLY',
        price: 99.99,
        usageLimit: 12000
      }
    ];

    // Filter by billingCycle if provided
    const filteredItems = billingCycle 
      ? mockPlanPricingList.filter(item => item.billingCycle === billingCycle)
      : mockPlanPricingList;

    return {
      items: filteredItems,
      meta: {
        totalItems: filteredItems.length,
        itemCount: filteredItems.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(filteredItems.length / limit),
        currentPage: page
      }
    };
  }

  async findPlanPricingById(id: number): Promise<PlanPricing | null> {
    if (id === 999) {
      return null;
    }

    return {
      id,
      planId: 1,
      billingCycle: 'MONTHLY',
      price: 9.99,
      usageLimit: 1000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }
}

// Mock controller
class MockPlanPricingUserController {
  constructor(private readonly planPricingUserService: MockPlanPricingUserService) {}

  async findAll(filterDto: { billingCycle?: string; page?: number; limit?: number }) {
    const { billingCycle, page = 1, limit = 10 } = filterDto;
    const result = await this.planPricingUserService.findPlanPricing(billingCycle || null, page, limit);
    
    const responseData = {
      items: result.items.map(item => ({ ...item })),
      meta: result.meta
    };
    
    return {
      code: 200,
      message: 'Success',
      result: responseData
    } as ApiResponse;
  }

  async findOne(id: number) {
    const planPricing = await this.planPricingUserService.findPlanPricingById(id);
    
    if (!planPricing) {
      throw new NotFoundException(`PlanPricing with ID ${id} not found`);
    }
    
    return {
      code: 200,
      message: 'Success',
      result: planPricing
    } as ApiResponse;
  }
}

describe('PlanPricingUserController', () => {
  let controller: MockPlanPricingUserController;
  let service: MockPlanPricingUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MockPlanPricingUserService,
        {
          provide: MockPlanPricingUserController,
          useFactory: (service: MockPlanPricingUserService) => new MockPlanPricingUserController(service),
          inject: [MockPlanPricingUserService],
        },
      ],
    }).compile();

    controller = module.get<MockPlanPricingUserController>(MockPlanPricingUserController);
    service = module.get<MockPlanPricingUserService>(MockPlanPricingUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all plan pricing options with pagination', async () => {
      const filterDto = { page: 1, limit: 10 };
      const result = await controller.findAll(filterDto);
      
      expect(result.code).toBe(200);
      expect(result.message).toBe('Success');
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result.result.items.length).toBeGreaterThan(0);
    });

    it('should filter plan pricing by billingCycle when provided', async () => {
      const filterDto = { 
        page: 1, 
        limit: 10,
        billingCycle: 'MONTHLY'
      };
      const result = await controller.findAll(filterDto);
      
      expect(result.code).toBe(200);
      expect(result.result.items.length).toBeGreaterThan(0);
      expect(result.result.items.every(item => item.billingCycle === 'MONTHLY')).toBe(true);
    });
  });

  describe('findOne', () => {
    it('should return a plan pricing by id', async () => {
      const result = await controller.findOne(1);
      
      expect(result.code).toBe(200);
      expect(result.result.id).toBe(1);
    });

    it('should throw NotFoundException if plan pricing is not found', async () => {
      await expect(controller.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });
});
