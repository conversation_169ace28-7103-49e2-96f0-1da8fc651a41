import {Injectable} from '@nestjs/common';
import {InjectRepository} from '@nestjs/typeorm';
import {Repository} from 'typeorm';
import {PlanPricing} from '@modules/subscription/entities';
import {PlanPricingRepository} from '@modules/subscription/repositories';
import {ApiResponseDto, PaginatedResult} from "@common/response/api-response-dto";
import {QueryDto, SortDirection} from "@common/dto";

@Injectable()
export class PlanPricingUserService {
    constructor(
        @InjectRepository(PlanPricing)
        private readonly planPricingRepository: Repository<PlanPricing>,
        private readonly planPricingCustomRepository: PlanPricingRepository
    ) {
    }

    /**
     * Lấy danh sách plan pricing với bộ lọc
     * @param billingCycle Chu kỳ thanh toán (tùy chọn
     * )
     * @param page Số trang
     * @param limit Số lượng bản ghi trên mỗi trang
     * @returns Danh sách plan pricing đã phân trang
     */
    async findPlanPricing(
        billingCycle?: string | null,
        page: number = 1,
        limit: number = 10
    ): Promise<ApiResponseDto<PaginatedResult<PlanPricing>>> {
        // Tạo tham số phân trang
        const paginationParams: QueryDto = {
            page,
            limit,
            sortBy: 'createdAt',
            sortDirection: SortDirection.DESC,
        };

        // Nếu có billingCycle, sử dụng phương thức tìm kiếm theo billingCycle
        if (billingCycle) {
            return ApiResponseDto.paginated(await this.planPricingCustomRepository.findByBillingCycle(billingCycle, paginationParams));
        }

        // Nếu không có billingCycle, lấy tất cả plan pricing
        return ApiResponseDto.paginated(await this.planPricingCustomRepository.findPlanPricing(paginationParams));
    }

    /**
     * Lấy chi tiết plan pricing theo ID
     * @param id ID của plan pricing
     * @returns Chi tiết plan pricing
     */
    async findPlanPricingById(id: number): Promise<PlanPricing | null> {
        return this.planPricingRepository.findOne({where: {id}});
    }
}
