import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import {PackageType} from "@modules/subscription/enums";

@Entity('plans')
export class Plan {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'bigint' })
  createdAt: number;

  @Column({ type: 'bigint' })
  updatedAt: number;

  @Column({
    type: 'enum',
    enum: PackageType,
    default: PackageType.TIME_ONLY
  })
  packageType: PackageType;
}
