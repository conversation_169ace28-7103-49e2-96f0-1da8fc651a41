import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity đại diện cho bảng usage_logs
 * Ghi nhận mức sử dụng user, phục vụ tính phí usage-based
 */
@Entity('usage_logs')
export class UsageLog {
  /**
   * ID của bản ghi usage log
   */
  @PrimaryGeneratedColumn('increment')
  id: number;

  /**
   * Subscription mà usage thuộc về
   */
  @Column({
    name: 'subscription_id',
    comment: 'Subscription mà usage thuộc về'
  })
  subscriptionId: number;

  /**
   * Tên tính năng, map với plan_features.feature
   */
  @Column({
    length: 100,
    comment: 'Tên tính năng, map với plan_features.feature'
  })
  feature: string;

  /**
   * Số lượng usage (GB, request,...)
   */
  @Column({
    type: 'numeric',
    comment: 'Số lượng usage (GB, request,...)'
  })
  amount: number;

  /**
   * Thời điểm usage phát sinh (Unix timestamp)
   */
  @Column({
    name: 'usage_time',
    type: 'bigint',
    comment: 'Thời điểm usage phát sinh (Unix timestamp)'
  })
  usageTime: number;

  /**
   * Thời điểm ghi log (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    comment: 'Thời điểm ghi log (Unix timestamp)'
  })
  createdAt: number;
}
