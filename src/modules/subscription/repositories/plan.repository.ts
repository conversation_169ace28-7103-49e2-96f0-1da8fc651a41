import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Plan } from '../entities/plan.entity';
import {PaginatedResult} from "@common/response/api-response-dto";
import {QueryDto} from "@common/dto";
import { SqlHelper } from '@common/helpers';

@Injectable()
export class PlanRepository extends Repository<Plan> {

  private sqlHelper: SqlHelper;

  constructor(private dataSource: DataSource) {
    super(Plan, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  /**
   * L<PERSON>y danh sách gói dịch vụ với phân trang
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findPlans(paginationParams: QueryDto): Promise<PaginatedResult<Plan>> {
    // Sử dụng helper function để lấ<PERSON> kết quả phân trang
    return this.sqlHelper.getPaginatedData(this, paginationParams, {alias: 'plans'});
  }

  /**
   * L<PERSON>y chi tiết gói dịch vụ theo ID
   * @param id ID của gói dịch vụ
   * @returns Chi tiết gói dịch vụ
   */
  async findPlanById(id: number): Promise<Plan | null> {
    return this.findOne({ where: { id } });
  }
}
