import { Injectable, Logger } from '@nestjs/common';
import { createActor, AnyActorRef } from 'xstate';
import {
  RuleContractState,
  RuleContractEvent,
  RuleContractContext,
  CreateEventData,
  SignByUserEventData,
  ApproveEventData,
  RejectEventData,
  UpgradeToBusinessEventData,
  IndividualContractData
} from './rule-contract.types';
import { ContractTypeEnum } from '../entities/rule-contract.entity';
import { createRuleContractMachine } from './rule-contract.machine';
import { RuleContractRepository } from '../repositories';
import { AppException } from '@common/exceptions';
import { RULE_CONTRACT_ERROR_CODES } from '../errors';
import { RuleContractActionsService } from './rule-contract-actions.service';

/**
 * Service quản lý máy trạng thái hợp đồng nguyên tắc
 */
@Injectable()
export class RuleContractStateService {
  private readonly logger = new Logger(RuleContractStateService.name);
  private readonly machines: Map<number, AnyActorRef> = new Map();

  constructor(
    private readonly ruleContractRepository: RuleContractRepository,
    private readonly ruleContractActionsService: RuleContractActionsService,
  ) {}

  /**
   * Khởi tạo máy trạng thái cho hợp đồng
   * @param userId ID của người dùng
   * @param contractId ID của hợp đồng (nếu có)
   * @returns Máy trạng thái đã khởi tạo
   */
  async initializeStateMachine(
    userId: number,
    contractId?: number
  ): Promise<AnyActorRef> {
    try {
      // Nếu đã có máy trạng thái cho hợp đồng này, trả về máy trạng thái đó
      if (contractId && this.machines.has(contractId)) {
        const existingMachine = this.machines.get(contractId);
        if (existingMachine) {
          return existingMachine;
        }
      }

      // Ngữ cảnh ban đầu
      const initialContext: Partial<RuleContractContext> = {
        userId,
        contractId: contractId || null,
      };

      // Nếu có contractId, lấy thông tin hợp đồng từ database
      if (contractId) {
        const contract = await this.ruleContractRepository.findById(contractId);
        if (!contract) {
          throw new AppException(
            RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
            `Không tìm thấy hợp đồng với ID ${contractId}`,
          );
        }

        // Cập nhật ngữ cảnh với thông tin hợp đồng
        initialContext.contractId = contract.id;
        initialContext.userId = contract.userId;
        initialContext.contractType = contract.type;
        initialContext.contractStatus = contract.status;
        initialContext.contractKey = contract.contractUrlPdf;
        initialContext.createdAt = contract.createdAt;
        initialContext.userSignatureAt = contract.userSignatureAt;
        initialContext.adminSignatureAt = contract.adminSignatureAt;
      }

      // Tạo máy trạng thái
      const machine = createRuleContractMachine(initialContext);

      // Tạo actor từ máy trạng thái
      const actor = createActor(machine, {
        input: initialContext as RuleContractContext,
        systemId: `contract-${contractId || 'new'}`,
      });

      // Đăng ký các actions
      actor.subscribe((state) => {
        this.logger.log(`Chuyển đổi trạng thái hợp đồng ${state.context.contractId}: ${state.value}`);
      });

      // Khởi động actor
      actor.start();

      // Lưu actor vào map nếu có contractId
      if (contractId) {
        this.machines.set(contractId, actor);
      }

      return actor;
    } catch (error) {
      this.logger.error(`Lỗi khi khởi tạo máy trạng thái: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo hợp đồng mới
   * @param userId ID của người dùng
   * @param data Dữ liệu tạo hợp đồng
   * @returns ID của hợp đồng đã tạo
   */
  async createContract(userId: number, data: CreateEventData): Promise<number> {
    try {
      // Khởi tạo máy trạng thái
      const actor = await this.initializeStateMachine(userId);

      // Gửi sự kiện CREATE
      actor.send({ type: RuleContractEvent.CREATE, data });

      // Lưu hợp đồng vào database
      const contract = await this.ruleContractActionsService.saveContract(
        actor.getSnapshot().context,
        { type: RuleContractEvent.CREATE, data }
      );

      // Trả về ID của hợp đồng đã tạo
      return contract.contractId || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo hợp đồng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo hợp đồng nguyên tắc cho cá nhân
   * @param userId ID của người dùng
   * @param data Dữ liệu tạo hợp đồng cá nhân
   * @returns Thông tin hợp đồng đã tạo
   */
  async createIndividualContract(
    userId: number,
    data: IndividualContractData
  ): Promise<{ contractId: number; contractKey: string; contractBase64: string; contractUrl?: string }> {
    try {
      // Khởi tạo máy trạng thái
      const actor = await this.initializeStateMachine(userId);

      // Tạo dữ liệu sự kiện
      const createData: CreateEventData = {
        userId,
        contractType: ContractTypeEnum.INDIVIDUAL,
        individualContractData: data
      };

      // Gửi sự kiện CREATE
      actor.send({ type: RuleContractEvent.CREATE, data: createData });

      // Lưu hợp đồng vào database và xử lý file PDF
      const result = await this.ruleContractActionsService.saveIndividualContract(
        actor.getSnapshot().context,
        { type: RuleContractEvent.CREATE, data: createData }
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo hợp đồng cá nhân: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Ký hợp đồng
   * @param contractId ID của hợp đồng
   * @param data Dữ liệu ký hợp đồng
   * @returns Trạng thái mới của hợp đồng
   */
  async signContract(contractId: number, data: SignByUserEventData): Promise<RuleContractState> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Gửi sự kiện SIGN_BY_USER
      actor.send({ type: RuleContractEvent.SIGN_BY_USER, data });

      // Lưu chữ ký vào database
      await this.ruleContractActionsService.saveUserSignature(
        actor.getSnapshot().context,
        { type: RuleContractEvent.SIGN_BY_USER, data }
      );

      // Gửi thông báo cho admin
      await this.ruleContractActionsService.notifyAdmin(
        actor.getSnapshot().context,
        { type: RuleContractEvent.SIGN_BY_USER, data }
      );

      // Trả về trạng thái mới
      return actor.getSnapshot().value as RuleContractState;
    } catch (error) {
      this.logger.error(`Lỗi khi ký hợp đồng ${contractId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Phê duyệt hợp đồng
   * @param contractId ID của hợp đồng
   * @param data Dữ liệu phê duyệt
   * @returns Trạng thái mới của hợp đồng
   */
  async approveContract(contractId: number, data: ApproveEventData): Promise<RuleContractState> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Kiểm tra quyền admin
      if (!this.ruleContractActionsService.isAdmin(
        actor.getSnapshot().context,
        { type: RuleContractEvent.APPROVE, data }
      )) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.PERMISSION_DENIED,
          'Chỉ admin mới có quyền phê duyệt hợp đồng',
        );
      }

      // Gửi sự kiện APPROVE
      actor.send({ type: RuleContractEvent.APPROVE, data });

      // Lưu phê duyệt vào database
      await this.ruleContractActionsService.saveAdminApproval(
        actor.getSnapshot().context,
        { type: RuleContractEvent.APPROVE, data }
      );

      // Gửi thông báo cho người dùng
      await this.ruleContractActionsService.notifyUser(
        actor.getSnapshot().context,
        { type: RuleContractEvent.APPROVE, data }
      );

      // Hoàn tất hợp đồng
      await this.ruleContractActionsService.finalizeContract(
        actor.getSnapshot().context,
        { type: RuleContractEvent.APPROVE, data }
      );

      // Trả về trạng thái mới
      return actor.getSnapshot().value as RuleContractState;
    } catch (error) {
      this.logger.error(`Lỗi khi phê duyệt hợp đồng ${contractId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Từ chối hợp đồng
   * @param contractId ID của hợp đồng
   * @param data Dữ liệu từ chối
   * @returns Trạng thái mới của hợp đồng
   */
  async rejectContract(contractId: number, data: RejectEventData): Promise<RuleContractState> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Kiểm tra quyền admin
      if (!this.ruleContractActionsService.isAdmin(
        actor.getSnapshot().context,
        { type: RuleContractEvent.REJECT, data }
      )) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.PERMISSION_DENIED,
          'Chỉ admin mới có quyền từ chối hợp đồng',
        );
      }

      // Gửi sự kiện REJECT
      actor.send({ type: RuleContractEvent.REJECT, data });

      // Lưu từ chối vào database
      await this.ruleContractActionsService.saveRejection(
        actor.getSnapshot().context,
        { type: RuleContractEvent.REJECT, data }
      );

      // Gửi thông báo cho người dùng
      await this.ruleContractActionsService.notifyUser(
        actor.getSnapshot().context,
        { type: RuleContractEvent.REJECT, data }
      );

      // Trả về trạng thái mới
      return actor.getSnapshot().value as RuleContractState;
    } catch (error) {
      this.logger.error(`Lỗi khi từ chối hợp đồng ${contractId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gửi lại hợp đồng đã bị từ chối
   * @param contractId ID của hợp đồng
   * @returns Trạng thái mới của hợp đồng
   */
  async resubmitContract(contractId: number): Promise<RuleContractState> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Gửi sự kiện RESUBMIT
      actor.send({ type: RuleContractEvent.RESUBMIT });

      // Xóa lý do từ chối
      await this.ruleContractActionsService.clearRejectionReason(
        actor.getSnapshot().context,
        { type: RuleContractEvent.RESUBMIT }
      );

      // Trả về trạng thái mới
      return actor.getSnapshot().value as RuleContractState;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi lại hợp đồng ${contractId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Nâng cấp loại hợp đồng từ cá nhân lên doanh nghiệp
   * @param contractId ID của hợp đồng
   * @param data Dữ liệu nâng cấp
   * @returns Trạng thái mới của hợp đồng
   */
  async upgradeToBusinessContract(
    contractId: number,
    data: UpgradeToBusinessEventData
  ): Promise<RuleContractState> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Kiểm tra điều kiện nâng cấp
      if (!this.ruleContractActionsService.canUpgradeToBusinessType(
        actor.getSnapshot().context,
        { type: RuleContractEvent.UPGRADE_TO_BUSINESS, data }
      )) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_OPERATION,
          'Không thể nâng cấp hợp đồng này lên loại doanh nghiệp',
        );
      }

      // Gửi sự kiện UPGRADE_TO_BUSINESS
      actor.send({ type: RuleContractEvent.UPGRADE_TO_BUSINESS, data });

      // Nâng cấp loại hợp đồng
      await this.ruleContractActionsService.upgradeContractType(
        actor.getSnapshot().context,
        { type: RuleContractEvent.UPGRADE_TO_BUSINESS, data }
      );

      // Trả về trạng thái mới
      return actor.getSnapshot().value as RuleContractState;
    } catch (error) {
      this.logger.error(
        `Lỗi khi nâng cấp hợp đồng ${contractId} lên loại doanh nghiệp: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Lấy trạng thái hiện tại của hợp đồng
   * @param contractId ID của hợp đồng
   * @returns Trạng thái hiện tại
   */
  async getCurrentState(contractId: number): Promise<RuleContractState> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Trả về trạng thái hiện tại
      return actor.getSnapshot().value as RuleContractState;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy trạng thái hiện tại của hợp đồng ${contractId}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Lấy context đầy đủ của hợp đồng từ state machine
   * @param contractId ID của hợp đồng
   * @returns Context của hợp đồng
   */
  async getContractContext(contractId: number): Promise<RuleContractContext> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Trả về context đầy đủ
      return actor.getSnapshot().context;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy context của hợp đồng ${contractId}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem có thể thực hiện event không
   * @param contractId ID của hợp đồng
   * @param eventType Loại event
   * @returns true nếu có thể thực hiện event
   */
  async canExecuteEvent(contractId: number, eventType: RuleContractEvent): Promise<boolean> {
    try {
      // Lấy máy trạng thái
      const actor = await this.initializeStateMachine(0, contractId);

      // Kiểm tra xem event có thể được thực hiện từ state hiện tại không
      const snapshot = actor.getSnapshot();
      const currentState = snapshot.value as RuleContractState;

      // Logic kiểm tra dựa trên state machine definition
      switch (currentState) {
        case RuleContractState.DRAFT:
          return [
            RuleContractEvent.CREATE,
            RuleContractEvent.SIGN_BY_USER,
            RuleContractEvent.UPGRADE_TO_BUSINESS
          ].includes(eventType);

        case RuleContractState.PENDING_APPROVAL:
          return [
            RuleContractEvent.APPROVE,
            RuleContractEvent.REJECT
          ].includes(eventType);

        case RuleContractState.REJECTED:
          return eventType === RuleContractEvent.RESUBMIT;

        case RuleContractState.APPROVED:
          return false; // Final state

        default:
          return false;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra event ${eventType} cho hợp đồng ${contractId}: ${error.message}`,
        error.stack
      );
      return false;
    }
  }

  /**
   * Lấy danh sách events có thể thực hiện từ state hiện tại
   * @param contractId ID của hợp đồng
   * @returns Danh sách events có thể thực hiện
   */
  async getAvailableEvents(contractId: number): Promise<RuleContractEvent[]> {
    try {
      const availableEvents: RuleContractEvent[] = [];

      for (const event of Object.values(RuleContractEvent)) {
        if (await this.canExecuteEvent(contractId, event)) {
          availableEvents.push(event);
        }
      }

      return availableEvents;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách events cho hợp đồng ${contractId}: ${error.message}`,
        error.stack
      );
      return [];
    }
  }

  /**
   * Cleanup máy trạng thái khi không cần thiết
   * @param contractId ID của hợp đồng
   */
  async cleanupStateMachine(contractId: number): Promise<void> {
    try {
      if (this.machines.has(contractId)) {
        const actor = this.machines.get(contractId);
        if (actor) {
          actor.stop();
        }
        this.machines.delete(contractId);
        this.logger.log(`Đã cleanup máy trạng thái cho hợp đồng ${contractId}`);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi cleanup máy trạng thái cho hợp đồng ${contractId}: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Lấy thống kê về các máy trạng thái đang hoạt động
   * @returns Thống kê máy trạng thái
   */
  getStateMachineStats(): { activeCount: number; contractIds: number[] } {
    const contractIds = Array.from(this.machines.keys());
    return {
      activeCount: this.machines.size,
      contractIds,
    };
  }
}
