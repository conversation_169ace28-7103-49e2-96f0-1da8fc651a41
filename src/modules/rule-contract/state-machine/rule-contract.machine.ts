import { createMachine } from 'xstate';
import {
  RuleContractState,
  RuleContractEvent,
  RuleContractContext
} from './rule-contract.types';
import { ContractStatusEnum, ContractTypeEnum } from '../entities/rule-contract.entity';

/**
 * <PERSON><PERSON><PERSON> nghĩa các kiểu sự kiện và dữ liệu đi kèm
 * Lưu ý: Trong XState v5, chúng ta không cần khai báo kiểu sự kiện trực tiếp
 * trong createMachine, thay vào đó chúng ta sử dụng cú pháp { type: 'actionName' }
 */

/**
 * Tạo máy trạng thái cho hợp đồng nguyên tắc
 * @param initialContext Ngữ cảnh ban đầu
 * @returns Máy trạng thái đã được cấu hình
 */
export const createRuleContractMachine = (initialContext: Partial<RuleContractContext> = {}) => {
  return createMachine({
    id: 'ruleContract',
    initial: RuleContractState.DRAFT,
    context: {
      contractId: null,
      userId: null,
      contractType: ContractTypeEnum.INDIVIDUAL,
      contractStatus: ContractStatusEnum.DRAFT,
      ...initialContext,
    },
    states: {
      [RuleContractState.DRAFT]: {
        on: {
          [RuleContractEvent.CREATE]: {
            target: RuleContractState.DRAFT,
            actions: { type: 'saveContract' },
          },
          [RuleContractEvent.SIGN_BY_USER]: {
            target: RuleContractState.PENDING_APPROVAL,
            actions: [
              { type: 'saveUserSignature' },
              { type: 'notifyAdmin' }
            ],
            guard: { type: 'isValidSignature' },
          },
          [RuleContractEvent.UPGRADE_TO_BUSINESS]: {
            target: RuleContractState.DRAFT,
            actions: [
              { type: 'upgradeContractType' },
              { type: 'saveContract' }
            ],
            guard: { type: 'canUpgradeToBusinessType' },
          },
        },
      },
      [RuleContractState.PENDING_APPROVAL]: {
        on: {
          [RuleContractEvent.APPROVE]: {
            target: RuleContractState.APPROVED,
            actions: [
              { type: 'saveAdminApproval' },
              { type: 'notifyUser' }
            ],
            guard: { type: 'isAdmin' },
          },
          [RuleContractEvent.REJECT]: {
            target: RuleContractState.REJECTED,
            actions: [
              { type: 'saveRejection' },
              { type: 'notifyUser' }
            ],
            guard: { type: 'isAdmin' },
          },
        },
      },
      [RuleContractState.APPROVED]: {
        type: 'final',
        entry: { type: 'finalizeContract' },
      },
      [RuleContractState.REJECTED]: {
        on: {
          [RuleContractEvent.RESUBMIT]: {
            target: RuleContractState.DRAFT,
            actions: { type: 'clearRejectionReason' },
          },
        },
      },
    },
  });
};
