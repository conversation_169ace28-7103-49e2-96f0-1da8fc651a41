import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { ContractTypeEnum } from '../../entities/rule-contract.entity';

/**
 * DTO cho yêu cầu đăng ký loại hợp đồng nguyên tắc
 */
export class RegisterRuleContractDto {
  /**
   * <PERSON><PERSON><PERSON> hợp đồng
   */
  @ApiProperty({
    description: 'Loại hợp đồng',
    enum: ContractTypeEnum,
    example: ContractTypeEnum.INDIVIDUAL,
    required: true,
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> hợp đồng không được để trống' })
  @IsEnum(ContractTypeEnum, { message: '<PERSON><PERSON><PERSON> hợp đồng không hợp lệ' })
  type: ContractTypeEnum;
}
