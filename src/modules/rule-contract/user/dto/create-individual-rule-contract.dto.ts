import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho yêu cầu tạo hợp đồng nguyên tắc cho cá nhân
 */
export class CreateIndividualRuleContractDto {
  /**
   * Họ tên đầy đủ
   */
  @ApiProperty({
    description: 'Họ tên đầy đủ',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsNotEmpty({ message: 'Họ tên không được để trống' })
  @IsString({ message: 'Họ tên phải là chuỗi' })
  @MinLength(2, { message: 'Họ tên phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Họ tên không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Địa chỉ
   */
  @ApiProperty({
    description: 'Địa chỉ',
    example: '<PERSON><PERSON> 123, Đ<PERSON>ờng ABC, Quận XYZ, TP. Hồ Chí Minh',
    required: true,
  })
  @IsNotEmpty({ message: 'Địa chỉ không được để trống' })
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MinLength(5, { message: 'Địa chỉ phải có ít nhất 5 ký tự' })
  @MaxLength(1000, { message: 'Địa chỉ không được vượt quá 1000 ký tự' })
  address: string;

  /**
   * Số điện thoại
   */
  @ApiProperty({
    description: 'Số điện thoại',
    example: '0912345678',
    required: true,
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  phone: string;

  /**
   * Ngày sinh
   */
  @ApiProperty({
    description: 'Ngày sinh (định dạng YYYY-MM-DD)',
    example: '1990-01-01',
    required: true,
  })
  @IsNotEmpty({ message: 'Ngày sinh không được để trống' })
  @IsDateString({}, { message: 'Ngày sinh phải có định dạng YYYY-MM-DD' })
  dateOfBirth: string;

  /**
   * Số CCCD/CMND
   */
  @ApiProperty({
    description: 'Số CCCD/CMND',
    example: '079123456789',
    required: true,
  })
  @IsNotEmpty({ message: 'Số CCCD/CMND không được để trống' })
  @IsString({ message: 'Số CCCD/CMND phải là chuỗi' })
  cccd: string;

  /**
   * Nơi cấp CCCD/CMND
   */
  @ApiProperty({
    description: 'Nơi cấp CCCD/CMND',
    example: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
    required: true,
  })
  @IsNotEmpty({ message: 'Nơi cấp CCCD/CMND không được để trống' })
  @IsString({ message: 'Nơi cấp CCCD/CMND phải là chuỗi' })
  issuePlace: string;

  /**
   * Ngày cấp CCCD/CMND
   */
  @ApiProperty({
    description: 'Ngày cấp CCCD/CMND (định dạng YYYY-MM-DD)',
    example: '2020-01-01',
    required: true,
  })
  @IsNotEmpty({ message: 'Ngày cấp CCCD/CMND không được để trống' })
  @IsDateString({}, { message: 'Ngày cấp CCCD/CMND phải có định dạng YYYY-MM-DD' })
  issueDate: string;

  /**
   * Mã số thuế
   */
  @ApiProperty({
    description: 'Mã số thuế (nếu có)',
    example: '1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã số thuế phải là chuỗi' })
  taxCode?: string;
}
