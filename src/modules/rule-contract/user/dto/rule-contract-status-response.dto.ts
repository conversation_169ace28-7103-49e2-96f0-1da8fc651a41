import { ApiProperty } from '@nestjs/swagger';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';

/**
 * DTO cho phản hồi trạng thái hợp đồng nguyên tắc
 */
export class RuleContractStatusResponseDto {
  /**
   * Trạng thái hợp đồng
   */
  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatusEnum,
    example: ContractStatusEnum.DRAFT,
  })
  status: ContractStatusEnum;

  /**
   * <PERSON><PERSON><PERSON> hợp đồng
   */
  @ApiProperty({
    description: 'Lo<PERSON><PERSON> hợp đồng',
    enum: ContractTypeEnum,
    example: ContractTypeEnum.INDIVIDUAL,
  })
  type: ContractTypeEnum;
}
