import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CustomFieldDataType } from '../enums/custom-field-data-type.enum';
import {
  TextCustomFieldConfigDto,
  NumberCustomFieldConfigDto,
  BooleanCustomFieldConfigDto,
  DateCustomFieldConfigDto,
  SelectCustomFieldConfigDto,
  ObjectCustomFieldConfigDto,
} from '../dto/custom-field-config.dto';
import {
  TextCustomFieldConfig,
  NumberCustomFieldConfig,
  BooleanCustomFieldConfig,
  DateCustomFieldConfig,
  SelectCustomFieldConfig,
  ObjectCustomFieldConfig,
} from '../interfaces/custom-field-config.interface';

/**
 * Utility class để validate và xử lý config của custom field
 */
export class CustomFieldConfigUtil {
  /**
   * Validate config theo dataType
   * @param dataType Loại dữ liệu
   * @param config Config cần validate
   * @returns Promise<string[]> - <PERSON>h sách lỗi (rỗng nếu hợp lệ)
   */
  static async validateConfig(
    dataType: CustomFieldDataType,
    config: any
  ): Promise<string[]> {
    if (!config || typeof config !== 'object') {
      return ['Config phải là object'];
    }

    let dto: any;
    
    switch (dataType) {
      case CustomFieldDataType.TEXT:
        dto = plainToClass(TextCustomFieldConfigDto, config);
        break;
      case CustomFieldDataType.NUMBER:
        dto = plainToClass(NumberCustomFieldConfigDto, config);
        break;
      case CustomFieldDataType.BOOLEAN:
        dto = plainToClass(BooleanCustomFieldConfigDto, config);
        break;
      case CustomFieldDataType.DATE:
        dto = plainToClass(DateCustomFieldConfigDto, config);
        break;
      case CustomFieldDataType.SELECT:
        dto = plainToClass(SelectCustomFieldConfigDto, config);
        // Validate defaultValue nằm trong options
        if (dto.defaultValue && dto.options) {
          const validValues = dto.options.map((opt: any) => opt.value);
          if (!validValues.includes(dto.defaultValue)) {
            return ['Giá trị mặc định phải là một trong các value trong options'];
          }
        }
        break;
      case CustomFieldDataType.OBJECT:
        dto = plainToClass(ObjectCustomFieldConfigDto, config);
        break;
      default:
        return [`Loại dữ liệu không hợp lệ: ${dataType}`];
    }

    const errors = await validate(dto);
    return errors.flatMap(error => 
      error.constraints ? Object.values(error.constraints) : []
    );
  }

  /**
   * Tạo config mặc định theo dataType
   * @param dataType Loại dữ liệu
   * @returns Config mặc định
   */
  static getDefaultConfig(dataType: CustomFieldDataType): any {
    switch (dataType) {
      case CustomFieldDataType.TEXT:
        return {
          placeholder: 'Nhập văn bản...',
          maxLength: 255,
        } as TextCustomFieldConfig;

      case CustomFieldDataType.NUMBER:
        return {
          placeholder: 'Nhập số...',
        } as NumberCustomFieldConfig;

      case CustomFieldDataType.BOOLEAN:
        return {
          placeholder: 'Chọn true/false',
          defaultValue: false,
        } as BooleanCustomFieldConfig;

      case CustomFieldDataType.DATE:
        return {
          placeholder: 'Chọn ngày...',
        } as DateCustomFieldConfig;

      case CustomFieldDataType.SELECT:
        return {
          placeholder: 'Chọn tùy chọn...',
          options: [
            { title: 'Tùy chọn 1', value: 'option_1' },
            { title: 'Tùy chọn 2', value: 'option_2' },
          ],
        } as SelectCustomFieldConfig;

      case CustomFieldDataType.OBJECT:
        return {
          placeholder: 'Nhập object...',
          defaultValue: {},
        } as ObjectCustomFieldConfig;

      default:
        return {};
    }
  }

  /**
   * Validate giá trị theo config
   * @param value Giá trị cần validate
   * @param dataType Loại dữ liệu
   * @param config Config validation
   * @returns string[] - Danh sách lỗi
   */
  static validateValue(
    value: any,
    dataType: CustomFieldDataType,
    config: any
  ): string[] {
    const errors: string[] = [];

    switch (dataType) {
      case CustomFieldDataType.TEXT:
        if (typeof value !== 'string') {
          errors.push('Giá trị phải là chuỗi');
          break;
        }
        
        if (config.minLength && value.length < config.minLength) {
          errors.push(`Độ dài tối thiểu là ${config.minLength} ký tự`);
        }
        
        if (config.maxLength && value.length > config.maxLength) {
          errors.push(`Độ dài tối đa là ${config.maxLength} ký tự`);
        }
        
        if (config.pattern) {
          const regex = new RegExp(config.pattern);
          if (!regex.test(value)) {
            errors.push('Giá trị không khớp với mẫu yêu cầu');
          }
        }
        break;

      case CustomFieldDataType.NUMBER:
        if (typeof value !== 'number') {
          errors.push('Giá trị phải là số');
          break;
        }
        
        if (config.minValue !== undefined && value < config.minValue) {
          errors.push(`Giá trị tối thiểu là ${config.minValue}`);
        }
        
        if (config.maxValue !== undefined && value > config.maxValue) {
          errors.push(`Giá trị tối đa là ${config.maxValue}`);
        }
        break;

      case CustomFieldDataType.BOOLEAN:
        if (typeof value !== 'boolean') {
          errors.push('Giá trị phải là boolean');
        }
        break;

      case CustomFieldDataType.DATE:
        if (typeof value !== 'string') {
          errors.push('Giá trị phải là chuỗi ngày');
          break;
        }
        
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          errors.push('Giá trị phải là định dạng ngày hợp lệ');
        }
        break;

      case CustomFieldDataType.SELECT:
        if (typeof value !== 'string') {
          errors.push('Giá trị phải là chuỗi');
          break;
        }
        
        if (config.options && Array.isArray(config.options)) {
          const validValues = config.options.map((opt: any) => opt.value);
          if (!validValues.includes(value)) {
            errors.push('Giá trị phải là một trong các tùy chọn hợp lệ');
          }
        }
        break;

      case CustomFieldDataType.OBJECT:
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          errors.push('Giá trị phải là object');
        }
        break;

      default:
        errors.push(`Loại dữ liệu không hợp lệ: ${dataType}`);
    }

    return errors;
  }

  /**
   * Lấy example config cho documentation
   * @param dataType Loại dữ liệu
   * @returns Example config
   */
  static getExampleConfig(dataType: CustomFieldDataType): any {
    switch (dataType) {
      case CustomFieldDataType.TEXT:
        return {
          placeholder: 'Nhập họ tên...',
          defaultValue: '',
          pattern: '^[a-zA-ZÀ-ỹ\\s]+$',
          minLength: 2,
          maxLength: 100,
        };

      case CustomFieldDataType.NUMBER:
        return {
          placeholder: 'Nhập tuổi...',
          defaultValue: 18,
          minValue: 0,
          maxValue: 120,
        };

      case CustomFieldDataType.BOOLEAN:
        return {
          placeholder: 'Đồng ý điều khoản',
          defaultValue: false,
        };

      case CustomFieldDataType.DATE:
        return {
          placeholder: 'Chọn ngày sinh...',
          defaultValue: '2000-01-01',
        };

      case CustomFieldDataType.SELECT:
        return {
          placeholder: 'Chọn giới tính...',
          options: [
            { title: 'Nam', value: 'male' },
            { title: 'Nữ', value: 'female' },
            { title: 'Khác', value: 'other' },
          ],
          defaultValue: 'male',
        };

      case CustomFieldDataType.OBJECT:
        return {
          placeholder: 'Nhập thông tin địa chỉ...',
          defaultValue: {
            street: '',
            city: '',
            country: 'Vietnam',
          },
        };

      default:
        return {};
    }
  }
}
