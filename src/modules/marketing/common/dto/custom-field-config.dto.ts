import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsNumber, 
  IsBoolean, 
  IsOptional, 
  IsArray, 
  ValidateNested, 
  IsObject,
  Min,
  IsDateString,
  ArrayMinSize
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Base DTO cho config
 */
export class BaseCustomFieldConfigDto {
  /**
   * Placeholder hiển thị trong input
   * @example "Nhập thông tin..."
   */
  @ApiProperty({
    description: 'Placeholder hiển thị trong input',
    example: 'Nhập thông tin...',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Placeholder phải là chuỗi' })
  placeholder?: string;
}

/**
 * DTO cho config của dataType = 'text'
 */
export class TextCustomFieldConfigDto extends BaseCustomFieldConfigDto {
  /**
   * Giá trị mặc định
   * @example "Giá trị mặc định"
   */
  @ApiProperty({
    description: '<PERSON>i<PERSON> trị mặc định',
    example: '<PERSON>i<PERSON> trị mặc định',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Gi<PERSON> trị mặc định phải là chuỗi' })
  defaultValue?: string;

  /**
   * Mẫu kiểm tra pattern (regex)
   * @example "^[a-zA-Z0-9]+$"
   */
  @ApiProperty({
    description: 'Mẫu kiểm tra pattern (regex)',
    example: '^[a-zA-Z0-9]+$',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Pattern phải là chuỗi' })
  pattern?: string;

  /**
   * Độ dài tối thiểu
   * @example 1
   */
  @ApiProperty({
    description: 'Độ dài tối thiểu',
    example: 1,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Độ dài tối thiểu phải là số' })
  @Min(0, { message: 'Độ dài tối thiểu không được nhỏ hơn 0' })
  minLength?: number;

  /**
   * Độ dài tối đa
   * @example 255
   */
  @ApiProperty({
    description: 'Độ dài tối đa',
    example: 255,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Độ dài tối đa phải là số' })
  @Min(1, { message: 'Độ dài tối đa không được nhỏ hơn 1' })
  maxLength?: number;
}

/**
 * DTO cho config của dataType = 'number'
 */
export class NumberCustomFieldConfigDto extends BaseCustomFieldConfigDto {
  /**
   * Giá trị mặc định
   * @example 0
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giá trị mặc định phải là số' })
  defaultValue?: number;

  /**
   * Giá trị tối thiểu
   * @example 0
   */
  @ApiProperty({
    description: 'Giá trị tối thiểu',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giá trị tối thiểu phải là số' })
  minValue?: number;

  /**
   * Giá trị tối đa
   * @example 100
   */
  @ApiProperty({
    description: 'Giá trị tối đa',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giá trị tối đa phải là số' })
  maxValue?: number;
}

/**
 * DTO cho config của dataType = 'boolean'
 */
export class BooleanCustomFieldConfigDto extends BaseCustomFieldConfigDto {
  /**
   * Giá trị mặc định
   * @example true
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Giá trị mặc định phải là boolean' })
  defaultValue?: boolean;
}

/**
 * DTO cho config của dataType = 'date'
 */
export class DateCustomFieldConfigDto extends BaseCustomFieldConfigDto {
  /**
   * Giá trị mặc định (ISO date string)
   * @example "2024-01-01"
   */
  @ApiProperty({
    description: 'Giá trị mặc định (ISO date string)',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Giá trị mặc định phải là định dạng ngày hợp lệ (YYYY-MM-DD)' })
  defaultValue?: string;
}

/**
 * DTO cho option trong select
 */
export class SelectOptionDto {
  /**
   * Tiêu đề hiển thị
   * @example "Tùy chọn 1"
   */
  @ApiProperty({
    description: 'Tiêu đề hiển thị',
    example: 'Tùy chọn 1',
  })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  title: string;

  /**
   * Giá trị thực tế
   * @example "option_1"
   */
  @ApiProperty({
    description: 'Giá trị thực tế',
    example: 'option_1',
  })
  @IsString({ message: 'Giá trị phải là chuỗi' })
  value: string;
}

/**
 * DTO cho config của dataType = 'select'
 */
export class SelectCustomFieldConfigDto extends BaseCustomFieldConfigDto {
  /**
   * Danh sách các tùy chọn
   */
  @ApiProperty({
    description: 'Danh sách các tùy chọn',
    type: [SelectOptionDto],
    example: [
      { title: 'Tùy chọn 1', value: 'option_1' },
      { title: 'Tùy chọn 2', value: 'option_2' }
    ],
  })
  @IsArray({ message: 'Options phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 tùy chọn' })
  @ValidateNested({ each: true })
  @Type(() => SelectOptionDto)
  options: SelectOptionDto[];

  /**
   * Giá trị mặc định (phải là một trong các value trong options)
   * @example "option_1"
   */
  @ApiProperty({
    description: 'Giá trị mặc định (phải là một trong các value trong options)',
    example: 'option_1',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giá trị mặc định phải là chuỗi' })
  defaultValue?: string;
}

/**
 * DTO cho config của dataType = 'object'
 */
export class ObjectCustomFieldConfigDto extends BaseCustomFieldConfigDto {
  /**
   * Giá trị mặc định
   * @example {"key": "value"}
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: { key: 'value' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Giá trị mặc định phải là object' })
  defaultValue?: Record<string, any>;
}
