import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * DTO chung cho yêu cầu xóa nhiều items
 */
export class BulkDeleteDto {
  @ApiProperty({
    description: 'Danh sách ID cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID phải là mảng' })
  @ArrayMinSize(1, { message: '<PERSON>ải có ít nhất một item để xóa' })
  @ArrayUnique({ message: 'Danh sách ID không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: 'Danh sách ID không được để trống' })
  ids: number[];
}

/**
 * DTO cho response xóa nhiều items
 */
export class BulkDeleteResponseDto {
  @ApiProperty({
    description: 'Số lượng items đã xóa thành công',
    example: 2
  })
  deletedCount: number;

  @ApiProperty({
    description: 'Số lượng items không thể xóa',
    example: 1
  })
  failedCount: number;

  @ApiProperty({
    description: 'Danh sách ID đã xóa thành công',
    example: [1, 2],
    type: [Number]
  })
  deletedIds: number[];

  @ApiProperty({
    description: 'Danh sách ID không thể xóa',
    example: [3],
    type: [Number]
  })
  failedIds: number[];

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Đã xóa 2 items thành công, 1 item không thể xóa'
  })
  message: string;
}

/**
 * DTO cho audience bulk delete
 */
export class BulkDeleteAudienceDto extends BulkDeleteDto {
  @ApiProperty({
    description: 'Danh sách ID audience cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  declare ids: number[];
}

/**
 * DTO cho tag bulk delete
 */
export class BulkDeleteTagDto extends BulkDeleteDto {
  @ApiProperty({
    description: 'Danh sách ID tag cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  declare ids: number[];
}

/**
 * DTO cho segment bulk delete
 */
export class BulkDeleteSegmentDto extends BulkDeleteDto {
  @ApiProperty({
    description: 'Danh sách ID segment cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  declare ids: number[];
}

/**
 * DTO cho campaign bulk delete
 */
export class BulkDeleteCampaignDto extends BulkDeleteDto {
  @ApiProperty({
    description: 'Danh sách ID campaign cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  declare ids: number[];
}

/**
 * DTO cho template email bulk delete
 */
export class BulkDeleteTemplateEmailDto extends BulkDeleteDto {
  @ApiProperty({
    description: 'Danh sách ID template email cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  declare ids: number[];
}

/**
 * DTO cho custom field bulk delete
 */
export class BulkDeleteCustomFieldDto {
  @ApiProperty({
    description: 'Danh sách ID trường tùy chỉnh cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một item để xóa' })
  @ArrayUnique({ message: 'Danh sách ID không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: 'Danh sách ID không được để trống' })
  customFieldIds: number[];
}
