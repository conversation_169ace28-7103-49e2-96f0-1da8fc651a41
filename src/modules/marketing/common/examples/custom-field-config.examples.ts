/**
 * Examples và test cases cho Custom Field Config
 */

import { CustomFieldDataType } from '../../user/dto/audience-custom-field-definition/create-audience-custom-field-definition.dto';
import {
  TextCustomFieldConfig,
  NumberCustomFieldConfig,
  BooleanCustomFieldConfig,
  DateCustomFieldConfig,
  SelectCustomFieldConfig,
  ObjectCustomFieldConfig,
} from '../interfaces/custom-field-config.interface';

/**
 * Examples cho từng loại dataType
 */
export class CustomFieldConfigExamples {
  
  /**
   * Example cho TEXT config
   */
  static getTextExample(): TextCustomFieldConfig {
    return {
      placeholder: 'Nhập họ và tên đầy đủ...',
      defaultValue: '',
      pattern: '^[a-zA-ZÀ-ỹ\\s]+$',
      minLength: 2,
      maxLength: 100,
    };
  }

  /**
   * Example cho NUMBER config
   */
  static getNumberExample(): NumberCustomFieldConfig {
    return {
      placeholder: 'Nhập tuổi của bạn...',
      defaultValue: 18,
      minValue: 0,
      maxValue: 120,
    };
  }

  /**
   * Example cho BOOLEAN config
   */
  static getBooleanExample(): BooleanCustomFieldConfig {
    return {
      placeholder: 'Đồng ý với điều khoản sử dụng',
      defaultValue: false,
    };
  }

  /**
   * Example cho DATE config
   */
  static getDateExample(): DateCustomFieldConfig {
    return {
      placeholder: 'Chọn ngày sinh của bạn...',
      defaultValue: '2000-01-01',
    };
  }

  /**
   * Example cho SELECT config
   */
  static getSelectExample(): SelectCustomFieldConfig {
    return {
      placeholder: 'Chọn giới tính...',
      options: [
        { title: 'Nam', value: 'male' },
        { title: 'Nữ', value: 'female' },
        { title: 'Khác', value: 'other' },
      ],
      defaultValue: 'male',
    };
  }

  /**
   * Example cho OBJECT config
   */
  static getObjectExample(): ObjectCustomFieldConfig {
    return {
      placeholder: 'Nhập thông tin địa chỉ...',
      defaultValue: {
        street: '',
        city: '',
        district: '',
        country: 'Vietnam',
      },
    };
  }

  /**
   * Lấy tất cả examples
   */
  static getAllExamples() {
    return {
      [CustomFieldDataType.TEXT]: this.getTextExample(),
      [CustomFieldDataType.NUMBER]: this.getNumberExample(),
      [CustomFieldDataType.BOOLEAN]: this.getBooleanExample(),
      [CustomFieldDataType.DATE]: this.getDateExample(),
      [CustomFieldDataType.SELECT]: this.getSelectExample(),
      [CustomFieldDataType.OBJECT]: this.getObjectExample(),
    };
  }
}

/**
 * Real-world examples cho các use cases thực tế
 */
export class RealWorldConfigExamples {
  
  /**
   * Config cho trường "Họ tên"
   */
  static getFullNameConfig(): TextCustomFieldConfig {
    return {
      placeholder: 'Nhập họ và tên đầy đủ',
      defaultValue: '',
      pattern: '^[a-zA-ZÀ-ỹ\\s]+$',
      minLength: 2,
      maxLength: 50,
    };
  }

  /**
   * Config cho trường "Email"
   */
  static getEmailConfig(): TextCustomFieldConfig {
    return {
      placeholder: 'Nhập địa chỉ email',
      defaultValue: '',
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
      maxLength: 255,
    };
  }

  /**
   * Config cho trường "Số điện thoại"
   */
  static getPhoneConfig(): TextCustomFieldConfig {
    return {
      placeholder: 'Nhập số điện thoại (VD: 0912345678)',
      defaultValue: '',
      pattern: '^(0|\\+84)[0-9]{9,10}$',
      minLength: 10,
      maxLength: 12,
    };
  }

  /**
   * Config cho trường "Tuổi"
   */
  static getAgeConfig(): NumberCustomFieldConfig {
    return {
      placeholder: 'Nhập tuổi',
      defaultValue: 18,
      minValue: 0,
      maxValue: 120,
    };
  }

  /**
   * Config cho trường "Lương"
   */
  static getSalaryConfig(): NumberCustomFieldConfig {
    return {
      placeholder: 'Nhập mức lương (VND)',
      minValue: 0,
      maxValue: 1000000000, // 1 tỷ
    };
  }

  /**
   * Config cho trường "Đã kết hôn"
   */
  static getMarriedConfig(): BooleanCustomFieldConfig {
    return {
      placeholder: 'Tình trạng hôn nhân',
      defaultValue: false,
    };
  }

  /**
   * Config cho trường "Ngày sinh"
   */
  static getBirthdateConfig(): DateCustomFieldConfig {
    return {
      placeholder: 'Chọn ngày sinh',
      defaultValue: '2000-01-01',
    };
  }

  /**
   * Config cho trường "Giới tính"
   */
  static getGenderConfig(): SelectCustomFieldConfig {
    return {
      placeholder: 'Chọn giới tính',
      options: [
        { title: 'Nam', value: 'male' },
        { title: 'Nữ', value: 'female' },
        { title: 'Khác', value: 'other' },
      ],
      defaultValue: 'male',
    };
  }

  /**
   * Config cho trường "Trình độ học vấn"
   */
  static getEducationConfig(): SelectCustomFieldConfig {
    return {
      placeholder: 'Chọn trình độ học vấn',
      options: [
        { title: 'Tiểu học', value: 'primary' },
        { title: 'Trung học cơ sở', value: 'secondary' },
        { title: 'Trung học phổ thông', value: 'high_school' },
        { title: 'Cao đẳng', value: 'college' },
        { title: 'Đại học', value: 'university' },
        { title: 'Thạc sĩ', value: 'master' },
        { title: 'Tiến sĩ', value: 'phd' },
      ],
    };
  }

  /**
   * Config cho trường "Địa chỉ"
   */
  static getAddressConfig(): ObjectCustomFieldConfig {
    return {
      placeholder: 'Nhập thông tin địa chỉ',
      defaultValue: {
        street: '',
        ward: '',
        district: '',
        city: '',
        country: 'Vietnam',
        zipCode: '',
      },
    };
  }

  /**
   * Config cho trường "Thông tin liên hệ khẩn cấp"
   */
  static getEmergencyContactConfig(): ObjectCustomFieldConfig {
    return {
      placeholder: 'Nhập thông tin liên hệ khẩn cấp',
      defaultValue: {
        name: '',
        relationship: '',
        phone: '',
        email: '',
      },
    };
  }

  /**
   * Lấy tất cả real-world examples
   */
  static getAllRealWorldExamples() {
    return {
      fullName: this.getFullNameConfig(),
      email: this.getEmailConfig(),
      phone: this.getPhoneConfig(),
      age: this.getAgeConfig(),
      salary: this.getSalaryConfig(),
      married: this.getMarriedConfig(),
      birthdate: this.getBirthdateConfig(),
      gender: this.getGenderConfig(),
      education: this.getEducationConfig(),
      address: this.getAddressConfig(),
      emergencyContact: this.getEmergencyContactConfig(),
    };
  }
}
