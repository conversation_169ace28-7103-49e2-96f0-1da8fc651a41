/**
 * Interface cho các cấu hình config của trường tùy chỉnh
 * Mỗi dataType sẽ có cấu trúc config riêng
 */

/**
 * Base interface cho tất cả config types
 */
export interface BaseCustomFieldConfig {
  /**
   * Placeholder hiển thị trong input
   */
  placeholder?: string;
}

/**
 * Interface cho config của dataType = 'text'
 */
export interface TextCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: string;
  
  /**
   * Mẫu kiểm tra pattern (regex)
   * @example "^[a-zA-Z0-9]+$"
   */
  pattern?: string;
  
  /**
   * Độ dài tối thiểu
   * @minimum 0
   */
  minLength?: number;
  
  /**
   * Độ dài tối đa
   * @minimum 1
   */
  maxLength?: number;
}

/**
 * Interface cho config của dataType = 'number'
 */
export interface NumberCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Gi<PERSON> trị mặc định
   */
  defaultValue?: number;
  
  /**
   * Giá trị tối thiểu
   */
  minValue?: number;
  
  /**
   * Giá trị tối đa
   */
  maxValue?: number;
}

/**
 * Interface cho config của dataType = 'boolean'
 */
export interface BooleanCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: boolean;
}

/**
 * Interface cho config của dataType = 'date'
 */
export interface DateCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   * @example "2024-01-01"
   */
  defaultValue?: string; // ISO date string
}

/**
 * Interface cho option trong select
 */
export interface SelectOption {
  /**
   * Tiêu đề hiển thị
   * @example "Tùy chọn 1"
   */
  title: string;
  
  /**
   * Giá trị thực tế
   * @example "option_1"
   */
  value: string;
}

/**
 * Interface cho config của dataType = 'select'
 */
export interface SelectCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Danh sách các tùy chọn
   */
  options: SelectOption[];
  
  /**
   * Giá trị mặc định (phải là một trong các value trong options)
   */
  defaultValue?: string;
}

/**
 * Interface cho config của dataType = 'object'
 */
export interface ObjectCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: Record<string, any>;
}

/**
 * Union type cho tất cả các config types
 */
export type CustomFieldConfig = 
  | TextCustomFieldConfig
  | NumberCustomFieldConfig
  | BooleanCustomFieldConfig
  | DateCustomFieldConfig
  | SelectCustomFieldConfig
  | ObjectCustomFieldConfig;

/**
 * Type guard functions để kiểm tra loại config
 */
export const isTextConfig = (config: any): config is TextCustomFieldConfig => {
  return typeof config === 'object' && config !== null;
};

export const isNumberConfig = (config: any): config is NumberCustomFieldConfig => {
  return typeof config === 'object' && config !== null;
};

export const isBooleanConfig = (config: any): config is BooleanCustomFieldConfig => {
  return typeof config === 'object' && config !== null;
};

export const isDateConfig = (config: any): config is DateCustomFieldConfig => {
  return typeof config === 'object' && config !== null;
};

export const isSelectConfig = (config: any): config is SelectCustomFieldConfig => {
  return typeof config === 'object' && config !== null && Array.isArray(config.options);
};

export const isObjectConfig = (config: any): config is ObjectCustomFieldConfig => {
  return typeof config === 'object' && config !== null;
};
