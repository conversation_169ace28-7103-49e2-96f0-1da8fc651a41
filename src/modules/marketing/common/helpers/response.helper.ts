import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { HttpStatus } from '@nestjs/common';

/**
 * Bọc dữ liệu trong ApiResponseDto
 * @param data Dữ liệu cần bọc
 * @param message Thông báo (tù<PERSON> chọn)
 * @returns Dữ liệu đã được bọc trong ApiResponseDto
 */
export function wrapResponse<T>(data: T, message?: string): AppApiResponse<T> {
  return AppApiResponse.success(data, message);
}
