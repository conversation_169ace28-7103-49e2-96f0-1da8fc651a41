import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Marketing (13000-13099)
 */
export const MARKETING_ERROR_CODES = {
  // ===== TRƯỜNG TÙY CHỈNH (13000-13009) =====
  /**
   * Lỗi khi không tìm thấy trường tùy chỉnh
   */
  CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    13000,
    'Không tìm thấy trường tùy chỉnh',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi trường tùy chỉnh đã tồn tại
   */
  CUSTOM_FIELD_ALREADY_EXISTS: new ErrorCode(
    13001,
    'Trường tùy chỉnh đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tạo trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    13002,
    'Tạo trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    13003,
    'Cập nhật trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    13004,
    'Xóa trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_DELETE_FAILED: new ErrorCode(
    13005,
    'Xóa trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi validation trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_VALIDATION_FAILED: new ErrorCode(
    13006,
    'Validation trường tùy chỉnh thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi truy vấn trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_QUERY_FAILED: new ErrorCode(
    13007,
    'Truy vấn trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== AUDIENCE (13010-13019) =====
  /**
   * Lỗi khi không tìm thấy audience
   */
  AUDIENCE_NOT_FOUND: new ErrorCode(
    13010,
    'Không tìm thấy khách hàng',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo audience thất bại
   */
  AUDIENCE_CREATION_FAILED: new ErrorCode(
    13011,
    'Tạo khách hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật audience thất bại
   */
  AUDIENCE_UPDATE_FAILED: new ErrorCode(
    13012,
    'Cập nhật khách hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa audience thất bại
   */
  AUDIENCE_DELETION_FAILED: new ErrorCode(
    13013,
    'Xóa khách hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== TAG (13020-13029) =====
  /**
   * Lỗi khi không tìm thấy tag
   */
  TAG_NOT_FOUND: new ErrorCode(
    13020,
    'Không tìm thấy tag',
    HttpStatus.NOT_FOUND,
  ),

  // ===== SEGMENT (13030-13039) =====
  /**
   * Lỗi khi không tìm thấy segment
   */
  SEGMENT_NOT_FOUND: new ErrorCode(
    13030,
    'Không tìm thấy phân khúc',
    HttpStatus.NOT_FOUND,
  ),

  // ===== CAMPAIGN (13040-13049) =====
  /**
   * Lỗi khi không tìm thấy campaign
   */
  CAMPAIGN_NOT_FOUND: new ErrorCode(
    13040,
    'Không tìm thấy chiến dịch',
    HttpStatus.NOT_FOUND,
  ),

  // ===== TEMPLATE (13050-13059) =====
  /**
   * Lỗi khi không tìm thấy template
   */
  TEMPLATE_NOT_FOUND: new ErrorCode(
    13050,
    'Không tìm thấy mẫu',
    HttpStatus.NOT_FOUND,
  ),
};
