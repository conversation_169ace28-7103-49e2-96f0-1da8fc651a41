import { registerDecorator, ValidationOptions, ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { parsePhoneNumber, isValidPhoneNumber } from 'libphonenumber-js';

@ValidatorConstraint({ name: 'isPhoneWithCountry', async: false })
export class IsPhoneWithCountryConstraint implements ValidatorConstraintInterface {
  validate(phone: string, args: ValidationArguments) {
    if (!phone) {
      return true; // Let @IsOptional handle empty values
    }

    const object = args.object as any;
    const countryCode = object.countryCode;

    if (!countryCode) {
      return false; // Country code is required when phone is provided
    }

    try {
      // Remove + from country code to get the numeric part
      const numericCountryCode = countryCode.replace('+', '');
      
      // Combine country code with phone number
      const fullPhoneNumber = `+${numericCountryCode}${phone.replace(/^0+/, '')}`;
      
      // Validate using libphonenumber-js
      return isValidPhoneNumber(fullPhoneNumber);
    } catch (error) {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments) {
    return 'Số điện thoại không hợp lệ với mã quốc gia được cung cấp';
  }
}

export function IsPhoneWithCountry(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsPhoneWithCountryConstraint,
    });
  };
}
