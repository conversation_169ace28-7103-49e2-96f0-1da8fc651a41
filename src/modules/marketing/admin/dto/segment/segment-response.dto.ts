import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi segment
 */
export class SegmentResponseDto {
  /**
   * ID của segment
   * @example 1
   */
  @ApiProperty({
    description: 'ID của segment',
    example: 1,
  })
  id: number;

  /**
   * Tên segment
   * @example "Khách hàng VIP"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng VIP',
  })
  name: string;

  /**
   * <PERSON>ô tả segment
   * @example "Khách hàng có tổng chi tiêu trên 10 triệu"
   */
  @ApiProperty({
    description: 'Mô tả segment',
    example: 'Khách hàng có tổng chi tiêu trên 10 triệu',
  })
  description: string;

  /**
   * Tiêu chí lọc khách hàng
   */
  @ApiProperty({
    description: 'Tiêu chí lọc khách hàng',
    example: {
      conditions: [
        { field: 'totalSpent', operator: 'greaterThan', value: 10000000 }
      ],
      operator: 'AND'
    },
  })
  criteria: any;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;

  /**
   * Số lượng khách hàng trong segment
   * @example 150
   */
  @ApiProperty({
    description: 'Số lượng khách hàng trong segment',
    example: 150,
  })
  audienceCount?: number;
}
