import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, Matches } from 'class-validator';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

// Re-export enum để backward compatibility
export { CustomFieldDataType };

/**
 * DTO cho việc tạo trường tùy chỉnh
 */
export class CreateAudienceCustomFieldDefinitionDto {
  /**
   * Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)
   * @example "customer_address"
   */
  @ApiProperty({
    description: 'Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)',
    example: 'customer_address',
  })
  @IsNotEmpty({ message: 'Định danh không được để trống' })
  @IsString({ message: '<PERSON><PERSON><PERSON> danh phải là chuỗi' })
  @Matches(/^[a-z0-9_]+$/, { message: 'Định danh chỉ được chứa chữ thường, số và dấu gạch dưới' })
  fieldKey: string;

  /**
   * Tên hiển thị thân thiện với admin
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với admin',
    example: 'Địa chỉ khách hàng',
  })
  @IsNotEmpty({ message: 'Tên hiển thị không được để trống' })
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName: string;

  /**
   * Kiểu dữ liệu: text, number, boolean, date, select, object
   * @example "text"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.TEXT,
  })
  @IsNotEmpty({ message: 'Kiểu dữ liệu không được để trống' })
  @IsEnum(CustomFieldDataType, {
    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,
  })
  dataType: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   * @example "Địa chỉ liên hệ của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
    example: 'Địa chỉ liên hệ của khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Danh sách tags để phân loại trường tùy chỉnh
   * @example ["contact", "personal"]
   */
  @ApiProperty({
    description: 'Danh sách tags để phân loại trường tùy chỉnh',
    example: ['contact', 'personal'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];

  /**
   * Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)
   * @example {"required": true, "maxLength": 255, "options": ["option1", "option2"]}
   */
  @ApiProperty({
    description: 'Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)',
    example: { required: true, maxLength: 255, options: ['option1', 'option2'] },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Config phải là object' })
  config?: Record<string, any>;
}
