import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldDataType } from './create-audience-custom-field-definition.dto';

/**
 * DTO cho phản hồi thông tin trường tùy chỉnh
 */
export class AudienceCustomFieldDefinitionResponseDto {
  /**
   * Định danh duy nhất cho trường tùy chỉnh
   * @example "customer_address"
   */
  @ApiProperty({
    description: 'Định danh duy nhất cho trường tùy chỉnh',
    example: 'customer_address',
  })
  fieldKey: string;

  /**
   * ID của admin mà trường tùy chỉnh này thuộc về
   * @example 1
   */
  @ApiProperty({
    description: 'ID của admin mà trường tùy chỉnh này thuộc về',
    example: 1,
  })
  createdBy: number;

  /**
   * Tên hiển thị thân thiện với admin
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với admin',
    example: 'Địa chỉ khách hàng',
  })
  displayName: string;

  /**
   * Kiểu dữ liệu
   * @example "text"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.TEXT,
  })
  dataType: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   * @example "Địa chỉ liên hệ của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
    example: 'Địa chỉ liên hệ của khách hàng',
    nullable: true,
  })
  description: string | null;

  /**
   * Danh sách tags để phân loại trường tùy chỉnh
   * @example ["contact", "personal"]
   */
  @ApiProperty({
    description: 'Danh sách tags để phân loại trường tùy chỉnh',
    example: ['contact', 'personal'],
    type: [String],
  })
  tags: string[];

  /**
   * Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)
   * @example {"required": true, "maxLength": 255, "options": ["option1", "option2"]}
   */
  @ApiProperty({
    description: 'Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)',
    example: { required: true, maxLength: 255, options: ['option1', 'option2'] },
  })
  config: Record<string, any>;
}
