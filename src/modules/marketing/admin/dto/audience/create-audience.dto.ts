import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, Matches } from 'class-validator';
import { CreateCustomFieldDto } from './create-custom-field.dto';
import { Type } from 'class-transformer';
import { IsPhoneWithCountry } from '../../validators/phone-with-country.validator';

/**
 * DTO cho tạo audience
 */
export class CreateAudienceDto {
  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  /**
   * Số điện thoại của khách hàng
   * @example "912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsPhoneWithCountry({ message: 'Số điện thoại không hợp lệ với mã quốc gia được cung cấp' })
  phone?: string;

  /**
   * Mã quốc gia của số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: '+84',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã quốc gia phải là chuỗi' })
  @Matches(/^\+\d{1,4}$/, { message: 'Mã quốc gia không hợp lệ (ví dụ: +84, +1)' })
  countryCode?: string;

  /**
   * Các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Các tag ID
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Các tag ID',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
