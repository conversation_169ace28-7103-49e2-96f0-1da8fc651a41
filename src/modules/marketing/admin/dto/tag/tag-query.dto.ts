import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query parameters khi lấy danh sách tag
 */
export class TagQueryDto extends QueryDto {
  /**
   * Tìm kiếm tổng hợp theo tên hoặc ID tag (override từ QueryDto)
   * @example "VIP" hoặc "123"
   */
  @ApiProperty({
    description: 'Tìm kiếm tổng hợp theo tên hoặc ID tag',
    example: 'VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  declare search?: string;

  /**
   * Tìm kiếm theo tên (deprecated - sử dụng search thay thế)
   * @example "VIP"
   * @deprecated Sử dụng search thay thế
   */
  @ApiProperty({
    description: 'Tì<PERSON> kiếm theo tên (deprecated - sử dụng search thay thế)',
    example: 'VIP',
    required: false,
    deprecated: true,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;
}
