import { Entity, Column, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_audience_has_tags trong cơ sở dữ liệu
 * Bảng trung gian để lưu trữ mối quan hệ nhiều-nhiều giữa admin_audience và admin_tags
 */
@Entity('admin_audience_has_tags')
export class AdminAudienceHasTag {
  /**
   * ID của audience
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng admin_audience
   */
  @PrimaryColumn({ name: 'audience_id', type: 'bigint' })
  audienceId: number;

  /**
   * ID của tag
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng admin_tags
   */
  @PrimaryColumn({ name: 'tag_id', type: 'bigint' })
  tagId: number;

  // Không sử dụng quan hệ với các bảng kh<PERSON>c, chỉ lưu ID
}
