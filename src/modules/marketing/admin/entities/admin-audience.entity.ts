import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_audience trong cơ sở dữ liệu
 * Bảng khách hàng của admin
 */
@Entity('admin_audience')
export class AdminAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên của khách hàng
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên khách hàng' })
  name: string;

  /**
   * Email của khách hàng
   */
  @Column({ name: 'email', length: 255, nullable: true, comment: 'Email người dùng' })
  email: string;

  /**
   * Số điện thoại của khách hàng
   */
  @Column({ name: 'phone', length: 20, nullable: true, comment: '<PERSON><PERSON> điện thoại' })
  phone: string;

  /**
   * <PERSON>ã quốc gia của số điện thoại
   */
  @Column({ name: 'country_code', length: 10, nullable: true, default: '+84', comment: 'M<PERSON> quốc gia của số điện thoại' })
  countryCode: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Ngày cập nhật' })
  updatedAt: number;
}
