import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { AdminAudienceCustomFieldDefinitionService } from '../services/admin-audience-custom-field-definition.service';
import {
  CreateAudienceCustomFieldDefinitionDto,
  UpdateAudienceCustomFieldDefinitionDto,
  AudienceCustomFieldDefinitionResponseDto,
  AudienceCustomFieldDefinitionQueryDto,
} from '../dto/audience-custom-field-definition';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { CustomFieldConfigUtil } from '@modules/marketing/common/utils/custom-field-config.util';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * Controller xử lý các API liên quan đến trường tùy chỉnh của admin
 */
@ApiTags(SWAGGER_API_TAGS.MARKETING_ADMIN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/marketing/audience-custom-fields')
export class AdminAudienceCustomFieldDefinitionController {
  constructor(private readonly customFieldService: AdminAudienceCustomFieldDefinitionService) {}

  /**
   * Tạo mới trường tùy chỉnh
   * @param employee Thông tin admin
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo mới trường tùy chỉnh',
    description: `
Tạo mới trường tùy chỉnh với validation config theo dataType. Config sẽ được validate tự động theo loại dữ liệu được chọn.

## Cấu trúc Config theo DataType:

### TEXT (dataType = "text")
\`\`\`json
{
  "fieldKey": "customer_name",
  "displayName": "Tên khách hàng",
  "dataType": "text",
  "description": "Tên đầy đủ của khách hàng",
  "tags": ["customer", "personal"],
  "config": {
    "placeholder": "Nhập tên khách hàng...",
    "defaultValue": "",
    "pattern": "^[a-zA-ZÀ-ỹ\\\\s]+$",
    "minLength": 2,
    "maxLength": 100
  }
}
\`\`\`

### NUMBER (dataType = "number")
\`\`\`json
{
  "fieldKey": "customer_age",
  "displayName": "Tuổi khách hàng",
  "dataType": "number",
  "description": "Tuổi của khách hàng",
  "config": {
    "placeholder": "Nhập tuổi...",
    "defaultValue": 25,
    "minValue": 0,
    "maxValue": 120
  }
}
\`\`\`

### BOOLEAN (dataType = "boolean")
\`\`\`json
{
  "fieldKey": "is_vip",
  "displayName": "Khách hàng VIP",
  "dataType": "boolean",
  "config": {
    "placeholder": "Trạng thái VIP",
    "defaultValue": false
  }
}
\`\`\`

### DATE (dataType = "date")
\`\`\`json
{
  "fieldKey": "registration_date",
  "displayName": "Ngày đăng ký",
  "dataType": "date",
  "config": {
    "placeholder": "Chọn ngày đăng ký...",
    "defaultValue": "2024-01-01"
  }
}
\`\`\`

### SELECT (dataType = "select")
\`\`\`json
{
  "fieldKey": "customer_type",
  "displayName": "Loại khách hàng",
  "dataType": "select",
  "config": {
    "placeholder": "Chọn loại khách hàng...",
    "options": [
      { "title": "Cá nhân", "value": "individual" },
      { "title": "Doanh nghiệp", "value": "business" },
      { "title": "Đối tác", "value": "partner" }
    ],
    "defaultValue": "individual"
  }
}
\`\`\`

### OBJECT (dataType = "object")
\`\`\`json
{
  "fieldKey": "company_info",
  "displayName": "Thông tin công ty",
  "dataType": "object",
  "config": {
    "placeholder": "Nhập thông tin công ty...",
    "defaultValue": {
      "name": "",
      "taxCode": "",
      "address": "",
      "phone": ""
    }
  }
}
\`\`\`

## Lưu ý:
- Nếu không cung cấp config, hệ thống sẽ tự động tạo config mặc định
- Config sẽ được validate theo dataType đã chọn
- Sử dụng endpoint GET /config-examples để lấy cấu trúc config mẫu
    `
  })
  @ApiResponse({
    status: 201,
    description: 'Trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
    examples: {
      'text-field': {
        summary: 'Tạo trường TEXT thành công',
        value: {
          success: true,
          message: 'Tạo trường tùy chỉnh thành công',
          data: {
            fieldKey: 'customer_name',
            createdBy: 456,
            displayName: 'Tên khách hàng',
            dataType: 'text',
            description: 'Tên đầy đủ của khách hàng',
            tags: ['customer', 'personal'],
            config: {
              placeholder: 'Nhập tên khách hàng...',
              defaultValue: '',
              pattern: '^[a-zA-ZÀ-ỹ\\s]+$',
              minLength: 2,
              maxLength: 100
            }
          }
        }
      },
      'number-field': {
        summary: 'Tạo trường NUMBER thành công',
        value: {
          success: true,
          message: 'Tạo trường tùy chỉnh thành công',
          data: {
            fieldKey: 'customer_age',
            createdBy: 456,
            displayName: 'Tuổi khách hàng',
            dataType: 'number',
            description: 'Tuổi của khách hàng',
            tags: ['customer'],
            config: {
              placeholder: 'Nhập tuổi...',
              defaultValue: 25,
              minValue: 0,
              maxValue: 120
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Config không hợp lệ cho dataType đã chọn',
    examples: {
      'invalid-number-config': {
        summary: 'Config NUMBER không hợp lệ',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Giá trị tối thiểu phải là số',
            'Giá trị tối đa phải lớn hơn giá trị tối thiểu'
          ],
          dataType: 'number',
          receivedConfig: {
            minValue: 'invalid',
            maxValue: 10,
            defaultValue: 20
          }
        }
      },
      'invalid-object-config': {
        summary: 'Config OBJECT không hợp lệ',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Giá trị mặc định phải là object'
          ],
          dataType: 'object',
          receivedConfig: {
            defaultValue: 'not an object'
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
  )
  async create(
    @CurrentEmployee() employee: JwtPayload,
    @Body() createDto: CreateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    // Validate config theo dataType nếu có config
    if (createDto.config && Object.keys(createDto.config).length > 0) {
      const configErrors = await CustomFieldConfigUtil.validateConfig(
        createDto.dataType,
        createDto.config
      );

      if (configErrors.length > 0) {
        throw new BadRequestException({
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: configErrors,
          dataType: createDto.dataType,
          receivedConfig: createDto.config
        });
      }
    } else {
      // Nếu không có config, sử dụng config mặc định
      createDto.config = CustomFieldConfigUtil.getDefaultConfig(createDto.dataType);
    }

    const result = await this.customFieldService.create(employee.id, createDto);
    return wrapResponse(result, 'Tạo trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param employee Thông tin admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Put(':fieldKey')
  @ApiOperation({
    summary: 'Cập nhật trường tùy chỉnh',
    description: `
Cập nhật trường tùy chỉnh với validation config theo dataType. Nếu thay đổi dataType, config sẽ được validate lại.

## Các trường có thể cập nhật:

### Thông tin cơ bản:
- \`displayName\`: Tên hiển thị
- \`description\`: Mô tả
- \`tags\`: Danh sách tags
- \`dataType\`: Loại dữ liệu (nếu thay đổi, config sẽ được validate lại)
- \`config\`: Cấu hình chi tiết theo dataType

## Examples cập nhật cho Admin:

### Cập nhật Customer Type SELECT field:
\`\`\`json
{
  "displayName": "Loại khách hàng",
  "description": "Phân loại khách hàng theo tính chất (đã cập nhật)",
  "config": {
    "placeholder": "Chọn loại khách hàng...",
    "options": [
      { "title": "Cá nhân", "value": "individual" },
      { "title": "Doanh nghiệp nhỏ", "value": "small_business" },
      { "title": "Doanh nghiệp vừa", "value": "medium_business" },
      { "title": "Doanh nghiệp lớn", "value": "large_business" },
      { "title": "Đối tác chiến lược", "value": "strategic_partner" },
      { "title": "Nhà cung cấp", "value": "supplier" }
    ],
    "defaultValue": "individual"
  }
}
\`\`\`

### Cập nhật Company Revenue NUMBER field:
\`\`\`json
{
  "displayName": "Doanh thu công ty (VND)",
  "description": "Doanh thu hàng năm của công ty",
  "config": {
    "placeholder": "Nhập doanh thu hàng năm...",
    "defaultValue": 1000000000,
    "minValue": 0,
    "maxValue": 1000000000000
  }
}
\`\`\`

### Thay đổi từ TEXT sang OBJECT cho Company Info:
\`\`\`json
{
  "dataType": "object",
  "displayName": "Thông tin công ty chi tiết",
  "config": {
    "placeholder": "Nhập thông tin công ty...",
    "defaultValue": {
      "name": "",
      "taxCode": "",
      "address": "",
      "phone": "",
      "email": "",
      "website": "",
      "industry": "",
      "employeeCount": 0,
      "foundedYear": 2000
    }
  }
}
\`\`\`

### Cập nhật Business Status BOOLEAN:
\`\`\`json
{
  "displayName": "Đang hoạt động",
  "description": "Trạng thái hoạt động của doanh nghiệp",
  "config": {
    "placeholder": "Trạng thái hoạt động",
    "defaultValue": true
  }
}
\`\`\`

## Lưu ý:
- Chỉ cần gửi các trường muốn cập nhật
- Nếu thay đổi dataType, config sẽ được validate theo dataType mới
- Config sẽ được merge với config hiện tại nếu không thay đổi dataType
- Admin có thể cập nhật tất cả custom fields trong hệ thống
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
    examples: {
      'update-customer-type': {
        summary: 'Cập nhật Customer Type SELECT field',
        value: {
          success: true,
          message: 'Cập nhật trường tùy chỉnh thành công',
          data: {
            fieldKey: 'customer_type',
            createdBy: 456,
            displayName: 'Loại khách hàng',
            dataType: 'select',
            description: 'Phân loại khách hàng theo tính chất (đã cập nhật)',
            tags: ['business', 'classification'],
            config: {
              placeholder: 'Chọn loại khách hàng...',
              options: [
                { title: 'Cá nhân', value: 'individual' },
                { title: 'Doanh nghiệp nhỏ', value: 'small_business' },
                { title: 'Doanh nghiệp vừa', value: 'medium_business' },
                { title: 'Doanh nghiệp lớn', value: 'large_business' },
                { title: 'Đối tác chiến lược', value: 'strategic_partner' },
                { title: 'Nhà cung cấp', value: 'supplier' }
              ],
              defaultValue: 'individual'
            },
            updatedAt: 1703123456
          }
        }
      },
      'update-revenue-field': {
        summary: 'Cập nhật Company Revenue NUMBER field',
        value: {
          success: true,
          message: 'Cập nhật trường tùy chỉnh thành công',
          data: {
            fieldKey: 'company_revenue',
            createdBy: 456,
            displayName: 'Doanh thu công ty (VND)',
            dataType: 'number',
            description: 'Doanh thu hàng năm của công ty',
            tags: ['business', 'financial'],
            config: {
              placeholder: 'Nhập doanh thu hàng năm...',
              defaultValue: 1000000000,
              minValue: 0,
              maxValue: 1000000000000
            },
            updatedAt: 1703123456
          }
        }
      },
      'change-to-object': {
        summary: 'Thay đổi từ TEXT sang OBJECT',
        value: {
          success: true,
          message: 'Cập nhật trường tùy chỉnh thành công',
          data: {
            fieldKey: 'company_info',
            createdBy: 456,
            displayName: 'Thông tin công ty chi tiết',
            dataType: 'object',
            description: 'Thông tin chi tiết về công ty',
            config: {
              placeholder: 'Nhập thông tin công ty...',
              defaultValue: {
                name: '',
                taxCode: '',
                address: '',
                phone: '',
                email: '',
                website: '',
                industry: '',
                employeeCount: 0,
                foundedYear: 2000
              }
            },
            updatedAt: 1703123456
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Config không hợp lệ cho dataType đã chọn',
    examples: {
      'invalid-object-config': {
        summary: 'Config OBJECT không hợp lệ',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Giá trị mặc định phải là object'
          ],
          dataType: 'object',
          receivedConfig: {
            defaultValue: 'not_an_object'
          }
        }
      },
      'invalid-business-select': {
        summary: 'Config SELECT không hợp lệ cho business',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Giá trị mặc định phải là một trong các value trong options'
          ],
          dataType: 'select',
          receivedConfig: {
            options: [
              { title: 'Option 1', value: 'opt1' },
              { title: 'Option 2', value: 'opt2' }
            ],
            defaultValue: 'invalid_option'
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async update(
    @CurrentEmployee() employee: JwtPayload,
    @Param('fieldKey') fieldKey: string,
    @Body() updateDto: UpdateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    // Nếu có cập nhật dataType hoặc config, cần validate
    if (updateDto.dataType || (updateDto.config && Object.keys(updateDto.config).length > 0)) {
      // Lấy thông tin hiện tại để biết dataType
      const currentField = await this.customFieldService.findOne(employee.id, fieldKey);
      const dataTypeToValidate = updateDto.dataType || currentField.dataType;
      const configToValidate = updateDto.config || currentField.config;

      // Validate config theo dataType
      if (configToValidate && Object.keys(configToValidate).length > 0) {
        const configErrors = await CustomFieldConfigUtil.validateConfig(
          dataTypeToValidate,
          configToValidate
        );

        if (configErrors.length > 0) {
          throw new BadRequestException({
            message: 'Config không hợp lệ cho dataType đã chọn',
            errors: configErrors,
            dataType: dataTypeToValidate,
            receivedConfig: configToValidate
          });
        }
      }
    }

    const result = await this.customFieldService.update(fieldKey, updateDto);
    return wrapResponse(result, 'Cập nhật trường tùy chỉnh thành công');
  }

  /**
   * Xóa trường tùy chỉnh
   * @param employee Thông tin admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Delete(':fieldKey')
  @ApiOperation({ summary: 'Xóa trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
  )
  async delete(
    @CurrentEmployee() employee: JwtPayload,
    @Param('fieldKey') fieldKey: string,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.delete(employee.id, fieldKey);
    return wrapResponse(result, 'Xóa trường tùy chỉnh thành công');
  }

  /**
   * Lấy thông tin trường tùy chỉnh
   * @param employee Thông tin admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh
   */
  @Get(':fieldKey')
  @ApiOperation({ summary: 'Lấy thông tin trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND)
  async findOne(
    @CurrentEmployee() employee: JwtPayload,
    @Param('fieldKey') fieldKey: string,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.findOne(employee.id, fieldKey);
    return wrapResponse(result);
  }

  /**
   * Lấy config examples cho từng dataType
   * @returns Config examples cho tất cả dataType
   */
  @Get('config-examples')
  @ApiOperation({
    summary: 'Lấy config examples cho từng dataType',
    description: 'Trả về cấu trúc config mẫu cho từng loại dữ liệu để frontend tham khảo'
  })
  @ApiResponse({
    status: 200,
    description: 'Config examples cho tất cả dataType',
    schema: {
      type: 'object',
      properties: {
        text: { type: 'object' },
        number: { type: 'object' },
        boolean: { type: 'object' },
        date: { type: 'object' },
        select: { type: 'object' },
        object: { type: 'object' }
      }
    }
  })
  async getConfigExamples(): Promise<ApiResponseDto<Record<string, any>>> {
    const examples = {
      [CustomFieldDataType.TEXT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.TEXT),
      [CustomFieldDataType.NUMBER]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.NUMBER),
      [CustomFieldDataType.BOOLEAN]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.BOOLEAN),
      [CustomFieldDataType.DATE]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.DATE),
      [CustomFieldDataType.SELECT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.SELECT),
      [CustomFieldDataType.OBJECT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.OBJECT),
    };

    return wrapResponse(examples, 'Lấy config examples thành công');
  }

  /**
   * Lấy danh sách trường tùy chỉnh
   * @param employee Thông tin admin
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh',
    schema: ApiResponseDto.getPaginatedSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  async findAll(
    @Query() queryDto: AudienceCustomFieldDefinitionQueryDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<AudienceCustomFieldDefinitionResponseDto>>> {
    const result = await this.customFieldService.findAll(queryDto);
    return wrapResponse(result);
  }
}
