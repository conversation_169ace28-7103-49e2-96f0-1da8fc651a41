import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { AdminTagService } from '../services/admin-tag.service';
import { CreateTagDto, UpdateTagDto, TagResponseDto, TagQueryDto } from '../dto/tag';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { BulkDeleteTagDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto';

/**
 * Controller cho AdminTag
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TAG)
@Controller('admin/marketing/tags')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth()
export class AdminTagController {
  constructor(private readonly adminTagService: AdminTagService) {}

  /**
   * Tạo tag mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo tag mới' })
  @ApiResponse({ status: 201, description: 'Tag đã tạo', type: TagResponseDto })
  async create(
    @CurrentEmployee() employee: JwtPayload,
    @Body() createTagDto: CreateTagDto,
  ): Promise<AppApiResponse<TagResponseDto>> {
    const result = await this.adminTagService.create(employee.id, createTagDto);
    return wrapResponse(result, 'Tag đã được tạo thành công');
  }

  /**
   * Lấy danh sách tag với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tag với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tag với phân trang',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              allOf: [
                { $ref: '#/components/schemas/PaginatedResult' },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/TagResponseDto' }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentEmployee() employee: JwtPayload,
    @Query() query: TagQueryDto
  ): Promise<AppApiResponse<PaginatedResult<TagResponseDto>>> {
    const result = await this.adminTagService.findAllPaginated(employee.id, query);
    return wrapResponse(result, 'Danh sách tag');
  }

  /**
   * Lấy tag theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy tag theo ID' })
  @ApiResponse({ status: 200, description: 'Tag', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag không tồn tại' })
  async findOne(
    @CurrentEmployee() employee: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<TagResponseDto>> {
    const result = await this.adminTagService.findOne(employee.id, +id);
    return wrapResponse(result, 'Thông tin tag');
  }

  /**
   * Cập nhật tag
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật tag' })
  @ApiResponse({ status: 200, description: 'Tag đã cập nhật', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag không tồn tại' })
  async update(
    @CurrentEmployee() employee: JwtPayload,
    @Param('id') id: string,
    @Body() updateTagDto: UpdateTagDto,
  ): Promise<AppApiResponse<TagResponseDto>> {
    const result = await this.adminTagService.update(employee.id, +id, updateTagDto);
    return wrapResponse(result, 'Tag đã được cập nhật thành công');
  }

  /**
   * Xóa tag
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tag' })
  @ApiResponse({ status: 200, description: 'true nếu xóa thành công', type: Boolean })
  @ApiResponse({ status: 404, description: 'Tag không tồn tại' })
  async remove(
    @CurrentEmployee() employee: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<boolean>> {
    const result = await this.adminTagService.remove(employee.id, +id);
    return wrapResponse(result, 'Tag đã được xóa thành công');
  }

  /**
   * Xóa nhiều tag
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều tag' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tag thành công',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({
    status: 207,
    description: 'Một số tag không thể xóa',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tag' })
  async bulkDelete(
    @CurrentEmployee() employee: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteTagDto
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.adminTagService.bulkDelete(employee.id, bulkDeleteDto.ids);
    return wrapResponse(result, result.message);
  }
}
