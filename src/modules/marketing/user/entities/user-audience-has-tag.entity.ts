import { Entity, Column, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_audience_has_tags trong cơ sở dữ liệu
 * Bảng trung gian để lưu trữ mối quan hệ nhiều-nhiều giữa user_audience và user_tags
 */
@Entity('user_audience_has_tags')
export class UserAudienceHasTag {
  /**
   * ID của audience
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng user_audience
   */
  @PrimaryColumn({ name: 'audience_id', type: 'bigint' })
  audienceId: number;

  /**
   * ID của tag
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng user_tags
   */
  @PrimaryColumn({ name: 'tag_id', type: 'bigint' })
  tagId: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
