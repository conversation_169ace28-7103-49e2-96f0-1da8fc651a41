import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_audience_custom_fields trong cơ sở dữ liệu
 * Bảng danh sách trường tùy chỉnh của audience
 */
@Entity('user_audience_custom_fields')
export class UserAudienceCustomField {
  /**
   * ID của custom field
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của audience
   */
  @Column({ name: 'audience_id', nullable: true })
  audienceId: number;

  // Không sử dụng quan hệ với bảng UserAudience, chỉ lưu ID

  /**
   * ID tham chiếu đến định nghĩa trường tùy chỉnh
   */
  @Column({ name: 'field_id', type: 'bigint', nullable: false, comment: 'ID tham chiếu đến định nghĩa trường' })
  fieldId: number;

  // Không sử dụng quan hệ với bảng UserAudienceCustomFieldDefinition, chỉ lưu ID

  /**
   * Gi<PERSON> trị của trường tùy chỉnh
   */
  @Column({ name: 'field_value', type: 'jsonb', nullable: true, comment: 'Giá trị của trường' })
  fieldValue: any;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
