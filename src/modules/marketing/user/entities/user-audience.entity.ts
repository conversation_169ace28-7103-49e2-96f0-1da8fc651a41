import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_audience trong cơ sở dữ liệu
 * Bảng khách hàng của người dùng
 */
@Entity('user_audience')
export class UserAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Mã khách hàng' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tên của khách hàng
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên khách hàng' })
  name: string;

  /**
   * Email của khách hàng
   */
  @Column({ name: 'email', length: 255, nullable: true, comment: 'Email người dùng' })
  email: string;

  /**
   * <PERSON><PERSON> điện thoại của khách hàng
   */
  @Column({ name: 'phone', length: 20, nullable: true, comment: 'Số điện thoại' })
  phone: string;

  /**
   * Mã quốc gia của số điện thoại
   */
  @Column({ name: 'country_code', length: 10, nullable: true, default: '+84', comment: 'Mã quốc gia của số điện thoại' })
  countryCode: string;

  /**
   * URL avatar của khách hàng (S3 key)
   */
  @Column({ name: 'avatar',type: 'varchar', length: 500, nullable: true, comment: 'URL avatar của khách hàng' })
  avatar: string | null;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Ngày cập nhật' })
  updatedAt: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
