import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_template_email trong cơ sở dữ liệu
 * Bảng template email của người dùng
 */
@Entity('user_template_email')
export class UserTemplateEmail {
  /**
   * ID của template
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tên template
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên email' })
  name: string;

  /**
   * Tiêu đề email
   */
  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Tiêu đề email' })
  subject: string;

  /**
   * Nội dung HTML email
   */
  @Column({ name: 'content', type: 'text', nullable: true, comment: 'Nội dung HTML email' })
  htmlContent: string;

  /**
   * Nội dung HTML email (backward compatibility field - không map với database)
   * Field này được thêm trong runtime để backward compatibility
   */
  content?: string;

  /**
   * Nội dung text thuần (tùy chọn)
   */
  @Column({ name: 'text_content', type: 'text', nullable: true, comment: 'Nội dung text thuần' })
  textContent?: string;

  /**
   * Loại template
   */
  @Column({
    name: 'type',
    length: 50,
    nullable: true,
    default: 'NEWSLETTER',
    comment: 'Loại template (NEWSLETTER, PROMOTIONAL, TRANSACTIONAL, WELCOME, ABANDONED_CART, FOLLOW_UP)'
  })
  type?: string;

  /**
   * Preview text hiển thị trong inbox
   */
  @Column({ name: 'preview_text', length: 255, nullable: true, comment: 'Preview text hiển thị trong inbox' })
  previewText?: string;

  /**
   * Các tag của template
   */
  @Column({ name: 'tags', type: 'jsonb', nullable: true, comment: 'Nhãn cho email' })
  tags: string[];

  /**
   * Các placeholder trong template (tên biến)
   */
  @Column({ name: 'placeholders', type: 'json', nullable: true, comment: 'Danh sách tên biến' })
  placeholders: string[];

  /**
   * Metadata chi tiết của biến
   */
  @Column({ name: 'variable_metadata', type: 'jsonb', nullable: true, comment: 'Metadata chi tiết của biến' })
  variableMetadata?: Record<string, {
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  }>;

  /**
   * Trạng thái template
   */
  @Column({
    name: 'status',
    length: 20,
    nullable: true,
    default: 'DRAFT',
    comment: 'Trạng thái template (DRAFT, ACTIVE, ARCHIVED)'
  })
  status: 'DRAFT' | 'ACTIVE' | 'ARCHIVED';

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
