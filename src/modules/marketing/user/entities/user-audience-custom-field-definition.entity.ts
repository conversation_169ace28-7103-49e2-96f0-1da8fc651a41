import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * Entity đại diện cho bảng audience_user_custom_fields trong cơ sở dữ liệu
 * Lưu thông tin các trường tùy chỉnh mà người dùng có thể định nghĩa động
 */
@Entity('audience_user_custom_fields')
export class UserAudienceCustomFieldDefinition {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)
   */
  @Column({ name: 'field_key', length: 100 })
  fieldKey: string;

  /**
   * ID của người dùng mà trường tùy chỉnh này thuộc về
   */
  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  /**
   * Tên hiển thị thân thiện với người dùng
   */
  @Column({ name: 'display_name', length: 255 })
  displayName: string;

  /**
   * Kiểu dữ liệu: text, number, boolean, date, select, object
   */
  @Column({
    name: 'data_type',
    type: 'enum',
    enum: CustomFieldDataType
  })
  dataType: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  /**
   * Danh sách tags để phân loại trường tùy chỉnh
   */
  @Column({ name: 'tags', type: 'jsonb', default: '[]' })
  tags: string[];

  /**
   * Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)
   */
  @Column({ name: 'config', type: 'jsonb', default: '{}' })
  config: Record<string, any>;
}
