-- Migration: <PERSON>h<PERSON><PERSON> các trường mới cho user_template_email
-- Date: 2024-01-01
-- Description: Thê<PERSON> các trường text_content, type, preview_text, variable_metadata để hỗ trợ tính năng template email nâng cao

-- 1. Thêm cột text_content (nội dung text thuần)
ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS text_content TEXT;

-- 2. Thêm cột type (loại template)
ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS type VARCHAR(50) DEFAULT 'NEWSLETTER';

-- 3. Thêm cột preview_text (preview text hiển thị trong inbox)
ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS preview_text VARCHAR(255);

-- 4. Thêm cột variable_metadata (metadata chi tiết của biến)
ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS variable_metadata JSONB;

-- 5. <PERSON><PERSON><PERSON> nhật comment cho các cột
COMMENT ON COLUMN user_template_email.text_content IS 'Nội dung text thuần của email';
COMMENT ON COLUMN user_template_email.type IS 'Loại template (NEWSLETTER, PROMOTIONAL, TRANSACTIONAL, WELCOME, ABANDONED_CART, FOLLOW_UP)';
COMMENT ON COLUMN user_template_email.preview_text IS 'Preview text hiển thị trong inbox';
COMMENT ON COLUMN user_template_email.variable_metadata IS 'Metadata chi tiết của biến (type, defaultValue, required, description)';

-- 6. Cập nhật comment cho cột content để rõ ràng hơn
COMMENT ON COLUMN user_template_email.content IS 'Nội dung HTML của email';

-- 7. Cập nhật comment cho cột placeholders để rõ ràng hơn
COMMENT ON COLUMN user_template_email.placeholders IS 'Danh sách tên biến được sử dụng trong template';

-- 8. Thêm index cho cột type để tối ưu query
CREATE INDEX IF NOT EXISTS idx_user_template_email_type ON user_template_email(type);

-- 9. Thêm index cho cột status (nếu chưa có)
CREATE INDEX IF NOT EXISTS idx_user_template_email_status ON user_template_email(status);

-- 10. Thêm constraint check cho type (với kiểm tra tồn tại)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_user_template_email_type'
        AND table_name = 'user_template_email'
    ) THEN
        ALTER TABLE user_template_email
        ADD CONSTRAINT chk_user_template_email_type
        CHECK (type IN ('NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP'));
    END IF;
END $$;

-- 11. Thêm constraint check cho status (với kiểm tra tồn tại)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_user_template_email_status'
        AND table_name = 'user_template_email'
    ) THEN
        ALTER TABLE user_template_email
        ADD CONSTRAINT chk_user_template_email_status
        CHECK (status IN ('DRAFT', 'ACTIVE', 'ARCHIVED'));
    END IF;
END $$;

-- 12. Cập nhật dữ liệu hiện có - set type mặc định
UPDATE user_template_email 
SET type = 'NEWSLETTER' 
WHERE type IS NULL;

-- 13. Tạo unique constraint cho (user_id, name) để đảm bảo tên template unique trong scope của user
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_template_email_user_name_unique 
ON user_template_email(user_id, name) 
WHERE name IS NOT NULL;

-- Verification queries (chạy để kiểm tra migration thành công)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'user_template_email' 
-- ORDER BY ordinal_position;

-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'user_template_email';
