-- Migration: Thê<PERSON> các trường mới cho user_template_email (<PERSON>ên bản đơn giản)
-- Date: 2024-01-01
-- Description: Thê<PERSON> các trường text_content, type, preview_text, variable_metadata

-- 1. Thê<PERSON> các cột mới
ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS text_content TEXT;

ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS type VARCHAR(50) DEFAULT 'NEWSLETTER';

ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS preview_text VARCHAR(255);

ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS variable_metadata JSONB;

-- 2. Cập nhật dữ liệu hiện có
UPDATE user_template_email 
SET type = 'NEWSLETTER' 
WHERE type IS NULL;

-- 3. <PERSON><PERSON><PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_user_template_email_type ON user_template_email(type);
CREATE INDEX IF NOT EXISTS idx_user_template_email_status ON user_template_email(status);

-- 4. Thêm unique constraint cho (user_id, name)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_template_email_user_name_unique 
ON user_template_email(user_id, name) 
WHERE name IS NOT NULL;

-- 5. Thêm comments
COMMENT ON COLUMN user_template_email.text_content IS 'Nội dung text thuần của email';
COMMENT ON COLUMN user_template_email.type IS 'Loại template (NEWSLETTER, PROMOTIONAL, TRANSACTIONAL, WELCOME, ABANDONED_CART, FOLLOW_UP)';
COMMENT ON COLUMN user_template_email.preview_text IS 'Preview text hiển thị trong inbox';
COMMENT ON COLUMN user_template_email.variable_metadata IS 'Metadata chi tiết của biến (type, defaultValue, required, description)';
COMMENT ON COLUMN user_template_email.content IS 'Nội dung HTML của email';
COMMENT ON COLUMN user_template_email.placeholders IS 'Danh sách tên biến được sử dụng trong template';

-- Verification query
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'user_template_email' 
-- ORDER BY ordinal_position;
