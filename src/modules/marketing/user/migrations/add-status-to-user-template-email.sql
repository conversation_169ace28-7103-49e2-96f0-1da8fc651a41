-- Migration: Add missing columns to marketing tables
-- Date: 2024-01-XX
-- Description: Thê<PERSON> các field còn thiếu vào bảng marketing

-- 1. Thêm cột status vào user_template_email
ALTER TABLE user_template_email
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'DRAFT';

-- Cập nhật dữ liệu hiện có (nế<PERSON> cần)
UPDATE user_template_email
SET status = 'ACTIVE'
WHERE status IS NULL;

-- 2. Thêm cột segment_id vào user_campaigns (nếu chưa có)
ALTER TABLE user_campaigns
ADD COLUMN IF NOT EXISTS segment_id BIGINT NULL;

-- 3. Thêm cột audience_ids vào user_campaigns (nếu chưa có)
ALTER TABLE user_campaigns
ADD COLUMN IF NOT EXISTS audience_ids JSONB NULL;

-- Thêm index cho performance
CREATE INDEX IF NOT EXISTS idx_user_template_email_status ON user_template_email(status);
CREATE INDEX IF NOT EXISTS idx_user_template_email_user_status ON user_template_email(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_campaigns_segment_id ON user_campaigns(segment_id);
CREATE INDEX IF NOT EXISTS idx_user_campaigns_user_segment ON user_campaigns(user_id, segment_id);
