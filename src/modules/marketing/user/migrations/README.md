# Marketing Database Migration

## 🚨 Lỗi hiện tại

API Marketing Overview đang gặp lỗi do thiếu columns trong database:

1. **user_template_email.status** - Cần để quản lý trạng thái template
2. **user_campaigns.segment_id** - Cần để liên kết với segment
3. **user_campaigns.audience_ids** - Cần để lưu danh sách audience

## 🔧 Cách khắc phục

### Bước 1: Chạy Migration SQL

Chạy file migration sau trong PostgreSQL:

```sql
-- File: add-status-to-user-template-email.sql

-- 1. Thêm cột status vào user_template_email
ALTER TABLE user_template_email 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'DRAFT';

-- Cập nhật dữ liệu hiện có
UPDATE user_template_email 
SET status = 'ACTIVE' 
WHERE status IS NULL;

-- 2. Thêm cột segment_id vào user_campaigns
ALTER TABLE user_campaigns 
ADD COLUMN IF NOT EXISTS segment_id BIGINT NULL;

-- 3. Thêm cột audience_ids vào user_campaigns
ALTER TABLE user_campaigns 
ADD COLUMN IF NOT EXISTS audience_ids JSONB NULL;

-- Thêm index cho performance
CREATE INDEX IF NOT EXISTS idx_user_template_email_status ON user_template_email(status);
CREATE INDEX IF NOT EXISTS idx_user_template_email_user_status ON user_template_email(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_campaigns_segment_id ON user_campaigns(segment_id);
CREATE INDEX IF NOT EXISTS idx_user_campaigns_user_segment ON user_campaigns(user_id, segment_id);
```

### Bước 2: Cập nhật Service (sau khi migration)

Sau khi chạy migration thành công, cập nhật lại service:

```typescript
// Trong getRecentTemplates method
const templates = await this.userTemplateEmailRepository.repository.find({
  where: { userId },
  select: ['id', 'name', 'subject', 'status', 'createdAt'], // Thêm lại status
  order: { createdAt: 'DESC' },
  take: 5,
});

// Và sử dụng lại mapTemplateStatus method
status: this.mapTemplateStatus(template.status),
```

### Bước 3: Kiểm tra

Sau khi migration, test lại API:

```bash
# Test Overview API
curl -X GET /api/v1/marketing/overview \
  -H "Authorization: Bearer <token>"

# Test Recent Templates API  
curl -X GET /api/v1/marketing/overview/recent-templates \
  -H "Authorization: Bearer <token>"
```

## 📋 Checklist

- [ ] Backup database trước khi migration
- [ ] Chạy migration SQL
- [ ] Kiểm tra columns đã được tạo
- [ ] Cập nhật service code (uncomment mapTemplateStatus)
- [ ] Test API hoạt động
- [ ] Cập nhật documentation

## 🔄 Rollback (nếu cần)

Nếu cần rollback migration:

```sql
-- Xóa columns đã thêm
ALTER TABLE user_template_email DROP COLUMN IF EXISTS status;
ALTER TABLE user_campaigns DROP COLUMN IF EXISTS segment_id;
ALTER TABLE user_campaigns DROP COLUMN IF EXISTS audience_ids;

-- Xóa indexes
DROP INDEX IF EXISTS idx_user_template_email_status;
DROP INDEX IF EXISTS idx_user_template_email_user_status;
DROP INDEX IF EXISTS idx_user_campaigns_segment_id;
DROP INDEX IF EXISTS idx_user_campaigns_user_segment;
```

## 📝 Ghi chú

- Migration sử dụng `IF NOT EXISTS` để tránh lỗi nếu chạy lại
- Tất cả columns mới đều nullable để không ảnh hưởng dữ liệu cũ
- Indexes được tạo để tối ưu performance
- Service hiện tại đã được fix tạm thời để tránh crash
