-- Migration: Thê<PERSON> constraints cho user_template_email
-- Date: 2024-01-01
-- Description: Thêm các constraints check cho type và status

-- <PERSON><PERSON><PERSON> tra và thêm constraint cho type
DO $$
BEGIN
    -- <PERSON><PERSON><PERSON> tra constraint type có tồn tại không
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_user_template_email_type' 
        AND table_name = 'user_template_email'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_template_email 
        ADD CONSTRAINT chk_user_template_email_type 
        CHECK (type IN ('NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP'));
        
        RAISE NOTICE 'Added constraint chk_user_template_email_type';
    ELSE
        RAISE NOTICE 'Constraint chk_user_template_email_type already exists';
    END IF;
END $$;

-- <PERSON><PERSON><PERSON> tra và thêm constraint cho status
DO $$
BEGIN
    -- <PERSON><PERSON><PERSON> tra constraint status có tồn tại không
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_user_template_email_status' 
        AND table_name = 'user_template_email'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_template_email 
        ADD CONSTRAINT chk_user_template_email_status 
        CHECK (status IN ('DRAFT', 'ACTIVE', 'ARCHIVED'));
        
        RAISE NOTICE 'Added constraint chk_user_template_email_status';
    ELSE
        RAISE NOTICE 'Constraint chk_user_template_email_status already exists';
    END IF;
END $$;

-- Verification: Kiểm tra constraints đã được tạo
SELECT 
    constraint_name, 
    constraint_type, 
    check_clause
FROM information_schema.check_constraints 
WHERE constraint_name IN ('chk_user_template_email_type', 'chk_user_template_email_status');
