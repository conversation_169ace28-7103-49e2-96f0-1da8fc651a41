import {
  Controller,
  Get,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { MarketingOverviewService } from '../services/marketing-overview.service';
import { MarketingOverviewResponseDto, RecentTemplatesResponseDto } from '../dto/overview';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';

/**
 * Controller xử lý API overview marketing
 */
@ApiTags(SWAGGER_API_TAGS.USER_MARKETING)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/overview')
export class MarketingOverviewController {
  constructor(private readonly marketingOverviewService: MarketingOverviewService) {}

  /**
   * Lấy thông tin overview marketing
   */
  @Get()
  @ApiOperation({ summary: 'Lấy thông tin overview marketing' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin overview marketing',
    type: MarketingOverviewResponseDto,
  })
  async getOverview(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<MarketingOverviewResponseDto>> {
    const result = await this.marketingOverviewService.getMarketingOverview(user.id);
    return wrapResponse(result, 'Thông tin overview marketing');
  }

  /**
   * Lấy danh sách templates gần đây
   */
  @Get('recent-templates')
  @ApiOperation({ summary: 'Lấy danh sách 5 templates gần đây nhất' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách templates gần đây',
    type: RecentTemplatesResponseDto,
  })
  async getRecentTemplates(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<RecentTemplatesResponseDto>> {
    const result = await this.marketingOverviewService.getRecentTemplates(user.id);
    return wrapResponse(result, 'Danh sách templates gần đây');
  }
}
