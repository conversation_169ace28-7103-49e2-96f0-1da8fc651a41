import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserAudienceCustomFieldDefinitionService } from '../services/user-audience-custom-field-definition.service';
import {
  CreateAudienceCustomFieldDefinitionDto,
  UpdateAudienceCustomFieldDefinitionDto,
  AudienceCustomFieldDefinitionResponseDto,
  AudienceCustomFieldDefinitionQueryDto,
} from '../dto/audience-custom-field-definition';
import { BulkDeleteCustomFieldDto, BulkDeleteResponseDto } from '@modules/marketing/common/dto/bulk-delete.dto';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { CustomFieldConfigUtil } from '@modules/marketing/common/utils/custom-field-config.util';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * Controller xử lý các API liên quan đến trường tùy chỉnh của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_MARKETING_CUSTOM_FIELD)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/marketing/audience-custom-fields')
export class UserAudienceCustomFieldDefinitionController {
  constructor(private readonly customFieldService: UserAudienceCustomFieldDefinitionService) {}

  /**
   * Tạo mới trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo mới trường tùy chỉnh',
    description: `
Tạo mới trường tùy chỉnh với validation config theo dataType. Config sẽ được validate tự động theo loại dữ liệu được chọn.

## Cấu trúc Config theo DataType:

### TEXT (dataType = "text")
\`\`\`json
{
  "fieldKey": "full_name",
  "displayName": "Họ và tên",
  "dataType": "text",
  "description": "Họ và tên đầy đủ của khách hàng",
  "tags": ["personal", "required"],
  "config": {
    "placeholder": "Nhập họ và tên đầy đủ...",
    "defaultValue": "",
    "pattern": "^[a-zA-ZÀ-ỹ\\\\s]+$",
    "minLength": 2,
    "maxLength": 100
  }
}
\`\`\`

### NUMBER (dataType = "number")
\`\`\`json
{
  "fieldKey": "age",
  "displayName": "Tuổi",
  "dataType": "number",
  "description": "Tuổi của khách hàng",
  "config": {
    "placeholder": "Nhập tuổi...",
    "defaultValue": 18,
    "minValue": 0,
    "maxValue": 120
  }
}
\`\`\`

### BOOLEAN (dataType = "boolean")
\`\`\`json
{
  "fieldKey": "is_married",
  "displayName": "Đã kết hôn",
  "dataType": "boolean",
  "config": {
    "placeholder": "Tình trạng hôn nhân",
    "defaultValue": false
  }
}
\`\`\`

### DATE (dataType = "date")
\`\`\`json
{
  "fieldKey": "birth_date",
  "displayName": "Ngày sinh",
  "dataType": "date",
  "config": {
    "placeholder": "Chọn ngày sinh...",
    "defaultValue": "2000-01-01"
  }
}
\`\`\`

### SELECT (dataType = "select")
\`\`\`json
{
  "fieldKey": "gender",
  "displayName": "Giới tính",
  "dataType": "select",
  "config": {
    "placeholder": "Chọn giới tính...",
    "options": [
      { "title": "Nam", "value": "male" },
      { "title": "Nữ", "value": "female" },
      { "title": "Khác", "value": "other" }
    ],
    "defaultValue": "male"
  }
}
\`\`\`

### OBJECT (dataType = "object")
\`\`\`json
{
  "fieldKey": "address",
  "displayName": "Địa chỉ",
  "dataType": "object",
  "config": {
    "placeholder": "Nhập thông tin địa chỉ...",
    "defaultValue": {
      "street": "",
      "ward": "",
      "district": "",
      "city": "",
      "country": "Vietnam"
    }
  }
}
\`\`\`

## Lưu ý:
- Nếu không cung cấp config, hệ thống sẽ tự động tạo config mặc định
- Config sẽ được validate theo dataType đã chọn
- Sử dụng endpoint GET /config-examples để lấy cấu trúc config mẫu
    `
  })
  @ApiResponse({
    status: 201,
    description: 'Trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
    examples: {
      'text-field': {
        summary: 'Tạo trường TEXT thành công',
        value: {
          success: true,
          message: 'Tạo trường tùy chỉnh thành công',
          data: {
            id: 1,
            fieldKey: 'full_name',
            userId: 123,
            displayName: 'Họ và tên',
            dataType: 'text',
            description: 'Họ và tên đầy đủ của khách hàng',
            tags: ['personal', 'required'],
            config: {
              placeholder: 'Nhập họ và tên đầy đủ...',
              defaultValue: '',
              pattern: '^[a-zA-ZÀ-ỹ\\s]+$',
              minLength: 2,
              maxLength: 100
            }
          }
        }
      },
      'select-field': {
        summary: 'Tạo trường SELECT thành công',
        value: {
          success: true,
          message: 'Tạo trường tùy chỉnh thành công',
          data: {
            id: 2,
            fieldKey: 'gender',
            userId: 123,
            displayName: 'Giới tính',
            dataType: 'select',
            description: 'Giới tính của khách hàng',
            tags: ['personal'],
            config: {
              placeholder: 'Chọn giới tính...',
              options: [
                { title: 'Nam', value: 'male' },
                { title: 'Nữ', value: 'female' },
                { title: 'Khác', value: 'other' }
              ],
              defaultValue: 'male'
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Config không hợp lệ cho dataType đã chọn',
    examples: {
      'invalid-text-config': {
        summary: 'Config TEXT không hợp lệ',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Độ dài tối thiểu không được nhỏ hơn 0',
            'Độ dài tối đa không được nhỏ hơn 1'
          ],
          dataType: 'text',
          receivedConfig: {
            minLength: -1,
            maxLength: 0
          }
        }
      },
      'invalid-select-config': {
        summary: 'Config SELECT không hợp lệ',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Phải có ít nhất 1 tùy chọn',
            'Giá trị mặc định phải là một trong các value trong options'
          ],
          dataType: 'select',
          receivedConfig: {
            options: [],
            defaultValue: 'invalid_value'
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
  )
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    // Validate config theo dataType nếu có config
    if (createDto.config && Object.keys(createDto.config).length > 0) {
      const configErrors = await CustomFieldConfigUtil.validateConfig(
        createDto.dataType,
        createDto.config
      );

      if (configErrors.length > 0) {
        throw new BadRequestException({
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: configErrors,
          dataType: createDto.dataType,
          receivedConfig: createDto.config
        });
      }
    } else {
      // Nếu không có config, sử dụng config mặc định
      createDto.config = CustomFieldConfigUtil.getDefaultConfig(createDto.dataType);
    }

    const result = await this.customFieldService.create(user.id, createDto);
    return wrapResponse(result, 'Tạo trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param id ID của trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật trường tùy chỉnh',
    description: `
Cập nhật trường tùy chỉnh với validation config theo dataType. Nếu thay đổi dataType, config sẽ được validate lại.

## Các trường có thể cập nhật:

### Thông tin cơ bản:
- \`displayName\`: Tên hiển thị
- \`description\`: Mô tả
- \`tags\`: Danh sách tags
- \`dataType\`: Loại dữ liệu (nếu thay đổi, config sẽ được validate lại)
- \`config\`: Cấu hình chi tiết theo dataType

## Examples cập nhật theo DataType:

### Cập nhật TEXT field:
\`\`\`json
{
  "displayName": "Họ và tên đầy đủ",
  "description": "Họ và tên đầy đủ của khách hàng (đã cập nhật)",
  "config": {
    "placeholder": "Nhập họ và tên đầy đủ...",
    "pattern": "^[a-zA-ZÀ-ỹ\\\\s]+$",
    "minLength": 3,
    "maxLength": 150
  }
}
\`\`\`

### Cập nhật SELECT field - thêm options:
\`\`\`json
{
  "displayName": "Giới tính",
  "config": {
    "placeholder": "Chọn giới tính...",
    "options": [
      { "title": "Nam", "value": "male" },
      { "title": "Nữ", "value": "female" },
      { "title": "Khác", "value": "other" },
      { "title": "Không muốn tiết lộ", "value": "prefer_not_to_say" }
    ],
    "defaultValue": "prefer_not_to_say"
  }
}
\`\`\`

### Thay đổi dataType từ TEXT sang SELECT:
\`\`\`json
{
  "dataType": "select",
  "config": {
    "placeholder": "Chọn trình độ học vấn...",
    "options": [
      { "title": "Tiểu học", "value": "primary" },
      { "title": "Trung học cơ sở", "value": "secondary" },
      { "title": "Trung học phổ thông", "value": "high_school" },
      { "title": "Cao đẳng", "value": "college" },
      { "title": "Đại học", "value": "university" }
    ]
  }
}
\`\`\`

### Cập nhật NUMBER field với validation mới:
\`\`\`json
{
  "displayName": "Tuổi",
  "config": {
    "placeholder": "Nhập tuổi...",
    "defaultValue": 25,
    "minValue": 16,
    "maxValue": 65
  }
}
\`\`\`

## Lưu ý:
- Chỉ cần gửi các trường muốn cập nhật
- Nếu thay đổi dataType, config sẽ được validate theo dataType mới
- Config sẽ được merge với config hiện tại nếu không thay đổi dataType
- Sử dụng endpoint GET /config-examples để lấy cấu trúc config mẫu
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
    examples: {
      'update-text-field': {
        summary: 'Cập nhật TEXT field thành công',
        value: {
          success: true,
          message: 'Cập nhật trường tùy chỉnh thành công',
          data: {
            id: 1,
            fieldKey: 'full_name',
            userId: 123,
            displayName: 'Họ và tên đầy đủ',
            dataType: 'text',
            description: 'Họ và tên đầy đủ của khách hàng (đã cập nhật)',
            tags: ['personal', 'required', 'updated'],
            config: {
              placeholder: 'Nhập họ và tên đầy đủ...',
              pattern: '^[a-zA-ZÀ-ỹ\\s]+$',
              minLength: 3,
              maxLength: 150
            },
            updatedAt: 1703123456
          }
        }
      },
      'update-select-options': {
        summary: 'Cập nhật SELECT field - thêm options',
        value: {
          success: true,
          message: 'Cập nhật trường tùy chỉnh thành công',
          data: {
            id: 2,
            fieldKey: 'gender',
            userId: 123,
            displayName: 'Giới tính',
            dataType: 'select',
            description: 'Giới tính của khách hàng',
            config: {
              placeholder: 'Chọn giới tính...',
              options: [
                { title: 'Nam', value: 'male' },
                { title: 'Nữ', value: 'female' },
                { title: 'Khác', value: 'other' },
                { title: 'Không muốn tiết lộ', value: 'prefer_not_to_say' }
              ],
              defaultValue: 'prefer_not_to_say'
            },
            updatedAt: 1703123456
          }
        }
      },
      'change-datatype': {
        summary: 'Thay đổi dataType từ TEXT sang SELECT',
        value: {
          success: true,
          message: 'Cập nhật trường tùy chỉnh thành công',
          data: {
            id: 3,
            fieldKey: 'education_level',
            userId: 123,
            displayName: 'Trình độ học vấn',
            dataType: 'select',
            description: 'Trình độ học vấn cao nhất',
            config: {
              placeholder: 'Chọn trình độ học vấn...',
              options: [
                { title: 'Tiểu học', value: 'primary' },
                { title: 'Trung học cơ sở', value: 'secondary' },
                { title: 'Trung học phổ thông', value: 'high_school' },
                { title: 'Cao đẳng', value: 'college' },
                { title: 'Đại học', value: 'university' }
              ]
            },
            updatedAt: 1703123456
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Config không hợp lệ cho dataType đã chọn',
    examples: {
      'invalid-config-after-datatype-change': {
        summary: 'Config không hợp lệ sau khi thay đổi dataType',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Phải có ít nhất 1 tùy chọn',
            'Options phải là mảng'
          ],
          dataType: 'select',
          receivedConfig: {
            placeholder: 'Chọn...',
            options: 'invalid_options'
          }
        }
      },
      'invalid-number-config': {
        summary: 'Config NUMBER không hợp lệ',
        value: {
          success: false,
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: [
            'Giá trị tối đa phải lớn hơn giá trị tối thiểu'
          ],
          dataType: 'number',
          receivedConfig: {
            minValue: 100,
            maxValue: 50
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
    @Body() updateDto: UpdateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    // Nếu có cập nhật dataType hoặc config, cần validate
    if (updateDto.dataType || (updateDto.config && Object.keys(updateDto.config).length > 0)) {
      // Lấy thông tin hiện tại để biết dataType
      const currentField = await this.customFieldService.findOne(user.id, id);
      const dataTypeToValidate = updateDto.dataType || currentField.dataType;
      const configToValidate = updateDto.config || currentField.config;

      // Validate config theo dataType
      if (configToValidate && Object.keys(configToValidate).length > 0) {
        const configErrors = await CustomFieldConfigUtil.validateConfig(
          dataTypeToValidate,
          configToValidate
        );

        if (configErrors.length > 0) {
          throw new BadRequestException({
            message: 'Config không hợp lệ cho dataType đã chọn',
            errors: configErrors,
            dataType: dataTypeToValidate,
            receivedConfig: configToValidate
          });
        }
      }
    }

    const result = await this.customFieldService.update(user.id, id, updateDto);
    return wrapResponse(result, 'Cập nhật trường tùy chỉnh thành công');
  }

  /**
   * Xóa nhiều trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param bulkDeleteDto Dữ liệu xóa nhiều
   * @returns Kết quả xóa nhiều
   */
  @Delete('bulk')
  @ApiOperation({ summary: 'Xóa nhiều trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Xóa trường tùy chỉnh thành công',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({
    status: 207,
    description: 'Một số trường tùy chỉnh không thể xóa',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy trường tùy chỉnh' })
  async bulkDelete(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteCustomFieldDto
  ): Promise<ApiResponseDto<BulkDeleteResponseDto>> {
    console.log('Controller received bulkDeleteDto:', JSON.stringify(bulkDeleteDto, null, 2));
    console.log('customFieldIds:', bulkDeleteDto.customFieldIds);
    console.log('Type of customFieldIds:', typeof bulkDeleteDto.customFieldIds);
    console.log('Is array:', Array.isArray(bulkDeleteDto.customFieldIds));

    const result = await this.customFieldService.bulkDelete(user.id, bulkDeleteDto.customFieldIds);
    return wrapResponse(result, result.message);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
  )
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.delete(user.id, id);
    return wrapResponse(result, 'Xóa trường tùy chỉnh thành công');
  }

  /**
   * Lấy thông tin trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND)
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.findOne(user.id, id);
    return wrapResponse(result);
  }

  /**
   * Lấy config examples cho từng dataType
   * @returns Config examples cho tất cả dataType
   */
  @Get('config-examples')
  @ApiOperation({
    summary: 'Lấy config examples cho từng dataType',
    description: 'Trả về cấu trúc config mẫu cho từng loại dữ liệu để frontend tham khảo'
  })
  @ApiResponse({
    status: 200,
    description: 'Config examples cho tất cả dataType',
    schema: {
      type: 'object',
      properties: {
        text: { type: 'object' },
        number: { type: 'object' },
        boolean: { type: 'object' },
        date: { type: 'object' },
        select: { type: 'object' },
        object: { type: 'object' }
      }
    }
  })
  async getConfigExamples(): Promise<ApiResponseDto<Record<string, any>>> {
    const examples = {
      [CustomFieldDataType.TEXT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.TEXT),
      [CustomFieldDataType.NUMBER]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.NUMBER),
      [CustomFieldDataType.BOOLEAN]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.BOOLEAN),
      [CustomFieldDataType.DATE]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.DATE),
      [CustomFieldDataType.SELECT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.SELECT),
      [CustomFieldDataType.OBJECT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.OBJECT),
    };

    return wrapResponse(examples, 'Lấy config examples thành công');
  }

  /**
   * Lấy danh sách trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(AudienceCustomFieldDefinitionResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: AudienceCustomFieldDefinitionQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AudienceCustomFieldDefinitionResponseDto>>> {
    const result = await this.customFieldService.findAll(user.id, queryDto);
    return wrapResponse(result);
  }
}
