import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { QueryDto } from '@/common/dto';

/**
 * Controller xử lý API liên quan đến mẫu tin nhắn Zalo
 */
@ApiTags(SWAGGER_API_TAGS.ZALO)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/templates')
export class ZaloTemplateController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách mẫu tin nhắn
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách mẫu tin nhắn' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách mẫu tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      name: { type: 'string' },
                      type: { type: 'string' },
                      content: { type: 'object' },
                      createdAt: { type: 'number' },
                      updatedAt: { type: 'number' },
                    },
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getTemplates(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<any>>> {
    const templates = await this.zaloService.getZaloMessageTemplates(user.id, oaId, queryDto);
    return ApiResponseDto.success(templates, 'Lấy danh sách mẫu tin nhắn thành công');
  }

  /**
   * Lấy thông tin chi tiết mẫu tin nhắn
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết mẫu tin nhắn' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết mẫu tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                name: { type: 'string' },
                type: { type: 'string' },
                content: { type: 'object' },
                createdAt: { type: 'number' },
                updatedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async getTemplateDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<any>> {
    const template = await this.zaloService.getZaloMessageTemplateDetail(user.id, oaId, id);
    return ApiResponseDto.success(template, 'Lấy thông tin chi tiết mẫu tin nhắn thành công');
  }

  /**
   * Tạo mẫu tin nhắn mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mẫu tin nhắn mới' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mẫu tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                name: { type: 'string' },
                type: { type: 'string' },
                content: { type: 'object' },
                createdAt: { type: 'number' },
                updatedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async createTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() createDto: any,
  ): Promise<ApiResponseDto<any>> {
    const template = await this.zaloService.createZaloMessageTemplate(user.id, oaId, createDto);
    return ApiResponseDto.success(template, 'Tạo mẫu tin nhắn thành công');
  }

  /**
   * Cập nhật mẫu tin nhắn
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật mẫu tin nhắn' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật mẫu tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                name: { type: 'string' },
                type: { type: 'string' },
                content: { type: 'object' },
                updatedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async updateTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: any,
  ): Promise<ApiResponseDto<any>> {
    const template = await this.zaloService.updateZaloMessageTemplate(user.id, oaId, id, updateDto);
    return ApiResponseDto.success(template, 'Cập nhật mẫu tin nhắn thành công');
  }

  /**
   * Xóa mẫu tin nhắn
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa mẫu tin nhắn' })
  @ApiResponse({
    status: 200,
    description: 'Xóa mẫu tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' },
          },
        },
      ],
    },
  })
  async deleteTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.zaloService.deleteZaloMessageTemplate(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Xóa mẫu tin nhắn thành công');
  }

  /**
   * Gửi tin nhắn sử dụng mẫu
   */
  @Post(':id/send')
  @ApiOperation({ summary: 'Gửi tin nhắn sử dụng mẫu' })
  @ApiResponse({
    status: 200,
    description: 'Gửi tin nhắn sử dụng mẫu thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                messageId: { type: 'string' },
                status: { type: 'string' },
                sentAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async sendTemplateMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() sendDto: { followerId: string; data?: Record<string, any> },
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.sendZaloTemplateMessage(
      user.id,
      oaId,
      id,
      sendDto.followerId,
      sendDto.data,
    );
    return ApiResponseDto.success(result, 'Gửi tin nhắn sử dụng mẫu thành công');
  }

  /**
   * Gửi tin nhắn sử dụng mẫu cho nhiều người theo dõi
   */
  @Post(':id/batch-send')
  @ApiOperation({ summary: 'Gửi tin nhắn sử dụng mẫu cho nhiều người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Gửi tin nhắn sử dụng mẫu cho nhiều người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                totalRecipients: { type: 'number' },
                successCount: { type: 'number' },
                failedCount: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async batchSendTemplateMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() sendDto: { followerIds: string[]; data?: Record<string, any> },
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.batchSendZaloTemplateMessage(
      user.id,
      oaId,
      id,
      sendDto.followerIds,
      sendDto.data,
    );
    return ApiResponseDto.success(result, 'Gửi tin nhắn sử dụng mẫu cho nhiều người theo dõi thành công');
  }
}
