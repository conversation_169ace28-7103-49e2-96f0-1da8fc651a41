import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { UserMarketingStatisticsService } from '../services/user-marketing-statistics.service';
import { 
  MarketingStatisticsQueryDto,
  MarketingOverviewStatisticsDto,
  AudienceGrowthStatisticsDto,
  CampaignPerformanceStatisticsDto,
  SegmentDistributionStatisticsDto
} from '../dto/statistics';

/**
 * Controller xử lý API thống kê marketing
 */
@ApiTags(SWAGGER_API_TAGS.USER_MARKETING_STATISTICS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/statistics')
export class UserMarketingStatisticsController {
  constructor(
    private readonly userMarketingStatisticsService: UserMarketingStatisticsService,
  ) {}

  /**
   * Lấy thống kê tổng quan về marketing
   */
  @Get('overview')
  @ApiOperation({ summary: 'Lấy thống kê tổng quan về marketing' })
  @ApiResponse({ 
    status: 200, 
    description: 'Thống kê tổng quan về marketing', 
    type: MarketingOverviewStatisticsDto 
  })
  async getOverviewStatistics(
    @CurrentUser() user: JwtPayload,
    @Query() query: MarketingStatisticsQueryDto,
  ): Promise<ApiResponseDto<MarketingOverviewStatisticsDto>> {
    const result = await this.userMarketingStatisticsService.getOverviewStatistics(user.id, query);
    return ApiResponseDto.success(result, 'Lấy thống kê tổng quan thành công');
  }

  /**
   * Lấy thống kê tăng trưởng audience
   */
  @Get('audience-growth')
  @ApiOperation({ summary: 'Lấy thống kê tăng trưởng audience' })
  @ApiResponse({ 
    status: 200, 
    description: 'Thống kê tăng trưởng audience', 
    type: AudienceGrowthStatisticsDto 
  })
  async getAudienceGrowthStatistics(
    @CurrentUser() user: JwtPayload,
    @Query() query: MarketingStatisticsQueryDto,
  ): Promise<ApiResponseDto<AudienceGrowthStatisticsDto>> {
    const result = await this.userMarketingStatisticsService.getAudienceGrowthStatistics(user.id, query);
    return ApiResponseDto.success(result, 'Lấy thống kê tăng trưởng audience thành công');
  }

  /**
   * Lấy thống kê hiệu suất campaign
   */
  @Get('campaign-performance')
  @ApiOperation({ summary: 'Lấy thống kê hiệu suất campaign' })
  @ApiResponse({ 
    status: 200, 
    description: 'Thống kê hiệu suất campaign', 
    type: CampaignPerformanceStatisticsDto 
  })
  async getCampaignPerformanceStatistics(
    @CurrentUser() user: JwtPayload,
    @Query() query: MarketingStatisticsQueryDto,
  ): Promise<ApiResponseDto<CampaignPerformanceStatisticsDto>> {
    const result = await this.userMarketingStatisticsService.getCampaignPerformanceStatistics(user.id, query);
    return ApiResponseDto.success(result, 'Lấy thống kê hiệu suất campaign thành công');
  }

  /**
   * Lấy thống kê phân phối segment
   */
  @Get('segment-distribution')
  @ApiOperation({ summary: 'Lấy thống kê phân phối segment' })
  @ApiResponse({ 
    status: 200, 
    description: 'Thống kê phân phối segment', 
    type: SegmentDistributionStatisticsDto 
  })
  async getSegmentDistributionStatistics(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<SegmentDistributionStatisticsDto>> {
    const result = await this.userMarketingStatisticsService.getSegmentDistributionStatistics(user.id);
    return ApiResponseDto.success(result, 'Lấy thống kê phân phối segment thành công');
  }
}
