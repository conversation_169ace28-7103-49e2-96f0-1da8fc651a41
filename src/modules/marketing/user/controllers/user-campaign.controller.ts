import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserCampaignService } from '../services/user-campaign.service';
import { CreateCampaignDto, UpdateCampaignDto, CampaignResponseDto, CampaignHistoryResponseDto, CampaignQueryDto } from '../dto/campaign';
import { CreateTemplateCampaignDto, CreateTemplateCampaignResponseDto } from '../dto/campaign/create-template-campaign.dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';

/**
 * Controller xử lý API liên quan đến campaign
 */
@ApiTags(SWAGGER_API_TAGS.USER_CAMPAIGN)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('/marketing/campaigns')
export class UserCampaignController {
  constructor(private readonly userCampaignService: UserCampaignService) {}

  /**
   * Tạo campaign mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo campaign mới' })
  @ApiResponse({ status: 201, description: 'Campaign đã được tạo thành công', type: CampaignResponseDto })
  async create(@CurrentUser() user: JwtPayload, @Body() createCampaignDto: CreateCampaignDto): Promise<AppApiResponse<CampaignResponseDto>> {
    const result = await this.userCampaignService.create(user.id, createCampaignDto);
    return wrapResponse(result, 'Campaign đã được tạo thành công');
  }

  /**
   * Lấy danh sách campaign với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách campaign với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách campaign với phân trang',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              allOf: [
                { $ref: '#/components/schemas/PaginatedResult' },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/CampaignResponseDto' }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: CampaignQueryDto
  ): Promise<AppApiResponse<PaginatedResult<CampaignResponseDto>>> {
    const result = await this.userCampaignService.findAll(user.id, query);
    return wrapResponse(result, 'Danh sách campaign');
  }

  /**
   * Lấy thông tin campaign theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin campaign theo ID' })
  @ApiResponse({ status: 200, description: 'Thông tin campaign', type: CampaignResponseDto })
  @ApiResponse({ status: 404, description: 'Campaign không tồn tại' })
  async findOne(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<CampaignResponseDto>> {
    const result = await this.userCampaignService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin campaign');
  }

  /**
   * Lấy lịch sử của campaign
   */
  @Get(':id/history')
  @ApiOperation({ summary: 'Lấy lịch sử của campaign' })
  @ApiResponse({ status: 200, description: 'Lịch sử campaign', type: [CampaignHistoryResponseDto] })
  @ApiResponse({ status: 404, description: 'Campaign không tồn tại' })
  async getCampaignHistory(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<CampaignHistoryResponseDto[]>> {
    const result = await this.userCampaignService.getCampaignHistory(user.id, +id);
    return wrapResponse(result, 'Lịch sử campaign');
  }

  /**
   * Cập nhật campaign
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật campaign' })
  @ApiResponse({ status: 200, description: 'Campaign đã được cập nhật thành công', type: CampaignResponseDto })
  @ApiResponse({ status: 404, description: 'Campaign không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
  ): Promise<AppApiResponse<CampaignResponseDto>> {
    const result = await this.userCampaignService.update(user.id, +id, updateCampaignDto);
    return wrapResponse(result, 'Campaign đã được cập nhật thành công');
  }

  /**
   * Xóa campaign
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa campaign' })
  @ApiResponse({ status: 200, description: 'Campaign đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Campaign không tồn tại' })
  async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.userCampaignService.remove(user.id, +id);
    return wrapResponse({ success: result }, 'Campaign đã được xóa thành công');
  }

  /**
   * Chạy campaign
   */
  @Post(':id/run')
  @ApiOperation({ summary: 'Chạy campaign' })
  @ApiResponse({ status: 200, description: 'Campaign đã được chạy thành công', type: CampaignResponseDto })
  @ApiResponse({ status: 404, description: 'Campaign không tồn tại' })
  async runCampaign(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<CampaignResponseDto>> {
    const result = await this.userCampaignService.runCampaign(user.id, +id);
    return wrapResponse(result, 'Campaign đã được chạy thành công');
  }

  /**
   * Tạo campaign từ template với format request tùy chỉnh
   */
  @Post('from-template')
  @ApiOperation({
    summary: 'Tạo campaign từ template',
    description: 'Tạo campaign mới từ template email với cấu hình email server và danh sách segments/audiences'
  })
  @ApiResponse({
    status: 201,
    description: 'Campaign từ template đã được tạo thành công',
    type: CreateTemplateCampaignResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ'
  })
  @ApiResponse({
    status: 404,
    description: 'Template hoặc email server không tồn tại'
  })
  async createFromTemplate(
    @CurrentUser() user: JwtPayload,
    @Body() createTemplateCampaignDto: CreateTemplateCampaignDto
  ): Promise<AppApiResponse<CreateTemplateCampaignResponseDto>> {
    const result = await this.userCampaignService.createTemplateCampaign(user.id, createTemplateCampaignDto);
    return wrapResponse(result, 'Campaign từ template đã được tạo thành công');
  }
}
