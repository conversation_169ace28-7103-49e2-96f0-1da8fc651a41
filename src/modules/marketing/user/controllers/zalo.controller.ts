import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloOfficialAccount, ZaloFollower, ZaloMessage } from '../entities';
import {
  ConnectOfficialAccountDto,
  FollowerQueryDto,
  FollowerResponseDto,
  MessageQueryDto,
  MessageRequestDto,
  MessageResponseDto,
  OfficialAccountResponseDto,
  TagRequestDto,
} from '../dto/zalo';

/**
 * Controller xử lý API liên quan đến Zalo
 */
@ApiTags(SWAGGER_API_TAGS.ZALO)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo')
export class ZaloController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách Official Account của người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Official Account của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Official Account thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: getSchemaPath(OfficialAccountResponseDto) }
            }
          }
        }
      ]
    }
  })
  async getOfficialAccounts(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Partial<ZaloOfficialAccount>[]>> {
    const result = await this.zaloService.getOfficialAccounts(user.id);

    // Chỉ trả về các thông tin cần thiết
    const response = result.map(oa => ({
      id: oa.id,
      oaId: oa.oaId,
      name: oa.name,
      description: oa.description,
      avatarUrl: oa.avatarUrl,
      status: oa.status,
      createdAt: oa.createdAt,
      updatedAt: oa.updatedAt,
    }));

    return ApiResponseDto.success(response, 'Lấy danh sách Official Account thành công');
  }

  /**
   * Lấy thông tin chi tiết Official Account
   */
  @Get(':oaId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết Official Account' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết Official Account thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(OfficialAccountResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async getOfficialAccountDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<Partial<ZaloOfficialAccount>>> {
    const result = await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      oaId: result.oaId,
      name: result.name,
      description: result.description,
      avatarUrl: result.avatarUrl,
      status: result.status,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };

    return ApiResponseDto.success(response, 'Lấy thông tin chi tiết Official Account thành công');
  }

  /**
   * Kết nối Official Account với hệ thống
   */
  @Post('connect')
  @ApiOperation({ summary: 'Kết nối Official Account với hệ thống' })
  @ApiResponse({
    status: 200,
    description: 'Kết nối thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(OfficialAccountResponseDto) }
          }
        }
      ]
    }
  })
  async connectOfficialAccount(
    @CurrentUser() user: JwtPayload,
    @Body() connectDto: ConnectOfficialAccountDto,
  ): Promise<ApiResponseDto<Partial<ZaloOfficialAccount>>> {
    const result = await this.zaloService.connectOfficialAccount(
      user.id,
      connectDto.accessToken,
      connectDto.refreshToken,
      connectDto.expiresAt,
    );

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      oaId: result.oaId,
      name: result.name,
      avatarUrl: result.avatarUrl,
      status: result.status,
    };

    return ApiResponseDto.success(response, 'Kết nối Official Account thành công');
  }

  /**
   * Ngắt kết nối Official Account
   */
  @Delete(':oaId/disconnect')
  @ApiOperation({ summary: 'Ngắt kết nối Official Account' })
  @ApiResponse({
    status: 200,
    description: 'Ngắt kết nối thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async disconnectOfficialAccount(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.zaloService.disconnectOfficialAccount(user.id, oaId);
    return ApiResponseDto.success(result, 'Ngắt kết nối Official Account thành công');
  }

  /**
   * Lấy danh sách người theo dõi của Official Account từ Zalo API
   */
  @Get(':oaId/followers/zalo')
  @ApiOperation({ summary: 'Lấy danh sách người theo dõi của Official Account từ Zalo API' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                followers: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      display_name: { type: 'string' },
                    }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getFollowersFromZalo(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query('offset') offset: number = 0,
    @Query('limit') limit: number = 50,
  ): Promise<ApiResponseDto<any>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getFollowers(oaId, offset, limit);
    return ApiResponseDto.success(result, 'Lấy danh sách người theo dõi từ Zalo thành công');
  }

  /**
   * Lấy danh sách người theo dõi của Official Account từ database
   */
  @Get(':oaId/followers')
  @ApiOperation({ summary: 'Lấy danh sách người theo dõi của Official Account từ database' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(FollowerResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getFollowersFromDatabase(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: FollowerQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getFollowersFromDatabase(oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách người theo dõi từ database thành công');
  }

  /**
   * Lấy thông tin chi tiết người theo dõi
   */
  @Get(':oaId/followers/:userId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(FollowerResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người theo dõi' })
  async getFollowerProfile(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
  ): Promise<ApiResponseDto<Partial<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getFollowerProfile(oaId, userId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      userId: result.userId,
      displayName: result.displayName,
      avatarUrl: result.avatarUrl,
      phone: result.phone,
      gender: result.gender,
      tags: result.tags,
      status: result.status,
      followedAt: result.followedAt,
    };

    return ApiResponseDto.success(response, 'Lấy thông tin người theo dõi thành công');
  }

  /**
   * Thêm tag cho người theo dõi
   */
  @Post(':oaId/followers/:userId/tags')
  @ApiOperation({ summary: 'Thêm tag cho người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Thêm tag thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(FollowerResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người theo dõi' })
  async addTagToFollower(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
    @Body() tagRequestDto: TagRequestDto,
  ): Promise<ApiResponseDto<Partial<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    await this.zaloService.addTagToFollower(oaId, userId, tagRequestDto.tagName);

    // Lấy thông tin người theo dõi sau khi thêm tag
    const follower = await this.zaloService.getFollowerProfile(oaId, userId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: follower.id,
      userId: follower.userId,
      displayName: follower.displayName,
      tags: follower.tags,
      status: follower.status,
    };

    return ApiResponseDto.success(response, 'Thêm tag thành công');
  }

  /**
   * Xóa tag của người theo dõi
   */
  @Delete(':oaId/followers/:userId/tags/:tag')
  @ApiOperation({ summary: 'Xóa tag của người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tag thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(FollowerResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người theo dõi' })
  async removeTagFromFollower(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
    @Param('tag') tag: string,
  ): Promise<ApiResponseDto<Partial<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    await this.zaloService.removeTagFromFollower(oaId, userId, tag);

    // Lấy thông tin người theo dõi sau khi xóa tag
    const follower = await this.zaloService.getFollowerProfile(oaId, userId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: follower.id,
      userId: follower.userId,
      displayName: follower.displayName,
      tags: follower.tags,
      status: follower.status,
    };

    return ApiResponseDto.success(response, 'Xóa tag thành công');
  }

  /**
   * Lấy lịch sử tin nhắn với một người dùng Zalo
   */
  @Get(':oaId/followers/:userId/messages')
  @ApiOperation({ summary: 'Lấy lịch sử tin nhắn với một người dùng Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(MessageResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getMessages(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
    @Query() queryDto: MessageQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloMessage>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getMessages(oaId, userId, queryDto);
    return ApiResponseDto.success(result, 'Lấy lịch sử tin nhắn thành công');
  }

  /**
   * Gửi tin nhắn đến người dùng Zalo
   */
  @Post(':oaId/messages')
  @ApiOperation({ summary: 'Gửi tin nhắn đến người dùng Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Gửi tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(MessageResponseDto) }
          }
        }
      ]
    }
  })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() messageDto: MessageRequestDto,
  ): Promise<ApiResponseDto<Partial<ZaloMessage>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const params: Record<string, any> = {};

    switch (messageDto.messageType) {
      case 'text':
        params.message = messageDto.message;
        break;
      case 'image':
        params.imageUrl = messageDto.imageUrl;
        break;
      case 'file':
        params.fileUrl = messageDto.fileUrl;
        break;
      case 'template':
        params.templateId = messageDto.templateId;
        params.templateData = messageDto.templateData;
        break;
    }

    const result = await this.zaloService.sendMessage(
      oaId,
      messageDto.userId,
      messageDto.messageType,
      params,
    );

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      messageId: result.messageId,
      messageType: result.messageType,
      content: result.content,
      data: result.data,
      direction: result.direction,
      timestamp: result.timestamp,
    };

    return ApiResponseDto.success(response, 'Gửi tin nhắn thành công');
  }

  /**
   * Webhook để nhận các sự kiện từ Zalo
   * Lưu ý: Endpoint này không yêu cầu xác thực JWT
   */
  @Post('webhook')
  @ApiOperation({ summary: 'Webhook để nhận các sự kiện từ Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Xử lý webhook thành công',
  })
  async handleWebhook(@Body() data: any): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.handleWebhook(data);
    return ApiResponseDto.success(result, 'Xử lý webhook thành công');
  }
}
