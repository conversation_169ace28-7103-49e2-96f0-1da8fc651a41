#!/bin/bash

# Script test cuối cùng cho Template Email API
# Sử dụng: ./test-final.sh [BASE_URL] [JWT_TOKEN]

BASE_URL=${1:-"http://localhost:3000"}
JWT_TOKEN=${2:-"YOUR_JWT_TOKEN_HERE"}

API_ENDPOINT="$BASE_URL/api/v1/marketing/template-emails"

echo "🎯 Final Test - Template Email API"
echo "📍 Endpoint: $API_ENDPOINT"
echo "🔑 Token: ${JWT_TOKEN:0:20}..."
echo ""

# Test với request chính xác như frontend đang gửi
echo "📝 Test: Request từ frontend (field 'content')"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Final Template",
    "subject": "Test Final Subject",
    "content": "<h1>Hello World!</h1><p>This should work now!</p>",
    "previewText": "",
    "tags": [],
    "textContent": "",
    "type": "PROMOTIONAL",
    "variables": []
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n"
echo "✅ Final test completed!"
echo ""
echo "📋 Expected result: 201 (Success)"
echo "🔍 Response should contain both 'content' and 'htmlContent' fields"
