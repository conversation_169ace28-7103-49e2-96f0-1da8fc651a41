#!/bin/bash

# <PERSON>ript test backward compatibility cho Template Email API
# Sử dụng: ./test-backward-compatibility.sh [BASE_URL] [JWT_TOKEN]

BASE_URL=${1:-"http://localhost:3000"}
JWT_TOKEN=${2:-"YOUR_JWT_TOKEN_HERE"}

API_ENDPOINT="$BASE_URL/api/v1/marketing/template-emails"

echo "🔄 Testing Template Email API - Backward Compatibility"
echo "📍 Endpoint: $API_ENDPOINT"
echo "🔑 Token: ${JWT_TOKEN:0:20}..."
echo ""

# Test 1: Sử dụng field "content" (cũ)
echo "📝 Test 1: Sử dụng field 'content' (backward compatibility)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Content Field",
    "subject": "Test với content field",
    "content": "<h1>Hello {user_name}!</h1><p>This uses content field</p>",
    "type": "NEWSLETTER",
    "tags": ["test", "content-field"]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 2: Sử dụng field "htmlContent" (mới)
echo "📝 Test 2: Sử dụng field 'htmlContent' (new field)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template HtmlContent Field",
    "subject": "Test với htmlContent field",
    "htmlContent": "<h1>Hello {user_name}!</h1><p>This uses htmlContent field</p>",
    "type": "NEWSLETTER",
    "tags": ["test", "htmlcontent-field"]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 3: Sử dụng cả 2 fields (ưu tiên htmlContent)
echo "📝 Test 3: Sử dụng cả 2 fields (htmlContent should take priority)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Both Fields",
    "subject": "Test với cả 2 fields",
    "content": "<h1>This is content field</h1>",
    "htmlContent": "<h1>This is htmlContent field - should be used</h1>",
    "type": "NEWSLETTER",
    "tags": ["test", "both-fields"]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 4: Không có field nào (should fail)
echo "📝 Test 4: Không có content field nào (should fail)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template No Content",
    "subject": "Test không có content",
    "type": "NEWSLETTER",
    "tags": ["test", "no-content"]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 5: Content rỗng (should fail)
echo "📝 Test 5: Content rỗng (should fail)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Empty Content",
    "subject": "Test content rỗng",
    "content": "",
    "type": "NEWSLETTER",
    "tags": ["test", "empty-content"]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n"
echo "✅ Backward Compatibility Test completed!"
echo ""
echo "📋 Expected results:"
echo "  - Test 1: 201 (Success - using content field)"
echo "  - Test 2: 201 (Success - using htmlContent field)"
echo "  - Test 3: 201 (Success - htmlContent takes priority)"
echo "  - Test 4: 400 (Bad Request - no content field)"
echo "  - Test 5: 400 (Bad Request - empty content)"
echo ""
echo "🔍 Response should contain both 'content' and 'htmlContent' fields for backward compatibility"
