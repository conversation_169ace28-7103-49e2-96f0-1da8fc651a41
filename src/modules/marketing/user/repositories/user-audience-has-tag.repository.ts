import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, FindOneOptions } from 'typeorm';
import { UserAudienceHasTag } from '../entities/user-audience-has-tag.entity';

/**
 * Repository cho UserAudienceHasTag
 */
@Injectable()
export class UserAudienceHasTagRepository {
  constructor(
    @InjectRepository(UserAudienceHasTag)
    private readonly repository: Repository<UserAudienceHasTag>,
  ) {}

  /**
   * Tìm kiếm nhiều bản ghi
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách bản ghi
   */
  async find(options?: FindManyOptions<UserAudienceHasTag>): Promise<UserAudienceHasTag[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một bản ghi
   * @param options Tùy chọn tìm kiếm
   * @returns Bản ghi hoặc null
   */
  async findOne(options?: FindOneOptions<UserAudienceHasTag>): Promise<UserAudienceHasTag | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Xóa các bản ghi theo tag ID
   * @param tagId ID của tag
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByTagId(tagId: number): Promise<number> {
    const result = await this.repository.delete({ tagId });
    return result.affected || 0;
  }

  /**
   * Xóa các bản ghi theo nhiều tag ID
   * @param tagIds Danh sách ID của tag
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByTagIds(tagIds: number[]): Promise<number> {
    if (tagIds.length === 0) {
      return 0;
    }
    const result = await this.repository.createQueryBuilder()
      .delete()
      .where('tag_id IN (:...tagIds)', { tagIds })
      .execute();
    return result.affected || 0;
  }

  /**
   * Xóa các bản ghi theo audience ID
   * @param audienceId ID của audience
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByAudienceId(audienceId: number): Promise<number> {
    const result = await this.repository.delete({ audienceId });
    return result.affected || 0;
  }

  /**
   * Lưu bản ghi
   * @param record Bản ghi cần lưu
   * @returns Bản ghi đã lưu
   */
  async save(record: UserAudienceHasTag): Promise<UserAudienceHasTag>;
  async save(record: UserAudienceHasTag[]): Promise<UserAudienceHasTag[]>;
  async save(record: UserAudienceHasTag | UserAudienceHasTag[]): Promise<UserAudienceHasTag | UserAudienceHasTag[]> {
    return this.repository.save(record as any);
  }

  /**
   * Xóa bản ghi
   * @param record Bản ghi cần xóa
   * @returns Bản ghi đã xóa
   */
  async remove(record: UserAudienceHasTag): Promise<UserAudienceHasTag>;
  async remove(record: UserAudienceHasTag[]): Promise<UserAudienceHasTag[]>;
  async remove(record: UserAudienceHasTag | UserAudienceHasTag[]): Promise<UserAudienceHasTag | UserAudienceHasTag[]> {
    return this.repository.remove(record as any);
  }
}
