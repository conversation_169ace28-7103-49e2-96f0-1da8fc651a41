import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';

/**
 * Repository cho UserAudienceCustomField
 */
@Injectable()
export class UserAudienceCustomFieldRepository {
  constructor(
    @InjectRepository(UserAudienceCustomField)
    private readonly repository: Repository<UserAudienceCustomField>,
  ) {}

  /**
   * Tìm kiếm nhiều trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách trường tùy chỉnh
   */
  async find(options?: FindManyOptions<UserAudienceCustomField>): Promise<UserAudienceCustomField[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Trường tùy chỉnh hoặc null
   */
  async findOne(options?: FindOneOptions<UserAudienceCustomField>): Promise<UserAudienceCustomField | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu trường tùy chỉnh
   * @param customField Trường tùy chỉnh cần lưu
   * @returns Trường tùy chỉnh đã lưu
   */
  async save(customField: UserAudienceCustomField): Promise<UserAudienceCustomField>;
  async save(customField: UserAudienceCustomField[]): Promise<UserAudienceCustomField[]>;
  async save(customField: UserAudienceCustomField | UserAudienceCustomField[]): Promise<UserAudienceCustomField | UserAudienceCustomField[]> {
    return this.repository.save(customField as any);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: string | number | string[] | number[] | FindOptionsWhere<UserAudienceCustomField>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param customField Trường tùy chỉnh cần xóa
   * @returns Trường tùy chỉnh đã xóa
   */
  async remove(customField: UserAudienceCustomField): Promise<UserAudienceCustomField>;
  async remove(customField: UserAudienceCustomField[]): Promise<UserAudienceCustomField[]>;
  async remove(customField: UserAudienceCustomField | UserAudienceCustomField[]): Promise<UserAudienceCustomField | UserAudienceCustomField[]> {
    return this.repository.remove(customField as any);
  }
}
