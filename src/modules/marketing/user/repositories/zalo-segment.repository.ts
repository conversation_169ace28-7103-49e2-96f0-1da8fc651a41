import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloSegment } from '../entities';

/**
 * Repository cho phân đoạn Zalo
 */
@Injectable()
export class ZaloSegmentRepository {
  constructor(
    @InjectRepository(ZaloSegment)
    private readonly repository: Repository<ZaloSegment>,
  ) {}

  /**
   * Tìm phân đoạn theo ID
   * @param id ID của phân đoạn
   * @returns Phân đoạn
   */
  async findById(id: number): Promise<ZaloSegment | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm phân đoạn theo ID và ID người dùng
   * @param id ID của phân đoạn
   * @param userId ID của người dùng
   * @returns Phân đoạn
   */
  async findByIdAndUserId(id: number, userId: number): Promise<ZaloSegment | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm phân đoạn theo ID, ID người dùng và ID Official Account
   * @param id ID của phân đoạn
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Phân đoạn
   */
  async findByIdAndUserIdAndOaId(id: number, userId: number, oaId: string): Promise<ZaloSegment | null> {
    return this.repository.findOne({ where: { id, userId, oaId } });
  }

  /**
   * Tìm danh sách phân đoạn theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Danh sách phân đoạn
   */
  async findByUserIdAndOaId(userId: number, oaId: string): Promise<ZaloSegment[]> {
    return this.repository.find({ where: { userId, oaId } });
  }

  /**
   * Tìm danh sách phân đoạn với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách phân đoạn và tổng số phân đoạn
   */
  async findWithPagination(options: any): Promise<[ZaloSegment[], number]> {
    const { where, skip, take, order } = options;
    return this.repository.findAndCount({
      where,
      skip,
      take,
      order,
    });
  }

  /**
   * Tạo phân đoạn mới
   * @param data Dữ liệu phân đoạn
   * @returns Phân đoạn đã tạo
   */
  async create(data: Partial<ZaloSegment>): Promise<ZaloSegment> {
    const segment = this.repository.create(data);
    return this.repository.save(segment);
  }

  /**
   * Cập nhật phân đoạn
   * @param id ID của phân đoạn
   * @param data Dữ liệu cập nhật
   * @returns Phân đoạn đã cập nhật
   */
  async update(id: number, data: Partial<ZaloSegment>): Promise<ZaloSegment | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa phân đoạn
   * @param id ID của phân đoạn
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Cập nhật số lượng người theo dõi trong phân đoạn
   * @param id ID của phân đoạn
   * @param count Số lượng người theo dõi
   * @returns Phân đoạn đã cập nhật
   */
  async updateFollowerCount(id: number, count: number): Promise<ZaloSegment | null> {
    await this.repository.update(id, { followerCount: count });
    return this.findById(id);
  }
}
