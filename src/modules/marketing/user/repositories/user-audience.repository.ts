import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { UserAudience } from '../entities/user-audience.entity';

/**
 * Repository cho UserAudience
 */
@Injectable()
export class UserAudienceRepository {
  constructor(
    @InjectRepository(UserAudience)
    private readonly repository: Repository<UserAudience>,
  ) {}

  /**
   * Tìm kiếm nhiều audience
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách audience
   */
  async find(options?: FindManyOptions<UserAudience>): Promise<UserAudience[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một audience
   * @param options Tùy chọn tìm kiếm
   * @returns Audience hoặc null
   */
  async findOne(options?: FindOneOptions<UserAudience>): Promise<UserAudience | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng audience
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng audience
   */
  async count(options?: FindManyOptions<UserAudience>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lưu audience
   * @param audience Audience cần lưu
   * @returns Audience đã lưu
   */
  async save(audience: UserAudience): Promise<UserAudience>;
  async save(audience: UserAudience[]): Promise<UserAudience[]>;
  async save(audience: UserAudience | UserAudience[]): Promise<UserAudience | UserAudience[]> {
    return this.repository.save(audience as any);
  }

  /**
   * Xóa audience
   * @param audience Audience cần xóa
   * @returns Audience đã xóa
   */
  async remove(audience: UserAudience): Promise<UserAudience>;
  async remove(audience: UserAudience[]): Promise<UserAudience[]>;
  async remove(audience: UserAudience | UserAudience[]): Promise<UserAudience | UserAudience[]> {
    return this.repository.remove(audience as any);
  }

  /**
   * Tạo mới audience
   * @param data Dữ liệu audience
   * @returns Audience đã tạo
   */
  async create(data: Partial<UserAudience>): Promise<UserAudience> {
    const audience = this.repository.create(data);
    return this.repository.save(audience);
  }
}
