import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho loại thống kê
 */
export enum StatisticsType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

/**
 * DTO cho tham số truy vấn thống kê marketing
 */
export class MarketingStatisticsQueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu thống kê (Unix timestamp)',
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  startDate?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc thống kê (Unix timestamp)',
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  endDate?: number;

  @ApiPropertyOptional({
    description: 'Loại thống kê',
    enum: StatisticsType,
    default: StatisticsType.MONTHLY
  })
  @IsOptional()
  @IsEnum(StatisticsType)
  type?: StatisticsType = StatisticsType.MONTHLY;
}
