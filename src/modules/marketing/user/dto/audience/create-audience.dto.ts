import { IsEmail, IsNotEmpty, IsOptional, IsString, ValidateNested, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateCustomFieldDto } from './create-custom-field.dto';
import { IsPhoneWithCountry } from '../../validators/phone-with-country.validator';

/**
 * DTO cho việc tạo audience mới
 */
export class CreateAudienceDto {
  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên không được để trống' })
  @IsString({ message: 'Tên phải là chuỗi' })
  name: string;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: '<PERSON>ail của khách hàng',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;

  /**
   * Số điện thoại của khách hàng
   * @example "912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '912345678',
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsPhoneWithCountry({ message: 'Số điện thoại không hợp lệ với mã quốc gia được cung cấp' })
  phone?: string;

  /**
   * Mã quốc gia của số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: '+84',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã quốc gia phải là chuỗi' })
  @Matches(/^\+\d{1,4}$/, { message: 'Mã quốc gia không hợp lệ (ví dụ: +84, +1)' })
  countryCode?: string;

  /**
   * URL avatar của khách hàng (S3 key)
   * @example "customer_avatars/2024/01/1234567890-uuid.jpg"
   */
  @ApiProperty({
    description: 'URL avatar của khách hàng (S3 key)',
    example: 'customer_avatars/2024/01/1234567890-uuid.jpg',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Avatar phải là chuỗi' })
  avatar?: string;

  /**
   * Danh sách các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Danh sách ID của các tag
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của các tag',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
