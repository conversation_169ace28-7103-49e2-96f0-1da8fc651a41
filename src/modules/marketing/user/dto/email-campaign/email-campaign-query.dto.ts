import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';
import { CampaignStatus } from '@/modules/marketing/enums/campaign-status.enum';

/**
 * DTO cho query parameters khi lấy danh sách email campaign
 */
export class EmailCampaignQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái campaign
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái campaign',
    enum: CampaignStatus,
    example: CampaignStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CampaignStatus)
  status?: CampaignStatus;

  /**
   * Tìm kiếm theo tiêu đề campaign
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tiêu đề campaign',
    example: 'khuyến mãi',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  /**
   * Tì<PERSON> kiếm theo subject email
   */
  @ApiProperty({
    description: 'T<PERSON><PERSON> kiếm theo subject email',
    example: 'Black Friday',
    required: false,
  })
  @IsOptional()
  @IsString()
  subject?: string;
}
