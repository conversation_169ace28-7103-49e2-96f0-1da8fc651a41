import { ApiProperty } from '@nestjs/swagger';
import { PaginationMetaDto } from '../common/paginated-response.dto';

/**
 * DTO cho thông tin email campaign trong danh sách
 */
export class EmailCampaignItemDto {
  /**
   * ID của campaign
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề campaign
   * @example "Khuyến mãi Black Friday 2024"
   */
  @ApiProperty({
    description: 'Tiêu đề campaign',
    example: 'Khuyến mãi Black Friday 2024',
  })
  title: string;

  /**
   * <PERSON><PERSON> tả campaign
   * @example "Chiến dịch email marketing cho sự kiện Black Friday"
   */
  @ApiProperty({
    description: 'Mô tả campaign',
    example: 'Chiến dịch email marketing cho sự kiện Black Friday',
    required: false,
  })
  description?: string;

  /**
   * Tiêu đề email
   * @example "🔥 Black Friday Sale - Giảm giá lên đến 70%!"
   */
  @ApiProperty({
    description: 'Tiêu đề email',
    example: '🔥 Black Friday Sale - Giảm giá lên đến 70%!',
  })
  subject: string;

  /**
   * Trạng thái campaign
   * @example "running"
   */
  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'running',
  })
  status: string;

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp)',
    example: 1703980800,
    required: false,
  })
  scheduledAt?: number;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1703980800,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1703980800,
  })
  updatedAt: number;

  /**
   * Tổng số người nhận
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 150,
    required: false,
  })
  totalRecipients?: number;

  /**
   * Số lượng đã gửi
   * @example 120
   */
  @ApiProperty({
    description: 'Số lượng đã gửi',
    example: 120,
    required: false,
  })
  sentCount?: number;

  /**
   * Tỷ lệ gửi thành công (%)
   * @example 80.5
   */
  @ApiProperty({
    description: 'Tỷ lệ gửi thành công (%)',
    example: 80.5,
    required: false,
  })
  sentRate?: number;

  /**
   * Số lượng đã click
   * @example 25
   */
  @ApiProperty({
    description: 'Số lượng đã click',
    example: 25,
    required: false,
  })
  clickedCount?: number;

  /**
   * Tỷ lệ click (%)
   * @example 20.8
   */
  @ApiProperty({
    description: 'Tỷ lệ click (%)',
    example: 20.8,
    required: false,
  })
  clickRate?: number;
}

/**
 * DTO cho phản hồi danh sách email campaign có phân trang
 */
export class EmailCampaignListResponseDto {
  /**
   * Danh sách email campaign
   */
  @ApiProperty({
    description: 'Danh sách email campaign',
    type: [EmailCampaignItemDto],
  })
  data: EmailCampaignItemDto[];

  /**
   * Thông tin phân trang
   */
  @ApiProperty({
    description: 'Thông tin phân trang',
    type: PaginationMetaDto,
  })
  meta: PaginationMetaDto;
}
