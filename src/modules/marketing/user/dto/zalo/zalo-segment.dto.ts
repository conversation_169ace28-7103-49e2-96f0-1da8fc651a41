import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * Enum cho loại điều kiện phân đoạn
 */
export enum ZaloSegmentConditionType {
  TAG = 'tag',
  GENDER = 'gender',
  FOLLOW_DATE = 'follow_date',
  INTERACTION = 'interaction',
}

/**
 * Enum cho toán tử so sánh
 */
export enum ZaloSegmentOperator {
  EQUAL = 'equal',
  NOT_EQUAL = 'not_equal',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  BETWEEN = 'between',
}

/**
 * DTO cho điều kiện phân đoạn Zalo
 */
export class ZaloSegmentConditionDto {
  @ApiProperty({
    description: 'Loại điều kiện',
    enum: ZaloSegmentConditionType,
    example: ZaloSegmentConditionType.TAG,
  })
  @IsEnum(ZaloSegmentConditionType)
  @IsNotEmpty()
  type: ZaloSegmentConditionType;

  @ApiProperty({
    description: 'Toán tử so sánh',
    enum: ZaloSegmentOperator,
    example: ZaloSegmentOperator.CONTAINS,
  })
  @IsEnum(ZaloSegmentOperator)
  @IsNotEmpty()
  operator: ZaloSegmentOperator;

  @ApiProperty({
    description: 'Giá trị điều kiện',
    example: ['vip', 'new-customer'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  values: string[];
}

/**
 * DTO cho việc tạo phân đoạn Zalo
 */
export class CreateZaloSegmentDto {
  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  @IsString()
  @IsNotEmpty()
  oaId: string;

  @ApiProperty({
    description: 'Tên của phân đoạn',
    example: 'Khách hàng VIP',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mô tả của phân đoạn',
    example: 'Phân đoạn dành cho khách hàng VIP',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Danh sách điều kiện phân đoạn',
    type: [ZaloSegmentConditionDto],
  })
  @IsArray()
  @IsNotEmpty()
  conditions: ZaloSegmentConditionDto[];
}

/**
 * DTO cho việc cập nhật phân đoạn Zalo
 */
export class UpdateZaloSegmentDto {
  @ApiProperty({
    description: 'Tên của phân đoạn',
    example: 'Khách hàng VIP - Cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Mô tả của phân đoạn',
    example: 'Phân đoạn dành cho khách hàng VIP - Cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Danh sách điều kiện phân đoạn',
    type: [ZaloSegmentConditionDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  conditions?: ZaloSegmentConditionDto[];
}

/**
 * DTO cho phản hồi thông tin phân đoạn Zalo
 */
export class ZaloSegmentResponseDto {
  @ApiProperty({
    description: 'ID của phân đoạn',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  oaId: string;

  @ApiProperty({
    description: 'Tên của phân đoạn',
    example: 'Khách hàng VIP',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả của phân đoạn',
    example: 'Phân đoạn dành cho khách hàng VIP',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'Danh sách điều kiện phân đoạn',
    type: [ZaloSegmentConditionDto],
  })
  conditions: ZaloSegmentConditionDto[];

  @ApiProperty({
    description: 'Số lượng người theo dõi trong phân đoạn',
    example: 100,
  })
  followerCount: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;
}

/**
 * DTO cho việc truy vấn danh sách phân đoạn Zalo
 */
export class ZaloSegmentQueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên phân đoạn',
    example: 'VIP',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Số trang',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng phân đoạn trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  limit?: number = 10;
}
