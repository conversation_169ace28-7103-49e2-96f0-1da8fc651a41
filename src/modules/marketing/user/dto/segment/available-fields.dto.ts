import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho các loại dữ liệu của field
 */
export enum FieldType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  BOOLEAN = 'BOOLEAN',
  URL = 'URL',
  IMAGE = 'IMAGE',
}

/**
 * DTO cho thông tin một field có thể sử dụng trong segment criteria
 */
export class AvailableFieldDto {
  /**
   * Tên trường (field name)
   * @example "email"
   */
  @ApiProperty({
    description: 'Tên trường (field name)',
    example: 'email',
  })
  fieldName: string;

  /**
   * Loại dữ liệu của trường
   * @example "EMAIL"
   */
  @ApiProperty({
    description: 'Loại dữ liệu của trường',
    example: 'EMAIL',
    enum: FieldType,
  })
  fieldType: FieldType;

  /**
   * Tên hiển thị thân thiện với người dùng
   * @example "Địa chỉ email"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với người dùng',
    example: 'Địa chỉ email',
  })
  displayName: string;

  /**
   * Có phải là trường tùy chỉnh hay không
   * @example false
   */
  @ApiProperty({
    description: 'Có phải là trường tùy chỉnh hay không',
    example: false,
  })
  isCustom: boolean;

  /**
   * Mô tả trường (tùy chọn)
   * @example "Địa chỉ email của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả trường (tùy chọn)',
    example: 'Địa chỉ email của khách hàng',
    required: false,
  })
  description?: string;

  /**
   * Các toán tử có thể sử dụng với trường này
   * @example ["EQUALS", "NOT_EQUALS", "CONTAINS", "NOT_CONTAINS"]
   */
  @ApiProperty({
    description: 'Các toán tử có thể sử dụng với trường này',
    example: ['EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS'],
    type: [String],
  })
  availableOperators: string[];
}

/**
 * DTO cho response danh sách các field có thể sử dụng
 */
export class AvailableFieldsResponseDto {
  /**
   * Danh sách các trường built-in
   * @example [{"fieldName": "email", "fieldType": "EMAIL", "displayName": "Địa chỉ email", "isCustom": false}]
   */
  @ApiProperty({
    description: 'Danh sách các trường built-in',
    type: [AvailableFieldDto],
    example: [
      {
        fieldName: 'email',
        fieldType: 'EMAIL',
        displayName: 'Địa chỉ email',
        isCustom: false,
        description: 'Địa chỉ email của khách hàng',
        availableOperators: ['EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS']
      }
    ],
  })
  builtInFields: AvailableFieldDto[];

  /**
   * Danh sách các trường tùy chỉnh
   * @example [{"fieldName": "age", "fieldType": "NUMBER", "displayName": "Tuổi", "isCustom": true}]
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [AvailableFieldDto],
    example: [
      {
        fieldName: 'age',
        fieldType: 'NUMBER',
        displayName: 'Tuổi',
        isCustom: true,
        description: 'Tuổi của khách hàng',
        availableOperators: ['EQUALS', 'NOT_EQUALS', 'GREATER_THAN', 'LESS_THAN']
      }
    ],
  })
  customFields: AvailableFieldDto[];

  /**
   * Tổng số trường có thể sử dụng
   * @example 8
   */
  @ApiProperty({
    description: 'Tổng số trường có thể sử dụng',
    example: 8,
  })
  totalFields: number;
}
