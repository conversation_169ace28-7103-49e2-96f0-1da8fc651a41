import { IsArray, IsIn, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

/**
 * Enum cho các loại điều kiện
 */
export enum ConditionType {
  AND = 'AND',
  OR = 'OR',
}

/**
 * Enum cho các toán tử so sánh
 */
export enum OperatorType {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  IN = 'in',
  NOT_IN = 'not_in',
  EXISTS = 'exists',
  NOT_EXISTS = 'not_exists',
}

/**
 * DTO cho điều kiện lọc
 */
export class FilterConditionDto {
  /**
   * ID của điều kiện (optional, dùng cho frontend tracking)
   * @example "condition-1749267747600"
   */
  @ApiProperty({
    description: 'ID của điều kiện',
    example: 'condition-1749267747600',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID điều kiện phải là chuỗi' })
  id?: string;

  /**
   * Tên trường cần lọc
   * @example "email"
   */
  @ApiProperty({
    description: 'Tên trường cần lọc',
    example: 'email',
  })
  @IsNotEmpty({ message: 'Tên trường không được để trống' })
  @IsString({ message: 'Tên trường phải là chuỗi' })
  field: string;

  /**
   * Toán tử so sánh
   * @example "contains"
   */
  @ApiProperty({
    description: 'Toán tử so sánh',
    enum: OperatorType,
    example: OperatorType.CONTAINS,
  })
  @IsNotEmpty({ message: 'Toán tử không được để trống' })
  @IsIn(Object.values(OperatorType), {
    message: `Toán tử phải là một trong các giá trị: ${Object.values(OperatorType).join(', ')}`,
  })
  operator: OperatorType;

  /**
   * Giá trị cần so sánh
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Giá trị cần so sánh',
    example: 'example.com',
  })
  @IsOptional()
  value?: any;
}

/**
 * DTO cho nhóm điều kiện lọc
 */
export class SegmentGroupDto {
  /**
   * ID của nhóm (optional, dùng cho frontend tracking)
   * @example "group-1749267747600"
   */
  @ApiProperty({
    description: 'ID của nhóm',
    example: 'group-1749267747600',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID nhóm phải là chuỗi' })
  id?: string;

  /**
   * Toán tử logic cho nhóm (AND/OR)
   * @example "AND"
   */
  @ApiProperty({
    description: 'Toán tử logic cho nhóm (AND/OR)',
    enum: ConditionType,
    example: ConditionType.AND,
  })
  @IsNotEmpty({ message: 'Toán tử logic không được để trống' })
  @IsIn(Object.values(ConditionType), {
    message: `Toán tử logic phải là một trong các giá trị: ${Object.values(ConditionType).join(', ')}`,
  })
  logicalOperator: ConditionType;

  /**
   * Danh sách các điều kiện lọc trong nhóm
   */
  @ApiProperty({
    description: 'Danh sách các điều kiện lọc trong nhóm',
    type: [FilterConditionDto],
  })
  @IsArray({ message: 'Danh sách điều kiện phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => FilterConditionDto)
  conditions: FilterConditionDto[];
}

/**
 * DTO cho tiêu chí segment (root level)
 */
export class SegmentCriteriaDto {
  /**
   * Danh sách các nhóm điều kiện
   */
  @ApiProperty({
    description: 'Danh sách các nhóm điều kiện',
    type: [SegmentGroupDto],
  })
  @IsArray({ message: 'Danh sách nhóm điều kiện phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => SegmentGroupDto)
  groups: SegmentGroupDto[];
}
