import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsObject, IsOptional, IsString } from 'class-validator';
import { CustomFieldDataType } from './create-audience-custom-field-definition.dto';

/**
 * DTO cho việc cập nhật trường tùy chỉnh
 */
export class UpdateAudienceCustomFieldDefinitionDto {
  /**
   * Tên hiển thị thân thiện với người dùng
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với người dùng',
    example: 'Địa chỉ khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName?: string;

  /**
   * Kiểu dữ liệu: text, number, boolean, date, select, object
   * @example "text"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.TEXT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldDataType, {
    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,
  })
  dataType?: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   * @example "Địa chỉ liên hệ của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
    example: 'Địa chỉ liên hệ của khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Danh sách tags để phân loại trường tùy chỉnh
   * @example ["contact", "personal"]
   */
  @ApiProperty({
    description: 'Danh sách tags để phân loại trường tùy chỉnh',
    example: ['contact', 'personal'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];

  /**
   * Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)
   * @example {"required": true, "maxLength": 255, "options": ["option1", "option2"]}
   */
  @ApiProperty({
    description: 'Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)',
    example: { required: true, maxLength: 255, options: ['option1', 'option2'] },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Config phải là object' })
  config?: Record<string, any>;
}
