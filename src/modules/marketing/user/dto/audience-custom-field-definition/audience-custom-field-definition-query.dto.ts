import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { CustomFieldDataType } from './create-audience-custom-field-definition.dto';

/**
 * Enum cho các trường sắp xếp
 */
export enum CustomFieldDefinitionSortField {
  ID = 'id',
  FIELD_KEY = 'fieldKey',
  DISPLAY_NAME = 'displayName',
  DATA_TYPE = 'dataType',
}

/**
 * DTO cho truy vấn danh sách trường tùy chỉnh
 */
export class AudienceCustomFieldDefinitionQueryDto extends QueryDto {

  /**
   * Tìm kiếm theo định danh
   * @example "customer"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo định danh',
    example: 'customer',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON>ịnh danh phải là chuỗi' })
  fieldKey?: string;

  /**
   * Tìm kiếm theo tên hiển thị
   * @example "Khách hàng"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên hiển thị',
    example: 'Khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName?: string;

  /**
   * Tìm kiếm theo kiểu dữ liệu
   * @example "text"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo kiểu dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.TEXT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldDataType, {
    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,
  })
  dataType?: CustomFieldDataType;

  /**
   * Sắp xếp theo trường
   * @example "displayName"
   */
  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: CustomFieldDefinitionSortField,
    example: CustomFieldDefinitionSortField.DISPLAY_NAME,
    default: CustomFieldDefinitionSortField.DISPLAY_NAME,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldDefinitionSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(CustomFieldDefinitionSortField).join(', ')}`,
  })
  sortBy?: CustomFieldDefinitionSortField = CustomFieldDefinitionSortField.DISPLAY_NAME;

  /**
   * Ghi đè thuộc tính sortDirection từ QueryDto để thay đổi giá trị mặc định
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    enum: SortDirection,
    example: SortDirection.ASC,
    default: SortDirection.ASC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, {
    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,
  })
  override sortDirection?: SortDirection = SortDirection.ASC;
}
