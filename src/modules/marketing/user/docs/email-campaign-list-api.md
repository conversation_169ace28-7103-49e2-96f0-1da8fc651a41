# API Lấy Danh Sách Chiến Dịch Email

## Endpoint
```
GET /marketing/email-campaigns
```

## <PERSON><PERSON> tả
API này cho phép lấy danh sách các chiến dịch email của người dùng với phân trang, filter và thống kê chi tiết.

## Authentication
- Yêu cầu JWT token trong header `Authorization: Bearer <token>`
- Chỉ lấy được chiến dịch của chính user đó

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Số trang hiện tại (bắt đầu từ 1) |
| `limit` | number | No | 10 | Số lượng bản ghi trên mỗi trang (tối đa 100) |
| `search` | string | No | - | Từ khóa tìm kiếm trong title |
| `sortBy` | string | No | createdAt | Trường cần sắp xếp |
| `sortDirection` | string | No | DESC | Hướng sắp xếp (ASC/DESC) |
| `status` | string | No | - | Lọc theo trạng thái campaign |
| `title` | string | No | - | Tìm kiếm theo tiêu đề campaign |
| `subject` | string | No | - | Tìm kiếm theo subject email |

### Các giá trị status hợp lệ:
- `draft` - Bản nháp
- `scheduled` - Đã lên lịch
- `running` - Đang chạy
- `completed` - Đã hoàn thành
- `paused` - Tạm dừng
- `cancelled` - Đã hủy

## Response Format

### Success Response (200)
```json
{
  "success": true,
  "message": "Danh sách chiến dịch email",
  "data": {
    "data": [
      {
        "id": 1,
        "title": "Khuyến mãi Black Friday 2024",
        "description": "Chiến dịch email marketing cho sự kiện Black Friday",
        "subject": "🔥 Black Friday Sale - Giảm giá lên đến 70%!",
        "status": "running",
        "scheduledAt": 1703980800,
        "createdAt": 1703980800,
        "updatedAt": 1703980800,
        "totalRecipients": 150,
        "sentCount": 120,
        "sentRate": 80.0,
        "clickedCount": 25,
        "clickRate": 20.8
      }
    ],
    "meta": {
      "total": 25,
      "page": 1,
      "limit": 10,
      "totalPages": 3,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### Error Response (401)
```json
{
  "success": false,
  "message": "Không có quyền truy cập",
  "error": "Unauthorized"
}
```

## Ví dụ sử dụng

### 1. Lấy danh sách cơ bản
```bash
GET /marketing/email-campaigns
```

### 2. Lấy trang 2 với 20 items
```bash
GET /marketing/email-campaigns?page=2&limit=20
```

### 3. Tìm kiếm theo title
```bash
GET /marketing/email-campaigns?title=Black Friday
```

### 4. Lọc theo trạng thái
```bash
GET /marketing/email-campaigns?status=running
```

### 5. Tìm kiếm và lọc kết hợp
```bash
GET /marketing/email-campaigns?search=khuyến mãi&status=completed&page=1&limit=5
```

### 6. Sắp xếp theo thời gian tạo tăng dần
```bash
GET /marketing/email-campaigns?sortBy=createdAt&sortDirection=ASC
```

## Thống kê trong Response

Mỗi campaign trong danh sách sẽ bao gồm các thống kê:

- **totalRecipients**: Tổng số người nhận
- **sentCount**: Số lượng email đã gửi thành công
- **sentRate**: Tỷ lệ gửi thành công (%)
- **clickedCount**: Số lượng người đã click
- **clickRate**: Tỷ lệ click (%)

## Lưu ý

1. API chỉ trả về email campaigns (platform = 'email')
2. Thống kê được tính toán real-time từ bảng `user_campaign_history`
3. Tỷ lệ được làm tròn đến 1 chữ số thập phân
4. Nếu không có dữ liệu thống kê, các trường tương ứng sẽ là `undefined`
5. Thời gian được trả về dưới dạng Unix timestamp (seconds)

## Performance

- API sử dụng pagination để tối ưu hiệu suất
- Thống kê được tính toán cho từng campaign trong batch
- Recommend sử dụng limit phù hợp (10-50) để đảm bảo response time tốt
