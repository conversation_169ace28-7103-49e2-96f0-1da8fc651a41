# API Preview Segment - Tính Tổng Audience

## Tổng quan

API này cho phép tính tổng số audience phù hợp với điều kiện segment mà không cần tạo segment trước. Điều này hữu ích khi người dùng muốn xem trước số lượng audience sẽ được lọc trước khi tạo segment thực tế.

## Endpoint

```
POST /v1/user/marketing/segments/preview
```

## Authentication

Yêu cầu JWT token của user trong header:
```
Authorization: Bearer <jwt_token>
```

## Request Body

```typescript
{
  "criteria": {
    "conditionType": "and" | "or",
    "conditions": [
      {
        "field": "email" | "phone" | "<custom_field_name>",
        "operator": "equals" | "not_equals" | "contains" | "not_contains" | "greater_than" | "less_than" | "in" | "not_in" | "exists" | "not_exists",
        "value": any
      }
    ],
    "groups": [
      // Nested criteria groups (optional)
    ]
  }
}
```

## Response

```typescript
{
  "success": true,
  "message": "Thống kê audience phù hợp với điều kiện segment",
  "result": {
    "totalAudiences": 150,           // Số audience phù hợp với điều kiện
    "percentageOfTotal": 0.75,       // Tỷ lệ % so với tổng audience
    "totalUserAudiences": 200        // Tổng số audience của user
  }
}
```

## Ví dụ sử dụng

### 1. Lọc audience theo email domain

```bash
curl -X POST /v1/user/marketing/segments/preview \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "criteria": {
      "conditionType": "and",
      "conditions": [
        {
          "field": "email",
          "operator": "contains",
          "value": "@gmail.com"
        }
      ]
    }
  }'
```

### 2. Lọc audience với nhiều điều kiện

```bash
curl -X POST /v1/user/marketing/segments/preview \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "criteria": {
      "conditionType": "and",
      "conditions": [
        {
          "field": "email",
          "operator": "contains",
          "value": "@gmail.com"
        },
        {
          "field": "phone",
          "operator": "exists",
          "value": null
        }
      ]
    }
  }'
```

### 3. Lọc audience với custom fields

```bash
curl -X POST /v1/user/marketing/segments/preview \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "criteria": {
      "conditionType": "or",
      "conditions": [
        {
          "field": "age",
          "operator": "greater_than",
          "value": 25
        },
        {
          "field": "city",
          "operator": "in",
          "value": ["Hà Nội", "TP.HCM"]
        }
      ]
    }
  }'
```

## Các toán tử hỗ trợ

- `equals`: Bằng chính xác
- `not_equals`: Không bằng
- `contains`: Chứa chuỗi con (cho string)
- `not_contains`: Không chứa chuỗi con
- `greater_than`: Lớn hơn (cho số)
- `less_than`: Nhỏ hơn (cho số)
- `in`: Có trong danh sách
- `not_in`: Không có trong danh sách
- `exists`: Trường tồn tại và không null
- `not_exists`: Trường không tồn tại hoặc null

## Lưu ý

1. API này chỉ tính toán và trả về số lượng, không tạo segment thực tế
2. Có thể sử dụng để preview trước khi tạo segment
3. Hỗ trợ cả basic fields (email, phone) và custom fields
4. Có thể tạo điều kiện phức tạp với nested groups
