# API Báo Cáo Hiệu Quả Email Marketing

## Tổng quan
Bộ API báo cáo hiệu quả email marketing cung cấp các thống kê chi tiết về hiệu suất chiến dịch email của người dùng.

## Authentication
- Tất cả API đều yêu cầu JWT token trong header `Authorization: Bearer <token>`
- Chỉ lấy được dữ liệu của chính user đó

---

## 1. API Overview Dashboard

### Endpoint
```
GET /marketing/email-campaigns/reports/overview
```

### Mô tả
Trả về tổng quan thống kê toàn bộ email marketing của user.

### Response
```json
{
  "success": true,
  "message": "Thống kê tổng quan email marketing",
  "data": {
    "totalSent": 1500,
    "totalOpened": 750,
    "totalClicks": 300,
    "totalUnsubscribed": 25
  }
}
```

---

## 2. API Performance Metrics

### Endpoint
```
GET /marketing/email-campaigns/reports/performance
```

### Mô tả
Trả về các tỷ lệ hiệu suất email marketing.

### Response
```json
{
  "success": true,
  "message": "Các tỷ lệ hiệu suất email marketing",
  "data": {
    "openRate": 50.0,
    "clickRate": 20.0,
    "bounceRate": 5.0,
    "unsubscribeRate": 1.7
  }
}
```

---

## 3. API Trend Chart

### Endpoint
```
GET /marketing/email-campaigns/reports/trends
```

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `startDate` | number | No | Thời gian bắt đầu (Unix timestamp) |
| `endDate` | number | No | Thời gian kết thúc (Unix timestamp) |

### Mô tả
Trả về xu hướng theo thời gian với data points cho biểu đồ.

### Response
```json
{
  "success": true,
  "message": "Xu hướng email marketing theo thời gian",
  "data": {
    "dates": ["2024-01-01", "2024-01-02", "2024-01-03"],
    "sent": [100, 150, 120],
    "opened": [50, 75, 60],
    "clicked": [20, 30, 25]
  }
}
```

### Ví dụ sử dụng
```bash
# Lấy xu hướng 30 ngày gần đây
GET /marketing/email-campaigns/reports/trends

# Lấy xu hướng trong khoảng thời gian cụ thể
GET /marketing/email-campaigns/reports/trends?startDate=1703980800&endDate=1704067200
```

---

## 4. API Campaign Comparison

### Endpoint
```
GET /marketing/email-campaigns/reports/comparison
```

### Mô tả
So sánh các chiến dịch với chỉ số hiệu suất.

### Response
```json
{
  "success": true,
  "message": "So sánh các chiến dịch email",
  "data": {
    "campaigns": [
      {
        "name": "Khuyến mãi Black Friday 2024",
        "sent": 500,
        "opened": 250,
        "clicked": 100
      },
      {
        "name": "Newsletter tháng 12",
        "sent": 300,
        "opened": 120,
        "clicked": 45
      }
    ]
  }
}
```

---

## 5. API Campaign Performance List

### Endpoint
```
GET /marketing/email-campaigns/reports/campaigns
```

### Query Parameters
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Số trang hiện tại |
| `limit` | number | No | 10 | Số lượng bản ghi trên mỗi trang |
| `sortBy` | string | No | createdAt | Trường cần sắp xếp |
| `sortDirection` | string | No | DESC | Hướng sắp xếp (ASC/DESC) |

### Mô tả
Danh sách có phân trang về hiệu quả từng chiến dịch.

### Response
```json
{
  "success": true,
  "message": "Danh sách hiệu quả từng chiến dịch email",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Khuyến mãi Black Friday 2024",
        "recipients": 500,
        "opened": 250,
        "openRate": 50.0,
        "clicked": 100,
        "clickRate": 20.0,
        "status": "completed",
        "totalEmails": 500
      }
    ],
    "meta": {
      "total": 25,
      "page": 1,
      "limit": 10,
      "totalPages": 3,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### Ví dụ sử dụng
```bash
# Lấy danh sách cơ bản
GET /marketing/email-campaigns/reports/campaigns

# Lấy trang 2 với 20 items
GET /marketing/email-campaigns/reports/campaigns?page=2&limit=20

# Sắp xếp theo tỷ lệ mở
GET /marketing/email-campaigns/reports/campaigns?sortBy=openRate&sortDirection=DESC
```

---

## Cách tính toán Metrics

### Status Mapping trong user_campaign_history:
- **Email đã gửi**: `sent`, `delivered`, `opened`, `clicked`
- **Email đã mở**: `opened`, `clicked`
- **Email đã click**: `clicked`
- **Email bounce**: `failed`
- **Email hủy đăng ký**: `unsubscribed`

### Công thức tính:
- **Open Rate** = (Total Opened / Total Sent) × 100
- **Click Rate** = (Total Clicked / Total Opened) × 100
- **Bounce Rate** = (Total Bounced / Total Sent) × 100
- **Unsubscribe Rate** = (Total Unsubscribed / Total Sent) × 100

---

## Lưu ý

1. **Performance**: Các API sử dụng tính toán real-time từ database
2. **Caching**: Recommend implement caching cho các API thống kê
3. **Time Range**: Trend API mặc định lấy 30 ngày gần đây nếu không có startDate/endDate
4. **Precision**: Tỷ lệ được làm tròn đến 1 chữ số thập phân
5. **Empty Data**: Nếu không có dữ liệu, trả về giá trị 0 hoặc array rỗng
6. **Timezone**: Thời gian sử dụng Unix timestamp (UTC)
