import { Injectable, NotFoundException } from '@nestjs/common';
import { FindOptionsWhere, Like } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { UserAudienceHasTagRepository } from '../repositories/user-audience-has-tag.repository';
import { CreateTagDto, TagResponseDto, UpdateTagDto, TagQueryDto, TagSortField, SortDirection } from '../dto/tag';
import { UserTag } from '../entities/user-tag.entity';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { PaginatedResult, PaginationMeta } from '@/common/response';

/**
 * Service xử lý logic liên quan đến tag
 */
@Injectable()
export class UserTagService {
  constructor(
    private readonly userTagRepository: UserTagRepository,
    private readonly userAudienceHasTagRepository: UserAudienceHasTagRepository,
  ) {}

  /**
   * Tạo tag mới
   * @param userId ID của người dùng
   * @param createTagDto Dữ liệu tạo tag
   * @returns Thông tin tag đã tạo
   */
  async create(userId: number, createTagDto: CreateTagDto): Promise<TagResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    const tag = new UserTag();
    tag.userId = userId;
    tag.name = createTagDto.name;
    tag.color = createTagDto.color || '#3498db'; // Màu mặc định nếu không được cung cấp
    tag.createdAt = now;
    tag.updatedAt = now;

    const savedTag = await this.userTagRepository.save(tag);
    return this.mapToDto(savedTag as UserTag);
  }

  /**
   * Cập nhật tag
   * @param userId ID của người dùng
   * @param id ID của tag
   * @param updateTagDto Dữ liệu cập nhật tag
   * @returns Thông tin tag đã cập nhật
   */
  async update(userId: number, id: number, updateTagDto: UpdateTagDto): Promise<TagResponseDto> {
    const tag = await this.userTagRepository.findOne({ where: { id, userId } });
    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    if (updateTagDto.name !== undefined) {
      tag.name = updateTagDto.name;
    }

    if (updateTagDto.color !== undefined) {
      tag.color = updateTagDto.color;
    }

    tag.updatedAt = Math.floor(Date.now() / 1000);

    const updatedTag = await this.userTagRepository.save(tag);
    return this.mapToDto(updatedTag as UserTag);
  }

  /**
   * Xóa tag
   * @param userId ID của người dùng
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<boolean> {
    const tag = await this.userTagRepository.findOne({ where: { id, userId } });
    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    // Xóa các bản ghi liên quan trong bảng user_audience_has_tags trước
    await this.userAudienceHasTagRepository.deleteByTagId(id);

    // Sau đó xóa tag
    await this.userTagRepository.remove(tag);
    return true;
  }

  /**
   * Xóa nhiều tag
   * @param userId ID của người dùng
   * @param ids Danh sách ID tag cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(userId: number, ids: number[]): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];
    const validTagIds: number[] = [];

    // Kiểm tra tất cả tag trước
    for (const id of ids) {
      try {
        const tag = await this.userTagRepository.findOne({ where: { id, userId } });
        if (!tag) {
          failedIds.push(id);
          continue;
        }
        validTagIds.push(id);
      } catch (error) {
        failedIds.push(id);
      }
    }

    // Xóa các bản ghi liên quan trong bảng user_audience_has_tags trước (bulk delete)
    if (validTagIds.length > 0) {
      await this.userAudienceHasTagRepository.deleteByTagIds(validTagIds);
    }

    // Sau đó xóa từng tag
    for (const id of validTagIds) {
      try {
        const tag = await this.userTagRepository.findOne({ where: { id, userId } });
        if (tag) {
          await this.userTagRepository.remove(tag);
          deletedIds.push(id);
        }
      } catch (error) {
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} tag thành công, ${failedCount} tag không thể xóa`
      : `Đã xóa ${deletedCount} tag thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy danh sách tag của người dùng (không phân trang - deprecated)
   * @param userId ID của người dùng
   * @returns Danh sách tag
   * @deprecated Sử dụng findAllPaginated thay thế
   */
  async findAll(userId: number): Promise<TagResponseDto[]> {
    const tags = await this.userTagRepository.find({ where: { userId } });
    return tags.map(tag => this.mapToDto(tag));
  }

  /**
   * Lấy danh sách tag của người dùng với phân trang và filter
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách tag với phân trang
   */
  async findAllPaginated(userId: number, query: TagQueryDto): Promise<PaginatedResult<TagResponseDto>> {
    const { page = 1, limit = 10, search, name, sortBy = TagSortField.CREATED_AT, sortDirection = SortDirection.DESC } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<UserTag> = { userId };

    // Ưu tiên sử dụng search trước, fallback về name để backward compatibility
    const searchTerm = search || name;

    let tags: UserTag[];
    let total: number;

    if (searchTerm) {
      // Kiểm tra nếu searchTerm là số (có thể là ID)
      const isNumeric = /^\d+$/.test(searchTerm.trim());

      if (isNumeric) {
        // Tìm kiếm theo cả ID và tên sử dụng query builder
        const queryBuilder = this.userTagRepository['repository'].createQueryBuilder('tag');
        queryBuilder
          .where('tag.userId = :userId', { userId })
          .andWhere('(tag.id = :id OR tag.name ILIKE :name)', {
            id: parseInt(searchTerm.trim()),
            name: `%${searchTerm}%`
          })
          .orderBy(`tag.${sortBy}`, sortDirection)
          .skip(offset)
          .take(limit);

        tags = await queryBuilder.getMany();

        // Đếm tổng số với cùng điều kiện
        const countQueryBuilder = this.userTagRepository['repository'].createQueryBuilder('tag');
        countQueryBuilder
          .where('tag.userId = :userId', { userId })
          .andWhere('(tag.id = :id OR tag.name ILIKE :name)', {
            id: parseInt(searchTerm.trim()),
            name: `%${searchTerm}%`
          });

        total = await countQueryBuilder.getCount();
      } else {
        // Chỉ tìm kiếm theo tên
        where.name = Like(`%${searchTerm}%`);

        // Đếm tổng số tag
        total = await this.userTagRepository.count({ where });

        // Lấy danh sách tag với phân trang và sắp xếp
        tags = await this.userTagRepository.find({
          where,
          order: { [sortBy]: sortDirection },
          skip: offset,
          take: limit,
        });
      }
    } else {
      // Không có điều kiện tìm kiếm
      total = await this.userTagRepository.count({ where });

      tags = await this.userTagRepository.find({
        where,
        order: { [sortBy]: sortDirection },
        skip: offset,
        take: limit,
      });
    }

    // Chuyển đổi kết quả thành DTO
    const items = tags.map(tag => this.mapToDto(tag));

    // Tạo thông tin phân trang
    const meta: PaginationMeta = {
      totalItems: total,
      itemCount: items.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    };

    return {
      items,
      meta,
    };
  }

  /**
   * Lấy thông tin tag theo ID
   * @param userId ID của người dùng
   * @param id ID của tag
   * @returns Thông tin tag
   */
  async findOne(userId: number, id: number): Promise<TagResponseDto> {
    const tag = await this.userTagRepository.findOne({ where: { id, userId } });
    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    return this.mapToDto(tag);
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param tag Entity tag
   * @returns DTO tag
   */
  private mapToDto(tag: UserTag): TagResponseDto {
    const dto = new TagResponseDto();
    dto.id = tag.id;
    dto.name = tag.name;
    dto.color = tag.color;
    dto.createdAt = tag.createdAt;
    dto.updatedAt = tag.updatedAt;
    return dto;
  }
}
