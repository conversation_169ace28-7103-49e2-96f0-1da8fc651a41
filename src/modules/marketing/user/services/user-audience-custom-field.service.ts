import { Injectable, Logger } from '@nestjs/common';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserAudienceCustomFieldDefinitionRepository } from '../repositories/user-audience-custom-field-definition.repository';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';
import { CreateCustomFieldDto, CustomFieldResponseDto } from '../dto/audience';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * Service xử lý logic nghiệp vụ cho giá trị trường tùy chỉnh của audience
 */
@Injectable()
export class UserAudienceCustomFieldService {
  private readonly logger = new Logger(UserAudienceCustomFieldService.name);

  constructor(
    private readonly customFieldRepository: UserAudienceCustomFieldRepository,
    private readonly customFieldDefinitionRepository: UserAudienceCustomFieldDefinitionRepository,
  ) {}

  /**
   * Tạo mới giá trị trường tùy chỉnh cho audience
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(
    userId: number,
    audienceId: number,
    createDto: CreateCustomFieldDto,
  ): Promise<CustomFieldResponseDto> {
    try {
      // Kiểm tra field definition có tồn tại không
      const fieldDefinition = await this.customFieldDefinitionRepository.findOne({
        where: { id: createDto.fieldId, userId },
      });

      if (!fieldDefinition) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy định nghĩa trường tùy chỉnh với ID ${createDto.fieldId}`,
        );
      }

      // Validate giá trị theo kiểu dữ liệu
      this.validateFieldValue(createDto.fieldValue, fieldDefinition.dataType, fieldDefinition.config);

      // Kiểm tra trường đã tồn tại cho audience này chưa
      const existingField = await this.customFieldRepository.findOne({
        where: { audienceId, fieldId: createDto.fieldId },
      });

      if (existingField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
          `Trường tùy chỉnh đã tồn tại cho audience này`,
        );
      }

      // Tạo mới giá trị trường tùy chỉnh
      const now = Math.floor(Date.now() / 1000);
      const customField = new UserAudienceCustomField();
      customField.audienceId = audienceId;
      customField.fieldId = createDto.fieldId;
      customField.fieldValue = createDto.fieldValue;
      customField.createdAt = now;
      customField.updatedAt = now;

      const savedCustomField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating custom field value: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        `Tạo giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật giá trị trường tùy chỉnh
   * @param userId ID của người dùng
   * @param id ID của custom field value
   * @param fieldValue Giá trị mới
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(
    userId: number,
    id: number,
    fieldValue: any,
  ): Promise<CustomFieldResponseDto> {
    try {
      // Tìm kiếm custom field value
      const customField = await this.customFieldRepository.findOne({
        where: { id },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy giá trị trường tùy chỉnh với ID ${id}`,
        );
      }

      // Kiểm tra field definition
      const fieldDefinition = await this.customFieldDefinitionRepository.findOne({
        where: { id: customField.fieldId, userId },
      });

      if (!fieldDefinition) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy định nghĩa trường tùy chỉnh`,
        );
      }

      // Validate giá trị theo kiểu dữ liệu
      this.validateFieldValue(fieldValue, fieldDefinition.dataType, fieldDefinition.config);

      // Cập nhật giá trị
      customField.fieldValue = fieldValue;
      customField.updatedAt = Math.floor(Date.now() / 1000);

      const updatedCustomField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(updatedCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating custom field value: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        `Cập nhật giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa giá trị trường tùy chỉnh
   * @param userId ID của người dùng
   * @param id ID của custom field value
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Transactional()
  async delete(userId: number, id: number): Promise<CustomFieldResponseDto> {
    try {
      // Tìm kiếm custom field value
      const customField = await this.customFieldRepository.findOne({
        where: { id },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy giá trị trường tùy chỉnh với ID ${id}`,
        );
      }

      // Kiểm tra quyền sở hữu thông qua field definition
      const fieldDefinition = await this.customFieldDefinitionRepository.findOne({
        where: { id: customField.fieldId, userId },
      });

      if (!fieldDefinition) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không có quyền xóa trường tùy chỉnh này`,
        );
      }

      // Xóa custom field value
      await this.customFieldRepository.delete(id);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(customField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting custom field value: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
        `Xóa giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách giá trị trường tùy chỉnh của audience
   * @param audienceId ID của audience
   * @returns Danh sách giá trị trường tùy chỉnh
   */
  async findByAudienceId(audienceId: number): Promise<CustomFieldResponseDto[]> {
    try {
      const customFields = await this.customFieldRepository.find({
        where: { audienceId },
      });

      return customFields.map(field => this.mapToResponseDto(field));
    } catch (error) {
      this.logger.error(`Error finding custom fields by audience ID: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_QUERY_FAILED,
        `Lấy danh sách trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Validate giá trị theo kiểu dữ liệu
   */
  private validateFieldValue(value: any, dataType: string, config: Record<string, any>): void {
    if (value === null || value === undefined) {
      if (config?.required) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
          'Trường này là bắt buộc',
        );
      }
      return;
    }

    switch (dataType) {
      case CustomFieldDataType.TEXT:
        if (typeof value !== 'string') {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'Giá trị phải là chuỗi',
          );
        }
        if (config?.maxLength && value.length > config.maxLength) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            `Độ dài không được vượt quá ${config.maxLength} ký tự`,
          );
        }
        if (config?.minLength && value.length < config.minLength) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            `Độ dài phải ít nhất ${config.minLength} ký tự`,
          );
        }
        if (config?.pattern && !new RegExp(config.pattern).test(value)) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'Giá trị không đúng định dạng',
          );
        }
        break;

      case CustomFieldDataType.NUMBER:
        if (!Number.isInteger(Number(value))) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'Giá trị phải là số nguyên',
          );
        }
        const numValue = Number(value);
        if (config?.min !== undefined && numValue < config.min) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            `Giá trị phải lớn hơn hoặc bằng ${config.min}`,
          );
        }
        if (config?.max !== undefined && numValue > config.max) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            `Giá trị phải nhỏ hơn hoặc bằng ${config.max}`,
          );
        }
        break;

      case CustomFieldDataType.DATE:
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'Giá trị phải là ngày hợp lệ',
          );
        }
        break;

      case CustomFieldDataType.BOOLEAN:
        if (typeof value !== 'boolean' && value !== 'true' && value !== 'false' && value !== 0 && value !== 1) {
          throw new AppException(
            MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'Giá trị phải là boolean',
          );
        }
        break;

      case CustomFieldDataType.OBJECT:
        try {
          if (typeof value === 'string') {
            JSON.parse(value);
          }
        } catch {
          if (typeof value !== 'object') {
            throw new AppException(
              MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
              'Giá trị phải là JSON hợp lệ',
            );
          }
        }
        break;
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   */
  private mapToResponseDto(customField: UserAudienceCustomField): CustomFieldResponseDto {
    return {
      id: customField.id,
      audienceId: customField.audienceId,
      fieldId: customField.fieldId,
      fieldValue: customField.fieldValue,
      createdAt: customField.createdAt,
      updatedAt: customField.updatedAt,
    };
  }
}
