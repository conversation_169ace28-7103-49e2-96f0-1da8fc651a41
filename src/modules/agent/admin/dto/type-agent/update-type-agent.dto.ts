import { TypeAgentStatus } from '@modules/agent/constants';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';

/**
 * DTO cho cập nhật cấu hình loại agent
 */
export class UpdateTypeAgentConfigDto implements Partial<TypeAgentConfig> {
  /**
     * <PERSON><PERSON> hồ sơ không
     */
  @ApiProperty({
    description: '<PERSON><PERSON> hồ sơ không',
    example: true,
  })
  @IsBoolean()
  enableAgentProfileCustomization: boolean;

  /**
   * Có đầu ra không
   */
  @ApiProperty({
    description: 'Có đầu ra qua Messenger không',
    example: true,
  })
  @IsBoolean()
  enableOutputToMessenger: boolean;

  @ApiProperty({
    description: '<PERSON><PERSON> đầu ra qua Live Chat trên website không',
    example: true,
  })
  @IsBoolean()
  enableOutputToWebsiteLiveChat: boolean;

  /**
   * Có chuyển đổi không
   */
  @ApiProperty({
    description: 'Có chuyển đổi không',
    example: false,
  })
  @IsBoolean()
  enableTaskConversionTracking: boolean;

  /**
   * Có tài nguyên không
   */
  @ApiProperty({
    description: 'Có tài nguyên không',
    example: true,
  })
  @IsBoolean()
  enableResourceUsage: boolean;

  /**
   * Có chiến lược không
   */
  @ApiProperty({
    description: 'Có chiến lược không',
    example: true,
  })
  @IsBoolean()
  enableDynamicStrategyExecution: boolean;

  /**
   * Có đa agent không
   */
  @ApiProperty({
    description: 'Có đa agent không',
    example: true,
  })
  @IsBoolean()
  enableMultiAgentCollaboration: boolean;
}

/**
 * DTO cho việc cập nhật loại agent
 */
export class UpdateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiPropertyOptional({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiPropertyOptional({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      enableAgentProfileCustomization: true,
      enableOutputToMessenger: true,
      enableOutputToWebsiteLiveChat: true,
      enableTaskConversionTracking: false,
      enableResourceUsage: true,
      enableDynamicStrategyExecution: true,
      enableMultiAgentCollaboration: false,
    },
  })
  @ValidateNested()
  @Type(() => UpdateTypeAgentConfigDto)
  @IsOptional()
  defaultConfig?: UpdateTypeAgentConfigDto;

  /**
   * Trạng thái của loại agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
  })
  @IsEnum(TypeAgentStatus)
  @IsOptional()
  status?: TypeAgentStatus;

  /**
   * Danh sách ID của các agent system
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các agent system',
    example: ['agent-system-uuid-1', 'agent-system-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  agentSystems?: string[];
}
