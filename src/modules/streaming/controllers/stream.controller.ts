import {
  BadRe<PERSON>Ex<PERSON>,
  <PERSON>,
  Get,
  Lo<PERSON>,
  Param,
  Req,
  Res,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';

/**
 * Stream Controller for Backend SSE
 * 
 * Provides Server-Sent Events (SSE) endpoint for streaming real-time events
 * from Redis Streams to frontend clients. Supports multi-device viewing
 * with complete stream replay.
 */
@ApiTags('Streaming')
@Controller('stream')
export class StreamController {
  private readonly logger = new Logger(StreamController.name);

  constructor() {}

  /**
   * SSE endpoint for streaming events to frontend clients
   * @param req Express request object
   * @param res Express response object  
   * @param threadId Thread ID for the stream
   */
  @Get('events/:threadId')
  @ApiOperation({
    summary: 'Stream events via Server-Sent Events',
    description: 'Establishes SSE connection to stream real-time events for a specific thread. Supports multi-device viewing with complete stream replay.',
  })
  @ApiParam({
    name: 'threadId',
    description: 'Thread ID to stream events for',
    example: 'thread_123456',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      'Connection': { description: 'keep-alive' },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid threadId parameter',
  })
  async streamEvents(
    @Req() req: Request,
    @Res() res: Response,
    @Param('threadId') threadId: string,
  ): Promise<void> {
    // Validate threadId parameter
    if (!threadId || threadId.trim() === '') {
      throw new BadRequestException('threadId is required and cannot be empty');
    }

    this.logger.log(`🔥 Starting SSE stream for thread ${threadId}`);

    try {
      // Set SSE headers for streaming
      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      });

      // Send initial connection confirmation
      res.write(`event: connected\n`);
      res.write(`data: {"threadId":"${threadId}","timestamp":${Date.now()},"status":"connected"}\n\n`);

      this.logger.log(`✅ SSE headers set for thread ${threadId}`);

      // TODO: Implement Redis Streams consumer (Subtask 21.2)
      // For now, send a test message every 5 seconds
      const testInterval = setInterval(() => {
        if (res.destroyed) {
          clearInterval(testInterval);
          return;
        }

        const testMessage = {
          event: 'test_message',
          data: {
            threadId,
            message: 'SSE connection active',
            timestamp: Date.now(),
          },
        };

        res.write(`id: test_${Date.now()}\n`);
        res.write(`data: ${JSON.stringify(testMessage)}\n\n`);
        
        this.logger.debug(`📡 Sent test message for thread ${threadId}`);
      }, 5000);

      // Handle client disconnect
      req.on('close', () => {
        clearInterval(testInterval);
        this.logger.log(`🔌 Client disconnected from thread ${threadId}`);
        // TODO: Cleanup Redis consumer group (Subtask 21.4)
      });

      req.on('error', (error) => {
        clearInterval(testInterval);
        this.logger.error(`❌ SSE error for thread ${threadId}:`, error);
        if (!res.destroyed) {
          res.end();
        }
      });

    } catch (error) {
      this.logger.error(`💥 Failed to establish SSE for thread ${threadId}:`, error);
      if (!res.headersSent) {
        throw error;
      }
    }
  }

  /**
   * Health check endpoint for streaming service
   */
  @Get('health')
  @ApiOperation({
    summary: 'Health check for streaming service',
    description: 'Returns the health status of the streaming service',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
  })
  getHealth(): { status: string; timestamp: number } {
    return {
      status: 'healthy',
      timestamp: Date.now(),
    };
  }
}
