import { Module } from '@nestjs/common';
import { StreamController } from './controllers/stream.controller';

/**
 * Streaming Module
 * 
 * Provides Server-Sent Events (SSE) functionality for real-time streaming
 * of events from Redis Streams to frontend clients.
 * 
 * Features:
 * - SSE endpoint for real-time streaming
 * - Redis Streams consumer integration
 * - Multi-device support with unique consumer groups
 * - Connection management and cleanup
 */
@Module({
  controllers: [StreamController],
  providers: [],
  exports: [],
})
export class StreamingModule {}
