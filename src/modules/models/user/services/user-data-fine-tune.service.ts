import { AppException } from '@/common';
import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType, TimeIntervalEnum } from '@/shared/utils';
import { FileJsonlType } from '@/shared/utils/file/file-jsonl-type.util';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { IsNull } from 'typeorm';
import { DataFineTuneStatus } from '../../constants/data-fine-tune-status.enum';
import { UserDataFineTune } from '../../entities/user-data-fine-tune.entity';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { UserDataFineTuneRepository } from '../../repositories/user-data-fine-tune.repository';
import { CreateUserDataFineTuneDto } from '../dto/user-data-fine-tune/create-user-data-fine-tune.dto';
import { UpdateUserDataFineTuneDto } from '../dto/user-data-fine-tune/update-user-data-fine-tune.dto';
import { UserDataFineTuneQueryDto } from '../dto/user-data-fine-tune/user-data-fine-tune-query.dto';
import { UserDataFineTuneResponseDto } from '../dto/user-data-fine-tune/user-data-fine-tune-response.dto';
import { UserDataFineTuneMapper } from '../mappers/user-data-fine-tune.mapper';

/**
 * Service xử lý business logic cho User Data Fine Tune
 */
@Injectable()
export class UserDataFineTuneService {
  private readonly logger = new Logger(UserDataFineTuneService.name);

  constructor(
    private readonly userDataFineTuneRepository: UserDataFineTuneRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Tạo mới dataset fine tune
   */
  async create(userId: number, createDto: CreateUserDataFineTuneDto): Promise<ApiResponseDto<{ id: string, trainUploadUrl: string, validUploadUrl: string | null }>> {
    this.logger.log(`Creating data fine tune for user ${userId}`);

    // Kiểm tra trùng tên
    const existsByName = await this.userDataFineTuneRepository.existsByName(createDto.name, userId);
    if (existsByName) {
      throw new AppException(MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NAME_EXISTS);
    }

    const trainDatasetKey = generateS3Key({
      baseFolder: userId.toString(),
      categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
      fileName: `train-${userId}.jsonl`,
      useTimeFolder: true,
    });

    const trainUploadUrl = await this.s3Service.createPresignedWithID(
      trainDatasetKey,
      TimeIntervalEnum.ONE_HOUR,
      FileJsonlType.getMimeType(createDto.trainDataset),
      FileSizeEnum.TEN_MB,
    );

    let validDatasetKey: string | null = null;
    let validUploadUrl: string | null = null;
    if (createDto.validDataset) {
      validDatasetKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
        fileName: `valid-${userId}.jsonl`,
        useTimeFolder: true,
      });

      validUploadUrl = await this.s3Service.createPresignedWithID(
        validDatasetKey,
        TimeIntervalEnum.ONE_HOUR,
        FileJsonlType.getMimeType(createDto.validDataset),
        FileSizeEnum.TEN_MB,
      );
    }

    // Tạo entity mới
    const newDataset = this.userDataFineTuneRepository.create({
      name: createDto.name,
      description: createDto.description,
      trainDataset: trainDatasetKey,
      validDataset: validDatasetKey,
      userId,
      provider: createDto.provider,
      status: DataFineTuneStatus.PENDING
    });

    // Lưu vào database
    const savedDataset = await this.userDataFineTuneRepository.save(newDataset);

    // TODO: Implement create logic
    return ApiResponseDto.success({ id: savedDataset.id, trainUploadUrl, validUploadUrl });
  }

  /**
   * Lấy danh sách dataset fine tune của user có phân trang
   */
  async findAll(userId: number, queryDto: UserDataFineTuneQueryDto): Promise<ApiResponseDto<PaginatedResult<UserDataFineTuneResponseDto>>> {
    this.logger.log(`Getting data fine tune list for user ${userId}`);

    const result = await this.userDataFineTuneRepository.findWithPagination(queryDto, userId);

    // Chuyển đổi entities sang DTOs
    const items = UserDataFineTuneMapper.toResponseDtoList(result.items, this.cdnService);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Lấy chi tiết dataset fine tune
   */
  async findOne(userId: number, id: string): Promise<ApiResponseDto<any>> {
    this.logger.log(`Getting data fine tune detail ${id} for user ${userId}`);
    // TODO: Implement findOne logic
    return ApiResponseDto.success({});
  }

  /**
   * Cập nhật dataset fine tune
   */
  async update(userId: number, id: string, updateDto: UpdateUserDataFineTuneDto): Promise<ApiResponseDto<{ id: string, trainUploadUrl: string | null, validUploadUrl: string | null }>> {
    this.logger.log(`Updating data fine tune ${id} for user ${userId}`);

    // Tìm dataset để đảm bảo ownership
    const existingDataset = await this.userDataFineTuneRepository.findByIdAndUserId(id, userId);
    if (!existingDataset) {
      throw new AppException(MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
    }

    // Kiểm tra tên trùng lặp nếu có thay đổi tên
    if (updateDto.name && updateDto.name !== existingDataset.name) {
      const nameExists = await this.userDataFineTuneRepository.existsByName(updateDto.name, userId, id);
      if (nameExists) {
        throw new AppException(MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NAME_EXISTS);
      }
    }

    let trainUploadUrl: string | null = null;
    let validUploadUrl: string | null = null;
    let trainDatasetKey = existingDataset.trainDataset;
    let validDatasetKey = existingDataset.validDataset;

    // Tạo presigned URL mới cho train dataset nếu có thay đổi
    if (updateDto.trainDataset) {
      trainDatasetKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
        fileName: `train-${userId}-${Date.now()}.jsonl`,
        useTimeFolder: true,
      });

      trainUploadUrl = await this.s3Service.createPresignedWithID(
        trainDatasetKey,
        TimeIntervalEnum.ONE_HOUR,
        FileJsonlType.getMimeType(updateDto.trainDataset),
        FileSizeEnum.TEN_MB,
      );
    }

    // Tạo presigned URL mới cho valid dataset nếu có thay đổi
    if (updateDto.validDataset) {
      validDatasetKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
        fileName: `valid-${userId}-${Date.now()}.jsonl`,
        useTimeFolder: true,
      });

      validUploadUrl = await this.s3Service.createPresignedWithID(
        validDatasetKey,
        TimeIntervalEnum.ONE_HOUR,
        FileJsonlType.getMimeType(updateDto.validDataset),
        FileSizeEnum.TEN_MB,
      );
    }

    // Cập nhật dataset
    const updateData: Partial<UserDataFineTune> = {
      updatedAt: Date.now()
    };

    if (updateDto.name) updateData.name = updateDto.name;
    if (updateDto.description !== undefined) updateData.description = updateDto.description;
    if (updateDto.trainDataset) {
      updateData.trainDataset = trainDatasetKey;
      updateData.status = DataFineTuneStatus.PENDING; // Reset status khi có file mới
    }
    if (updateDto.validDataset) {
      updateData.validDataset = validDatasetKey;
      updateData.status = DataFineTuneStatus.PENDING; // Reset status khi có file mới
    }

    await this.userDataFineTuneRepository.update({ id, userId }, updateData);

    this.logger.log(`Dataset ${id} updated successfully for user ${userId}`);

    return ApiResponseDto.success({
      id,
      trainUploadUrl,
      validUploadUrl
    });
  }

  /**
   * Xóa dataset fine tune
   */
  async remove(userId: number, id: string): Promise<ApiResponseDto<{ message: string }>> {
    this.logger.log(`Removing data fine tune ${id} for user ${userId}`);

    // Kiểm tra dataset tồn tại và ownership
    const existingDataset = await this.userDataFineTuneRepository.findByIdAndUserId(id, userId);
    if (!existingDataset) {
      throw new AppException(MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
    }

    // Thực hiện soft delete
    const deleted = await this.userDataFineTuneRepository.softDeleteByIdAndUserId(id, userId);
    if (!deleted) {
      throw new AppException(MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
    }

    return ApiResponseDto.success({ message: 'Xóa dataset fine tune thành công' });
  }


  /**
   * Cập nhật trạng thái upload dataset
   */
  async updateStatus(userId: number, id: string): Promise<ApiResponseDto<{ id: string }>> {
    this.logger.log(`Updating dataset status ${id} for user`);

    // Tìm dataset theo ID và userId để đảm bảo ownership
    const dataset = await this.userDataFineTuneRepository.findOne({
      where: {
        id,
        userId,
        deletedAt: IsNull()
      }
    });

    if (!dataset) {
      throw new AppException(MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
    }

    dataset.status = DataFineTuneStatus.APPROVED;

    // Kiểm tra file tồn tại trên S3 khi status = true
    const trainExists = await this.s3Service.checkObjectExists(dataset.trainDataset);
    if (!trainExists) {
      dataset.status = DataFineTuneStatus.ERROR;
    }

    // Kiểm tra valid dataset nếu có
    if (dataset.validDataset) {
      const validExists = await this.s3Service.checkObjectExists(dataset.validDataset);
      if (!validExists) {
        dataset.status = DataFineTuneStatus.ERROR;
      }
    }

    // Cập nhật trạng thái
    await this.userDataFineTuneRepository.update(
      { id, userId },
      {
        status: dataset.status,
        updatedAt: Date.now()
      }
    );

    this.logger.log(`Dataset ${id} status updated to for user ${userId}`);

    return ApiResponseDto.success({ id });
  }

  async urlUpload(userId: number, mime: string): Promise<ApiResponseDto<{ uploadUrl: string, viewUrl: string | null }>> {

    const key = generateS3Key({
      baseFolder: userId.toString(),
      categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
      useTimeFolder: true,
    });

    const uploadUrl = await this.s3Service.createPresignedWithID(
      key,
      TimeIntervalEnum.ONE_HOUR,
      ImageType.getType(mime),
      FileSizeEnum.TEN_MB,
    );

    const viewUrl = this.cdnService.generateUrlView(key, TimeIntervalEnum.ONE_HOUR);

    return ApiResponseDto.success({uploadUrl, viewUrl });
  }
}
