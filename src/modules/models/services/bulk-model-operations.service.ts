import { Injectable, Logger } from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { SystemModelsRepository } from '../repositories/system-models.repository';
import { UserModelsRepository } from '../repositories/user-models.repository';
import { SystemModelKeyLlmRepository } from '../repositories/system-model-key-llm.repository';
import { UserModelKeyLlmRepository } from '../repositories/user-model-key-llm.repository';
import { SystemModels } from '../entities/system-models.entity';
import { UserModels } from '../entities/user-models.entity';
import { SystemModelKeyLlm } from '../entities/system-model-key-llm.entity';
import { UserModelKeyLlm } from '../entities/user-model-key-llm.entity';

/**
 * Interface cho bulk model data
 */
export interface BulkModelData {
  modelId: string;
  modelRegistryId: string;
  metadata?: Record<string, any>;
}

/**
 * Interface cho bulk operation result
 */
export interface BulkOperationResult {
  totalProcessed: number;
  newModelsCreated: number;
  existingModelsFound: number;
  mappingsCreated: number;
  errors: string[];
  modelIdMap: Map<string, string>; // modelId -> internal ID
  operationTime: number;
}

/**
 * Interface cho bulk mapping data
 */
export interface BulkMappingData {
  modelId: string;
  llmKeyId: string;
}

/**
 * Bulk Model Operations Service
 * Optimizes database operations từ O(N) individual queries xuống O(1) bulk operations
 */
@Injectable()
export class BulkModelOperationsService {
  private readonly logger = new Logger(BulkModelOperationsService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly userModelsRepository: UserModelsRepository,
    private readonly systemModelKeyLlmRepository: SystemModelKeyLlmRepository,
    private readonly userModelKeyLlmRepository: UserModelKeyLlmRepository,
  ) {}

  /**
   * Bulk upsert system models với single transaction
   * @param modelsData Danh sách model data
   * @param keyId System key ID cho mappings
   * @returns Bulk operation result
   */
  @Transactional()
  async bulkUpsertSystemModels(
    modelsData: BulkModelData[],
    keyId: string
  ): Promise<BulkOperationResult> {
    const startTime = Date.now();
    const result: BulkOperationResult = {
      totalProcessed: modelsData.length,
      newModelsCreated: 0,
      existingModelsFound: 0,
      mappingsCreated: 0,
      errors: [],
      modelIdMap: new Map(),
      operationTime: 0
    };

    if (modelsData.length === 0) {
      result.operationTime = Date.now() - startTime;
      return result;
    }

    try {
      this.logger.log(`Starting bulk upsert for ${modelsData.length} system models`);

      // Step 1: Bulk check existing models - O(1) query
      const modelIds = modelsData.map(data => data.modelId);
      const existingModels = await this.systemModelsRepository
        .createQueryBuilder('sm')
        .select(['sm.id', 'sm.modelId'])
        .where('sm.modelId IN (:...modelIds)', { modelIds })
        .getMany();

      // Create lookup map for existing models
      const existingModelMap = new Map<string, string>();
      existingModels.forEach(model => {
        existingModelMap.set(model.modelId, model.id);
        result.modelIdMap.set(model.modelId, model.id);
      });

      result.existingModelsFound = existingModels.length;

      // Step 2: Prepare new models for bulk insert
      const newModelsData = modelsData.filter(data => !existingModelMap.has(data.modelId));
      
      if (newModelsData.length > 0) {
        // Bulk insert new models - O(1) operation
        const newModels = newModelsData.map(data => 
          this.systemModelsRepository.create({
            modelId: data.modelId,
            modelRegistryId: data.modelRegistryId,
            active: true,
            ...data.metadata
          })
        );

        const savedModels = await this.systemModelsRepository.save(newModels);
        result.newModelsCreated = savedModels.length;

        // Update model ID map with new models
        savedModels.forEach(model => {
          result.modelIdMap.set(model.modelId, model.id);
        });

        this.logger.log(`Bulk inserted ${savedModels.length} new system models`);
      }

      // Step 3: Bulk create mappings
      const mappingResult = await this.bulkCreateSystemModelKeyMappings(
        Array.from(result.modelIdMap.values()),
        keyId
      );
      result.mappingsCreated = mappingResult.created;
      result.errors.push(...mappingResult.errors);

      result.operationTime = Date.now() - startTime;
      this.logger.log(`Bulk system model upsert completed in ${result.operationTime}ms`);

      return result;

    } catch (error) {
      result.errors.push(`Bulk system model upsert failed: ${error.message}`);
      result.operationTime = Date.now() - startTime;
      this.logger.error(`Bulk system model upsert failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk upsert user models với single transaction
   * @param modelsData Danh sách model data
   * @param keyId User key ID cho mappings
   * @returns Bulk operation result
   */
  @Transactional()
  async bulkUpsertUserModels(
    modelsData: BulkModelData[],
    keyId: string
  ): Promise<BulkOperationResult> {
    const startTime = Date.now();
    const result: BulkOperationResult = {
      totalProcessed: modelsData.length,
      newModelsCreated: 0,
      existingModelsFound: 0,
      mappingsCreated: 0,
      errors: [],
      modelIdMap: new Map(),
      operationTime: 0
    };

    if (modelsData.length === 0) {
      result.operationTime = Date.now() - startTime;
      return result;
    }

    try {
      this.logger.log(`Starting bulk upsert for ${modelsData.length} user models`);

      // Step 1: Bulk check existing models - O(1) query
      const modelIds = modelsData.map(data => data.modelId);
      const existingModels = await this.userModelsRepository
        .createQueryBuilder('um')
        .select(['um.id', 'um.modelId'])
        .where('um.modelId IN (:...modelIds)', { modelIds })
        .getMany();

      // Create lookup map for existing models
      const existingModelMap = new Map<string, string>();
      existingModels.forEach(model => {
        existingModelMap.set(model.modelId, model.id);
        result.modelIdMap.set(model.modelId, model.id);
      });

      result.existingModelsFound = existingModels.length;

      // Step 2: Prepare new models for bulk insert
      const newModelsData = modelsData.filter(data => !existingModelMap.has(data.modelId));
      
      if (newModelsData.length > 0) {
        // Bulk insert new models - O(1) operation
        const newModels = newModelsData.map(data => 
          this.userModelsRepository.create({
            modelId: data.modelId,
            modelRegistryId: data.modelRegistryId,
            ...data.metadata
          })
        );

        const savedModels = await this.userModelsRepository.save(newModels);
        result.newModelsCreated = savedModels.length;

        // Update model ID map with new models
        savedModels.forEach(model => {
          result.modelIdMap.set(model.modelId, model.id);
        });

        this.logger.log(`Bulk inserted ${savedModels.length} new user models`);
      }

      // Step 3: Bulk create mappings
      const mappingResult = await this.bulkCreateUserModelKeyMappings(
        Array.from(result.modelIdMap.values()),
        keyId
      );
      result.mappingsCreated = mappingResult.created;
      result.errors.push(...mappingResult.errors);

      result.operationTime = Date.now() - startTime;
      this.logger.log(`Bulk user model upsert completed in ${result.operationTime}ms`);

      return result;

    } catch (error) {
      result.errors.push(`Bulk user model upsert failed: ${error.message}`);
      result.operationTime = Date.now() - startTime;
      this.logger.error(`Bulk user model upsert failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk create system model key mappings
   * @param modelIds Danh sách model IDs
   * @param keyId System key ID
   * @returns Mapping creation result
   */
  private async bulkCreateSystemModelKeyMappings(
    modelIds: string[],
    keyId: string
  ): Promise<{ created: number; errors: string[] }> {
    if (modelIds.length === 0) {
      return { created: 0, errors: [] };
    }

    try {
      // Step 1: Check existing mappings - O(1) query
      const existingMappings = await this.systemModelKeyLlmRepository
        .createQueryBuilder('sml')
        .select(['sml.modelId'])
        .where('sml.modelId IN (:...modelIds)', { modelIds })
        .andWhere('sml.llmKeyId = :keyId', { keyId })
        .getMany();

      const existingModelIds = new Set(existingMappings.map(m => m.modelId));

      // Step 2: Prepare new mappings
      const newMappings = modelIds
        .filter(modelId => !existingModelIds.has(modelId))
        .map(modelId => 
          this.systemModelKeyLlmRepository.create({
            modelId,
            llmKeyId: keyId
          })
        );

      // Step 3: Bulk insert mappings - O(1) operation
      if (newMappings.length > 0) {
        await this.systemModelKeyLlmRepository.save(newMappings);
        this.logger.log(`Bulk created ${newMappings.length} system model key mappings`);
      }

      return { created: newMappings.length, errors: [] };

    } catch (error) {
      const errorMsg = `Failed to bulk create system model key mappings: ${error.message}`;
      this.logger.error(errorMsg, error.stack);
      return { created: 0, errors: [errorMsg] };
    }
  }

  /**
   * Bulk create user model key mappings
   * @param modelIds Danh sách model IDs
   * @param keyId User key ID
   * @returns Mapping creation result
   */
  private async bulkCreateUserModelKeyMappings(
    modelIds: string[],
    keyId: string
  ): Promise<{ created: number; errors: string[] }> {
    if (modelIds.length === 0) {
      return { created: 0, errors: [] };
    }

    try {
      // Step 1: Check existing mappings - O(1) query
      const existingMappings = await this.userModelKeyLlmRepository
        .createQueryBuilder('uml')
        .select(['uml.modelId'])
        .where('uml.modelId IN (:...modelIds)', { modelIds })
        .andWhere('uml.llmKeyId = :keyId', { keyId })
        .getMany();

      const existingModelIds = new Set(existingMappings.map(m => m.modelId));

      // Step 2: Prepare new mappings
      const newMappings = modelIds
        .filter(modelId => !existingModelIds.has(modelId))
        .map(modelId => 
          this.userModelKeyLlmRepository.create({
            modelId,
            llmKeyId: keyId
          })
        );

      // Step 3: Bulk insert mappings - O(1) operation
      if (newMappings.length > 0) {
        await this.userModelKeyLlmRepository.save(newMappings);
        this.logger.log(`Bulk created ${newMappings.length} user model key mappings`);
      }

      return { created: newMappings.length, errors: [] };

    } catch (error) {
      const errorMsg = `Failed to bulk create user model key mappings: ${error.message}`;
      this.logger.error(errorMsg, error.stack);
      return { created: 0, errors: [errorMsg] };
    }
  }

  /**
   * Bulk delete model mappings by key ID
   * @param keyId Key ID
   * @param isSystemKey Whether it's system key or user key
   * @returns Number of deleted mappings
   */
  @Transactional()
  async bulkDeleteModelMappings(keyId: string, isSystemKey: boolean = true): Promise<number> {
    try {
      const repository = isSystemKey 
        ? this.systemModelKeyLlmRepository 
        : this.userModelKeyLlmRepository;

      const result = await repository
        .createQueryBuilder()
        .delete()
        .where('llmKeyId = :keyId', { keyId })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.log(`Bulk deleted ${deletedCount} ${isSystemKey ? 'system' : 'user'} model mappings for key ${keyId}`);
      
      return deletedCount;

    } catch (error) {
      this.logger.error(`Failed to bulk delete model mappings: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get bulk operation statistics
   * @returns Statistics object
   */
  async getBulkOperationStats(): Promise<{
    systemModels: number;
    userModels: number;
    systemMappings: number;
    userMappings: number;
  }> {
    try {
      const [systemModels, userModels, systemMappings, userMappings] = await Promise.all([
        this.systemModelsRepository.count(),
        this.userModelsRepository.count(),
        this.systemModelKeyLlmRepository.count(),
        this.userModelKeyLlmRepository.count()
      ]);

      return {
        systemModels,
        userModels,
        systemMappings,
        userMappings
      };

    } catch (error) {
      this.logger.error(`Failed to get bulk operation stats: ${error.message}`, error.stack);
      throw error;
    }
  }
}
