-- Tạ<PERSON> bảng whatsapp_accounts
CREATE TABLE whatsapp_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    phone_number_id VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    business_account_id VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    access_token TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    agent_id UUID REFERENCES agents(id),
    created_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    updated_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)
);

-- Tạo bảng whatsapp_templates
CREATE TABLE whatsapp_templates (
    id SERIAL PRIMARY KEY,
    whatsapp_account_id INTEGER NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    template_id VARCHAR(255) NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    language VARCHAR(10) NOT NULL,
    category VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    components JSONB NOT NULL,
    created_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    updated_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)
);

-- Tạo bảng whatsapp_messages
CREATE TABLE whatsapp_messages (
    id SERIAL PRIMARY KEY,
    whatsapp_account_id INTEGER NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    message_id VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    contact_name VARCHAR(255),
    content TEXT,
    message_type VARCHAR(20) NOT NULL,
    metadata JSONB DEFAULT '{}',
    direction VARCHAR(10) NOT NULL,
    status VARCHAR(20),
    sent_at BIGINT NOT NULL,
    updated_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)
);

-- Tạo bảng whatsapp_webhook_logs
CREATE TABLE whatsapp_webhook_logs (
    id SERIAL PRIMARY KEY,
    whatsapp_account_id INTEGER REFERENCES whatsapp_accounts(id) ON DELETE SET NULL,
    payload JSONB NOT NULL,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    received_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)
);

-- Tạo các chỉ mục để tối ưu hiệu suất truy vấn
CREATE INDEX idx_whatsapp_accounts_user_id ON whatsapp_accounts(user_id);
CREATE INDEX idx_whatsapp_accounts_agent_id ON whatsapp_accounts(agent_id);
CREATE INDEX idx_whatsapp_accounts_phone_number ON whatsapp_accounts(phone_number);
CREATE INDEX idx_whatsapp_templates_account_id ON whatsapp_templates(whatsapp_account_id);
CREATE INDEX idx_whatsapp_templates_name ON whatsapp_templates(name);
CREATE INDEX idx_whatsapp_messages_account_id ON whatsapp_messages(whatsapp_account_id);
CREATE INDEX idx_whatsapp_messages_phone_number ON whatsapp_messages(phone_number);
CREATE INDEX idx_whatsapp_messages_sent_at ON whatsapp_messages(sent_at);
CREATE INDEX idx_whatsapp_webhook_logs_account_id ON whatsapp_webhook_logs(whatsapp_account_id);
CREATE INDEX idx_whatsapp_webhook_logs_received_at ON whatsapp_webhook_logs(received_at);

-- Thêm các ràng buộc duy nhất
ALTER TABLE whatsapp_accounts ADD CONSTRAINT uk_whatsapp_accounts_phone_number UNIQUE (phone_number);
ALTER TABLE whatsapp_templates ADD CONSTRAINT uk_whatsapp_templates_account_name UNIQUE (whatsapp_account_id, name);
ALTER TABLE whatsapp_messages ADD CONSTRAINT uk_whatsapp_messages_account_message_id UNIQUE (whatsapp_account_id, message_id);

-- Thêm các comment cho bảng và cột
COMMENT ON TABLE whatsapp_accounts IS 'Lưu trữ thông tin về các tài khoản WhatsApp Business được kết nối';
COMMENT ON COLUMN whatsapp_accounts.user_id IS 'ID người dùng sở hữu tài khoản';
COMMENT ON COLUMN whatsapp_accounts.phone_number_id IS 'ID số điện thoại WhatsApp Business';
COMMENT ON COLUMN whatsapp_accounts.phone_number IS 'Số điện thoại WhatsApp Business (định dạng quốc tế)';
COMMENT ON COLUMN whatsapp_accounts.business_account_id IS 'ID tài khoản doanh nghiệp WhatsApp';
COMMENT ON COLUMN whatsapp_accounts.display_name IS 'Tên hiển thị của tài khoản';
COMMENT ON COLUMN whatsapp_accounts.access_token IS 'Access token để gọi API';
COMMENT ON COLUMN whatsapp_accounts.is_active IS 'Trạng thái hoạt động của tài khoản';
COMMENT ON COLUMN whatsapp_accounts.agent_id IS 'ID agent được kết nối với tài khoản WhatsApp';
COMMENT ON COLUMN whatsapp_accounts.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN whatsapp_accounts.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

COMMENT ON TABLE whatsapp_templates IS 'Lưu trữ thông tin về các mẫu tin nhắn WhatsApp đã được phê duyệt';
COMMENT ON COLUMN whatsapp_templates.whatsapp_account_id IS 'ID tài khoản WhatsApp';
COMMENT ON COLUMN whatsapp_templates.template_id IS 'ID mẫu tin nhắn trên WhatsApp';
COMMENT ON COLUMN whatsapp_templates.name IS 'Tên mẫu tin nhắn';
COMMENT ON COLUMN whatsapp_templates.language IS 'Ngôn ngữ của mẫu tin nhắn';
COMMENT ON COLUMN whatsapp_templates.category IS 'Loại mẫu tin nhắn (text, media, interactive)';
COMMENT ON COLUMN whatsapp_templates.status IS 'Trạng thái của mẫu tin nhắn (APPROVED, PENDING, REJECTED)';
COMMENT ON COLUMN whatsapp_templates.components IS 'Nội dung của mẫu tin nhắn (JSON)';
COMMENT ON COLUMN whatsapp_templates.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN whatsapp_templates.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

COMMENT ON TABLE whatsapp_messages IS 'Lưu trữ lịch sử tin nhắn trao đổi qua WhatsApp';
COMMENT ON COLUMN whatsapp_messages.whatsapp_account_id IS 'ID tài khoản WhatsApp';
COMMENT ON COLUMN whatsapp_messages.message_id IS 'ID tin nhắn trên WhatsApp';
COMMENT ON COLUMN whatsapp_messages.phone_number IS 'Số điện thoại người nhận/gửi';
COMMENT ON COLUMN whatsapp_messages.contact_name IS 'Tên người nhận/gửi (nếu có)';
COMMENT ON COLUMN whatsapp_messages.content IS 'Nội dung tin nhắn';
COMMENT ON COLUMN whatsapp_messages.message_type IS 'Loại tin nhắn (text, image, document, template, etc.)';
COMMENT ON COLUMN whatsapp_messages.metadata IS 'Metadata của tin nhắn (JSON)';
COMMENT ON COLUMN whatsapp_messages.direction IS 'Hướng tin nhắn (incoming/outgoing)';
COMMENT ON COLUMN whatsapp_messages.status IS 'Trạng thái gửi tin nhắn (sent, delivered, read, failed)';
COMMENT ON COLUMN whatsapp_messages.sent_at IS 'Thời điểm gửi tin nhắn (Unix timestamp)';
COMMENT ON COLUMN whatsapp_messages.updated_at IS 'Thời điểm cập nhật trạng thái (Unix timestamp)';

COMMENT ON TABLE whatsapp_webhook_logs IS 'Lưu trữ log các webhook từ WhatsApp để debug và theo dõi';
COMMENT ON COLUMN whatsapp_webhook_logs.whatsapp_account_id IS 'ID tài khoản WhatsApp';
COMMENT ON COLUMN whatsapp_webhook_logs.payload IS 'Dữ liệu webhook nhận được (JSON)';
COMMENT ON COLUMN whatsapp_webhook_logs.status IS 'Trạng thái xử lý (success/error)';
COMMENT ON COLUMN whatsapp_webhook_logs.error_message IS 'Thông báo lỗi (nếu có)';
COMMENT ON COLUMN whatsapp_webhook_logs.received_at IS 'Thời điểm nhận webhook (Unix timestamp)';
