/**
 * Configuration cho các nhà cung cấp vận chuyển
 */

/**
 * Base URLs cho các nhà cung cấp
 */
export const PROVIDER_BASE_URLS = {
  // GHN URLs
  GHN_STAGING: 'https://dev-online-gateway.ghn.vn',
  GHN_PRODUCTION: 'https://online-gateway.ghn.vn',
  
  // GHTK URLs  
  GHTK_BASE: 'https://services.giaohangtietkiem.vn',
  
  // Ahamove URLs
  AHAMOVE_STAGING: 'https://partner-apistg.ahamove.com',
  AHAMOVE_PRODUCTION: 'https://partner-api.ahamove.com',
  
  // J&T URLs
  JT_BASE: 'https://api.jet.co.id',
} as const;

/**
 * API endpoints cho test connection
 */
export const PROVIDER_TEST_ENDPOINTS = {
  GHN: {
    GET_SHOPS: '/shiip/public-api/v2/shop/all',
    GET_PROVINCES: '/shiip/public-api/master-data/province',
  },
  
  GHTK: {
    GET_PICKUP_ADDRESSES: '/services/shipment/list_pick_add',
    CALCULATE_FEE: '/services/shipment/fee',
  },
  
  AHAMOVE: {
    AUTHENTICATE: '/v3/accounts/token',
    GET_CITIES: '/v3/cities',
  },
  
  JT: {
    CHECK_TARIFF: '/api/tariff/getPrice',
    CREATE_ORDER: '/api/order/addOrder',
  },
} as const;

/**
 * Timeout configurations (milliseconds)
 */
export const PROVIDER_TIMEOUTS = {
  DEFAULT: 10000,      // 10 seconds
  GHN: 15000,         // 15 seconds
  GHTK: 12000,        // 12 seconds  
  AHAMOVE: 10000,     // 10 seconds
  JT: 20000,          // 20 seconds (J&T có thể chậm hơn)
} as const;

/**
 * Test data cho validation
 */
export const PROVIDER_TEST_DATA = {
  GHN: {
    TEST_SHOP_QUERY: {
      offset: 0,
      limit: 1,
    },
  },
  
  GHTK: {
    // Không cần test data đặc biệt
  },
  
  AHAMOVE: {
    TEST_COUNTRY_ID: 'VN',
  },
  
  JT: {
    TEST_TARIFF_DATA: {
      weight: 1.0,
      sendSiteCode: 'JAKARTA',
      destAreaCode: 'KALIDERES',
      productType: 'EZ',
    },
  },
} as const;

/**
 * Helper function để lấy base URL theo environment
 */
export function getProviderBaseUrl(provider: string, isProduction = false): string {
  switch (provider.toUpperCase()) {
    case 'GHN':
      return isProduction ? PROVIDER_BASE_URLS.GHN_PRODUCTION : PROVIDER_BASE_URLS.GHN_STAGING;
    
    case 'GHTK':
      return PROVIDER_BASE_URLS.GHTK_BASE;
    
    case 'AHAMOVE':
      return isProduction ? PROVIDER_BASE_URLS.AHAMOVE_PRODUCTION : PROVIDER_BASE_URLS.AHAMOVE_STAGING;
    
    case 'JT':
      return PROVIDER_BASE_URLS.JT_BASE;
    
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

/**
 * Helper function để lấy timeout theo provider
 */
export function getProviderTimeout(provider: string): number {
  switch (provider.toUpperCase()) {
    case 'GHN':
      return PROVIDER_TIMEOUTS.GHN;
    case 'GHTK':
      return PROVIDER_TIMEOUTS.GHTK;
    case 'AHAMOVE':
      return PROVIDER_TIMEOUTS.AHAMOVE;
    case 'JT':
      return PROVIDER_TIMEOUTS.JT;
    default:
      return PROVIDER_TIMEOUTS.DEFAULT;
  }
}

/**
 * Environment variables mapping
 */
export const ENV_KEYS = {
  // Environment mode
  NODE_ENV: 'NODE_ENV',
  
  // Provider URLs (optional overrides)
  GHN_BASE_URL: 'GHN_BASE_URL',
  GHTK_BASE_URL: 'GHTK_BASE_URL', 
  AHAMOVE_BASE_URL: 'AHAMOVE_BASE_URL',
  JT_BASE_URL: 'JT_BASE_URL',
  
  // Encryption
  SHIPMENT_SECRET_KEY: 'SHIPMENT_SECRET_KEY',
} as const;
