import { Controller, Post, Body, UseGuards, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiExtraModels } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { IntegrationAudienceService } from '../services/integration-audience.service';
import { CreateIntegrationAudienceDto, IntegrationAudienceResponseDto } from '../dto';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API tạo audience thông qua integration admin
 */
@ApiTags(SWAGGER_API_TAGS.INTEGRATION_ADMIN)
@Controller('admin/integration/audiences')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(AppApiResponse, IntegrationAudienceResponseDto)
export class IntegrationAudienceController {
  constructor(private readonly integrationAudienceService: IntegrationAudienceService) {}

  /**
   * Tạo audience mới thông qua integration admin
   */
  @Post()
  @ApiOperation({ 
    summary: 'Tạo audience mới thông qua integration admin',
    description: 'API này cho phép tạo audience mới với thông tin cơ bản và gán tags. Được sử dụng cho các tích hợp bên ngoài.'
  })
  @ApiResponse({
    status: 201,
    description: 'Audience đã được tạo thành công',
    schema: AppApiResponse.getSchema(IntegrationAudienceResponseDto)
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Dữ liệu đầu vào không hợp lệ' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Không tìm thấy tag được chỉ định' 
  })
  @ApiResponse({ 
    status: 409, 
    description: 'Email đã tồn tại trong hệ thống' 
  })
  async create(
    @Body() createDto: CreateIntegrationAudienceDto,
  ): Promise<AppApiResponse<IntegrationAudienceResponseDto>> {
    // Validate email không trùng lặp
    const isEmailUnique = await this.integrationAudienceService.validateUniqueEmail(createDto.email);
    if (!isEmailUnique) {
      throw new BadRequestException(`Email ${createDto.email} đã tồn tại trong hệ thống`);
    }

    // Tạo audience
    const result = await this.integrationAudienceService.create(createDto);
    
    return wrapResponse(result, result.message);
  }
}
