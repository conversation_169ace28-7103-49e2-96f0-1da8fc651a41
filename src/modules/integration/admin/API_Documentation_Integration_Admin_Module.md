# API Documentation - Integration Admin Module

## Tổng quan

Module Integration Admin cung cấp các API cho phép quản trị viên (admin) quản lý các tích hợp với các dịch vụ bên ngoài trong hệ thống. Module này bao gồm các chức năng quản lý:

- <PERSON><PERSON><PERSON><PERSON> toán (Payment Gateway)
- Cấu hình máy chủ email (SMTP)
- Trang Facebook
- API keys của người dùng
- C<PERSON><PERSON> hình máy chủ SMS

Tất cả các API trong module này đều yêu cầu xác thực JWT và được bảo vệ bởi `JwtEmployeeGuard`, chỉ cho phép nhân viên có quyền admin truy cập.

## Cấu trúc phản hồi chung

Tất cả các API đều trả về cấu trúc phản hồi thống nhất theo định dạng `ApiResponseDto`:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    // Dữ liệu trả về
  }
}
```

Đối với các API trả về danh sách có phân trang, cấu trúc phản hồi sẽ là:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    "items": [
      // Mảng các đối tượng
    ],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

---

## 1. Payment Gateway Admin API

Quản lý các cổng thanh toán trong hệ thống.

### 1.1. Lấy danh sách cổng thanh toán

```
GET /admin/integration/payment-gateway
```

**Mô tả:** Lấy danh sách tất cả các cổng thanh toán trong hệ thống với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| search | String | Không | Tìm kiếm theo tên cổng thanh toán |
| status | String | Không | Lọc theo trạng thái (ACTIVE, INACTIVE) |

**DTO:**

```typescript
// PaymentGatewayQueryDto
{
  page?: number;        // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;       // Số lượng bản ghi trên mỗi trang
  search?: string;      // Tìm kiếm theo tên cổng thanh toán
  status?: string;      // Lọc theo trạng thái
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách cổng thanh toán thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "accountId": "ACC123456",
        "companyId": 1,
        "bankCode": "VCB",
        "accountNumber": "**********",
        "accountHolderName": "Nguyen Van A",
        "label": "Tài khoản chính",
        "status": "ACTIVE",
        "requestId": "REQ123456",
        "isVa": false,
        "canCreateVa": true,
        "createdAt": *************,
        "updatedAt": *************
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 1.2. Lấy thông tin chi tiết cổng thanh toán

```
GET /admin/integration/payment-gateway/:id
```

**Mô tả:** Lấy thông tin chi tiết của một cổng thanh toán cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cổng thanh toán |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết cổng thanh toán thành công",
  "result": {
    "id": 1,
    "accountId": "ACC123456",
    "companyId": 1,
    "bankCode": "VCB",
    "accountNumber": "**********",
    "accountHolderName": "Nguyen Van A",
    "label": "Tài khoản chính",
    "status": "ACTIVE",
    "requestId": "REQ123456",
    "isVa": false,
    "canCreateVa": true,
    "createdAt": *************,
    "updatedAt": *************,
    "company": {
      "id": 1,
      "name": "Công ty TNHH ABC",
      "taxCode": "**********",
      "address": "123 Đường ABC, Quận 1, TP.HCM"
    }
  }
}
```

### 1.3. Cập nhật trạng thái cổng thanh toán

```
PUT /admin/integration/payment-gateway/:id/status
```

**Mô tả:** Cập nhật trạng thái của một cổng thanh toán.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cổng thanh toán |

**Body:**

```typescript
// UpdatePaymentGatewayStatusDto
{
  status: string;  // Trạng thái mới (ACTIVE, INACTIVE)
}
```

**Ví dụ:**

```json
{
  "status": "INACTIVE"
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Cập nhật trạng thái cổng thanh toán thành công",
  "result": {
    "id": 1,
    "accountId": "ACC123456",
    "companyId": 1,
    "bankCode": "VCB",
    "accountNumber": "**********",
    "accountHolderName": "Nguyen Van A",
    "label": "Tài khoản chính",
    "status": "INACTIVE",
    "requestId": "REQ123456",
    "isVa": false,
    "canCreateVa": true,
    "createdAt": *************,
    "updatedAt": *************
  }
}
```

---

## 2. Email Server Configuration Admin API

Quản lý cấu hình máy chủ email (SMTP) trong hệ thống.

### 2.1. Lấy danh sách cấu hình máy chủ email

```
GET /admin/integration/email-server
```

**Mô tả:** Lấy danh sách tất cả các cấu hình máy chủ email trong hệ thống với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| search | String | Không | Tìm kiếm theo tên máy chủ hoặc host |
| userId | Number | Không | Lọc theo ID người dùng |

**DTO:**

```typescript
// EmailServerQueryDto
{
  page?: number;        // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;       // Số lượng bản ghi trên mỗi trang
  search?: string;      // Tìm kiếm theo tên máy chủ hoặc host
  userId?: number;      // Lọc theo ID người dùng
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách cấu hình máy chủ email thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "serverName": "Gmail SMTP",
        "host": "smtp.gmail.com",
        "port": 587,
        "username": "<EMAIL>",
        "password": "********",
        "useSsl": true,
        "additionalSettings": {
          "auth": "login",
          "tls": {
            "rejectUnauthorized": false
          }
        },
        "createdAt": *************,
        "updatedAt": *************,
        "user": {
          "id": 123,
          "fullName": "Nguyen Van A",
          "email": "<EMAIL>"
        }
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 2.2. Lấy thông tin chi tiết cấu hình máy chủ email

```
GET /admin/integration/email-server/:id
```

**Mô tả:** Lấy thông tin chi tiết của một cấu hình máy chủ email cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết cấu hình máy chủ email thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "serverName": "Gmail SMTP",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "********",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": {
        "rejectUnauthorized": false
      }
    },
    "createdAt": *************,
    "updatedAt": *************,
    "user": {
      "id": 123,
      "fullName": "Nguyen Van A",
      "email": "<EMAIL>"
    }
  }
}
```

### 2.3. Tạo mới cấu hình máy chủ email

```
POST /admin/integration/email-server
```

**Mô tả:** Tạo một cấu hình máy chủ email mới trong hệ thống.

**Body:**

```typescript
// CreateEmailServerDto
{
  serverName: string;                  // Tên hiển thị của cấu hình
  host: string;                        // Địa chỉ máy chủ SMTP
  port: number;                        // Cổng SMTP
  username: string;                    // Tên đăng nhập
  password: string;                    // Mật khẩu
  useSsl: boolean;                     // Sử dụng SSL/TLS
  additionalSettings?: Record<string, any>; // Cấu hình nâng cao (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "serverName": "Gmail SMTP Admin",
  "host": "smtp.gmail.com",
  "port": 587,
  "username": "<EMAIL>",
  "password": "app-password-or-token",
  "useSsl": true,
  "additionalSettings": {
    "auth": "login",
    "tls": {
      "rejectUnauthorized": false
    }
  }
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Tạo mới cấu hình máy chủ email thành công",
  "result": {
    "id": 1,
    "serverName": "Gmail SMTP Admin",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "********",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": {
        "rejectUnauthorized": false
      }
    },
    "createdBy": 1,
    "createdAt": *************,
    "updatedAt": *************
  }
}
```

### 2.4. Cập nhật cấu hình máy chủ email

```
PUT /admin/integration/email-server/:id
```

**Mô tả:** Cập nhật thông tin của một cấu hình máy chủ email hiện có.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Body:**

```typescript
// UpdateEmailServerDto
{
  serverName?: string;                  // Tên hiển thị của cấu hình
  host?: string;                        // Địa chỉ máy chủ SMTP
  port?: number;                        // Cổng SMTP
  username?: string;                    // Tên đăng nhập
  password?: string;                    // Mật khẩu
  useSsl?: boolean;                     // Sử dụng SSL/TLS
  additionalSettings?: Record<string, any>; // Cấu hình nâng cao
}
```

**Ví dụ:**

```json
{
  "serverName": "Gmail SMTP Updated",
  "port": 465,
  "useSsl": true
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Cập nhật cấu hình máy chủ email thành công",
  "result": {
    "id": 1,
    "serverName": "Gmail SMTP Updated",
    "host": "smtp.gmail.com",
    "port": 465,
    "username": "<EMAIL>",
    "password": "********",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": {
        "rejectUnauthorized": false
      }
    },
    "createdBy": 1,
    "createdAt": *************,
    "updatedAt": *************,
    "updatedBy": 1
  }
}
```

### 2.5. Xóa cấu hình máy chủ email

```
DELETE /admin/integration/email-server/:id
```

**Mô tả:** Xóa một cấu hình máy chủ email khỏi hệ thống.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Xóa cấu hình máy chủ email thành công",
  "result": {
    "message": "Cấu hình máy chủ email đã được xóa"
  }
}
```

### 2.6. Kiểm tra kết nối máy chủ email

```
POST /admin/integration/email-server/:id/test
```

**Mô tả:** Kiểm tra kết nối đến máy chủ email và gửi email thử nghiệm.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Body:**

```typescript
// TestEmailServerDto
{
  recipientEmail: string;  // Địa chỉ email nhận thư kiểm tra
  subject?: string;        // Tiêu đề email kiểm tra (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "recipientEmail": "<EMAIL>",
  "subject": "Test Email Connection"
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Kiểm tra kết nối máy chủ email thành công",
  "result": {
    "success": true,
    "message": "Kết nối thành công! Email kiểm tra đã được gửi."
  }
}
```

**Phản hồi lỗi:**

```json
{
  "code": 200,
  "message": "Kiểm tra kết nối máy chủ email thành công",
  "result": {
    "success": false,
    "message": "Kết nối thất bại!",
    "details": "Invalid login: 535-5.7.8 Username and Password not accepted."
  }
}
```

---

## 3. Facebook Page Admin API

Quản lý các trang Facebook được kết nối trong hệ thống.

### 3.1. Lấy danh sách trang Facebook

```
GET /admin/integration/facebook-page
```

**Mô tả:** Lấy danh sách tất cả các trang Facebook được kết nối trong hệ thống với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| search | String | Không | Tìm kiếm theo tên trang Facebook |
| userId | Number | Không | Lọc theo ID người dùng |
| hasAgent | Boolean | Không | Lọc theo trạng thái kết nối với agent |

**DTO:**

```typescript
// FacebookPageQueryDto
{
  page?: number;        // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;       // Số lượng bản ghi trên mỗi trang
  search?: string;      // Tìm kiếm theo tên trang Facebook
  userId?: number;      // Lọc theo ID người dùng
  hasAgent?: boolean;   // Lọc theo trạng thái kết nối với agent
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách trang Facebook thành công",
  "result": {
    "items": [
      {
        "id": "123456789",
        "userId": 123,
        "name": "Trang Facebook của tôi",
        "accessToken": "********",
        "category": "Business",
        "picture": "https://graph.facebook.com/123456789/picture",
        "agentId": 1,
        "createdAt": *************,
        "updatedAt": *************,
        "user": {
          "id": 123,
          "fullName": "Nguyen Van A",
          "email": "<EMAIL>"
        },
        "agent": {
          "id": 1,
          "name": "Agent ABC"
        }
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 3.2. Lấy thông tin chi tiết trang Facebook

```
GET /admin/integration/facebook-page/:id
```

**Mô tả:** Lấy thông tin chi tiết của một trang Facebook cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | String | Có | ID của trang Facebook |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết trang Facebook thành công",
  "result": {
    "id": "123456789",
    "userId": 123,
    "name": "Trang Facebook của tôi",
    "accessToken": "********",
    "category": "Business",
    "picture": "https://graph.facebook.com/123456789/picture",
    "agentId": 1,
    "createdAt": *************,
    "updatedAt": *************,
    "user": {
      "id": 123,
      "fullName": "Nguyen Van A",
      "email": "<EMAIL>"
    },
    "agent": {
      "id": 1,
      "name": "Agent ABC"
    }
  }
}
```

---

## 4. User Key Admin API

Quản lý các API key của người dùng trong hệ thống.

### 4.1. Lấy danh sách API key

```
GET /admin/integration/user-key
```

**Mô tả:** Lấy danh sách tất cả các API key của người dùng trong hệ thống với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| userId | Number | Không | Lọc theo ID người dùng |
| providerKey | String | Không | Lọc theo key của nhà cung cấp |
| isActive | Boolean | Không | Lọc theo trạng thái hoạt động |

**DTO:**

```typescript
// UserKeyAdminQueryDto
{
  page?: number;        // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;       // Số lượng bản ghi trên mỗi trang
  userId?: number;      // Lọc theo ID người dùng
  providerKey?: string; // Lọc theo key của nhà cung cấp
  isActive?: boolean;   // Lọc theo trạng thái hoạt động
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách API key thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "providerId": 1,
        "credentials": {
          "api_key": "********abcd",
          "organization_id": "org-******"
        },
        "settings": {
          "is_active": true,
          "is_default": true
        },
        "createdAt": *************,
        "updatedAt": *************,
        "provider": {
          "id": 1,
          "providerKey": "openai",
          "name": "OpenAI",
          "icon": "https://example.com/openai-icon.png"
        },
        "user": {
          "id": 123,
          "fullName": "Nguyen Van A",
          "email": "<EMAIL>"
        }
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 4.2. Lấy thông tin chi tiết API key

```
GET /admin/integration/user-key/:id
```

**Mô tả:** Lấy thông tin chi tiết của một API key cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của API key |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết API key thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "providerId": 1,
    "credentials": {
      "api_key": "********abcd",
      "organization_id": "org-******"
    },
    "settings": {
      "is_active": true,
      "is_default": true
    },
    "createdAt": *************,
    "updatedAt": *************,
    "provider": {
      "id": 1,
      "providerKey": "openai",
      "name": "OpenAI",
      "icon": "https://example.com/openai-icon.png"
    },
    "user": {
      "id": 123,
      "fullName": "Nguyen Van A",
      "email": "<EMAIL>"
    }
  }
}
```

---

## 5. SMS Server Configuration Admin API

Quản lý cấu hình máy chủ SMS trong hệ thống.

### 5.1. Lấy danh sách cấu hình máy chủ SMS

```
GET /admin/integration/sms-server
```

**Mô tả:** Lấy danh sách tất cả các cấu hình máy chủ SMS trong hệ thống với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| search | String | Không | Tìm kiếm theo tên máy chủ |
| userId | Number | Không | Lọc theo ID người dùng |

**DTO:**

```typescript
// SmsServerQueryDto
{
  page?: number;        // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;       // Số lượng bản ghi trên mỗi trang
  search?: string;      // Tìm kiếm theo tên máy chủ
  userId?: number;      // Lọc theo ID người dùng
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách cấu hình máy chủ SMS thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "serverName": "Twilio SMS",
        "providerKey": "twilio",
        "accountSid": "AC**********",
        "authToken": "********",
        "fromNumber": "+***********",
        "additionalSettings": {
          "region": "ap-southeast-1"
        },
        "createdAt": *************,
        "updatedAt": *************,
        "user": {
          "id": 123,
          "fullName": "Nguyen Van A",
          "email": "<EMAIL>"
        }
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 5.2. Lấy thông tin chi tiết cấu hình máy chủ SMS

```
GET /admin/integration/sms-server/:id
```

**Mô tả:** Lấy thông tin chi tiết của một cấu hình máy chủ SMS cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ SMS |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết cấu hình máy chủ SMS thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "serverName": "Twilio SMS",
    "providerKey": "twilio",
    "accountSid": "AC**********",
    "authToken": "********",
    "fromNumber": "+***********",
    "additionalSettings": {
      "region": "ap-southeast-1"
    },
    "createdAt": *************,
    "updatedAt": *************,
    "user": {
      "id": 123,
      "fullName": "Nguyen Van A",
      "email": "<EMAIL>"
    }
  }
}
```

## Lỗi chung

Tất cả các API đều có thể trả về các mã lỗi sau:

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Bad Request - Dữ liệu không hợp lệ |
| 401 | Unauthorized - Không có quyền truy cập |
| 403 | Forbidden - Không có quyền thực hiện hành động |
| 404 | Not Found - Không tìm thấy tài nguyên |
| 500 | Internal Server Error - Lỗi máy chủ |

Cấu trúc phản hồi lỗi:

```json
{
  "code": 12000, // Mã lỗi cụ thể của module
  "message": "Thông báo lỗi chi tiết",
  "result": null
}
```
