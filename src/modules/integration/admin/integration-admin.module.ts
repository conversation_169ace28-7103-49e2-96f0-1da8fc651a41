import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as controllers from './controllers';
import * as services from './services';
import { MarketingAdminModule } from '@/modules/marketing/admin/marketing-admin.module';

/**
 * <PERSON>dule quản lý tích hợp cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(entities)),
    MarketingAdminModule,
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
  ],
  exports: [
    ...Object.values(services),
  ],
})
export class IntegrationAdminModule {}
