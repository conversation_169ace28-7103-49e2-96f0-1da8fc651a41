import { IsEmail, IsNotEmpty, IsOptional, IsPhoneNumber, IsString, IsArray, IsNumber, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo audience thông qua integration admin
 */
export class CreateIntegrationAudienceDto {
  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên không được để trống' })
  @IsString({ message: 'Tên phải là chuỗi' })
  name: string;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: 'kha<PERSON><PERSON>@example.com',
    required: true,
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> không được để trống' })
  @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })
  email: string;

  /**
   * Số điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
    required: true,
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
  phone: string;

  /**
   * Mã quốc gia của số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: '+84',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã quốc gia phải là chuỗi' })
  @Matches(/^\+\d{1,4}$/, { message: 'Mã quốc gia không hợp lệ (ví dụ: +84, +1)' })
  countryCode?: string;

  /**
   * Danh sách ID của các tag
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của các tag',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Tag IDs phải là mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi tag ID phải là số' })
  @Type(() => Number)
  tagIds?: number[];
}
