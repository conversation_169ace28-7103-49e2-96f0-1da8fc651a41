import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response khi tạo audience thông qua integration admin
 */
export class IntegrationAudienceResponseDto {
  /**
   * ID của audience
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  id: number;

  /**
   * Tên của khách hàng
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
  })
  name: string;

  /**
   * Email của khách hàng
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Số điện thoại của khách hàng
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
  })
  phone: string;

  /**
   * Mã quốc gia của số điện thoại
   */
  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: '+84',
  })
  countryCode: string;

  /**
   * Danh sách tag IDs được gán
   */
  @ApiProperty({
    description: 'Danh sách tag IDs được gán',
    example: [1, 2, 3],
    type: [Number],
  })
  tagIds: number[];

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1640995200,
  })
  updatedAt: number;

  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Audience đã được tạo thành công',
  })
  message: string;
}
