import { Injectable, NotFoundException } from '@nestjs/common';
import { AdminAudienceService } from '@/modules/marketing/admin/services/admin-audience.service';
import { AdminAudienceHasTagRepository } from '@/modules/marketing/admin/repositories/admin-audience-has-tag.repository';
import { AdminTagRepository } from '@/modules/marketing/admin/repositories/admin-tag.repository';
import { CreateIntegrationAudienceDto, IntegrationAudienceResponseDto } from '../dto';
import { CreateAudienceDto } from '@/modules/marketing/admin/dto/audience';
import { Transactional } from 'typeorm-transactional';
import { AdminAudienceHasTag } from '@/modules/marketing/admin/entities';
import { In } from 'typeorm';

/**
 * Service xử lý logic tạo audience thông qua integration admin
 */
@Injectable()
export class IntegrationAudienceService {
  constructor(
    private readonly adminAudienceService: AdminAudienceService,
    private readonly adminAudienceHasTagRepository: AdminAudienceHasTagRepository,
    private readonly adminTagRepository: AdminTagRepository,
  ) {}

  /**
   * Tạo audience mới thông qua integration admin
   * @param createDto Dữ liệu tạo audience
   * @returns Thông tin audience đã tạo
   */
  @Transactional()
  async create(createDto: CreateIntegrationAudienceDto): Promise<IntegrationAudienceResponseDto> {
    // Validate tags nếu có
    if (createDto.tagIds && createDto.tagIds.length > 0) {
      const existingTags = await this.adminTagRepository.find({
        where: {
          id: In(createDto.tagIds),
        },
      });

      if (existingTags.length !== createDto.tagIds.length) {
        const existingTagIds = existingTags.map(tag => tag.id);
        const missingTagIds = createDto.tagIds.filter(id => !existingTagIds.includes(id));
        throw new NotFoundException(`Không tìm thấy tag với ID: ${missingTagIds.join(', ')}`);
      }
    }

    // Chuyển đổi DTO để sử dụng với AdminAudienceService
    const adminCreateDto: CreateAudienceDto = {
      name: createDto.name,
      email: createDto.email,
      phone: createDto.phone,
      countryCode: createDto.countryCode,
      tagIds: createDto.tagIds,
    };

    // Tạo audience thông qua AdminAudienceService
    const createdAudience = await this.adminAudienceService.create(adminCreateDto);

    // Tạo mối quan hệ với tags nếu có
    if (createDto.tagIds && createDto.tagIds.length > 0) {
      const audienceHasTags = createDto.tagIds.map(tagId => {
        const audienceHasTag = new AdminAudienceHasTag();
        audienceHasTag.audienceId = createdAudience.id;
        audienceHasTag.tagId = tagId;
        return audienceHasTag;
      });

      await this.adminAudienceHasTagRepository.save(audienceHasTags);
    }

    // Tạo response DTO
    const response: IntegrationAudienceResponseDto = {
      id: createdAudience.id,
      name: createdAudience.name || '',
      email: createdAudience.email,
      phone: createdAudience.phone || '',
      countryCode: createdAudience.countryCode || '+84',
      tagIds: createDto.tagIds || [],
      createdAt: createdAudience.createdAt,
      updatedAt: createdAudience.updatedAt,
      message: 'Audience đã được tạo thành công thông qua integration admin',
    };

    return response;
  }

  /**
   * Validate email không trùng lặp
   * @param email Email cần kiểm tra
   * @returns true nếu email chưa tồn tại
   */
  async validateUniqueEmail(email: string): Promise<boolean> {
    try {
      // Sử dụng findAll với email filter để kiểm tra
      const result = await this.adminAudienceService.findAll({ 
        page: 1, 
        limit: 1, 
        email: email 
      });
      
      return result.data.length === 0;
    } catch (error) {
      // Nếu có lỗi, cho phép tạo (có thể là lỗi khác)
      return true;
    }
  }
}
