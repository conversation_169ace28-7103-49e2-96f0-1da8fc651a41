# Integration Audience API Examples

## Tạo Audience mới thông qua Integration Admin

### Endpoint
```
POST /admin/integration/audiences
```

### Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### Request Body

#### Ví dụ 1: Tạo audience với đầy đủ thông tin
```json
{
  "name": "Nguyễn <PERSON>n <PERSON>",
  "email": "<EMAIL>",
  "phone": "+84912345678",
  "tagIds": [1, 2, 3]
}
```

#### Ví dụ 2: Tạo audience không có tags
```json
{
  "name": "Trần Thị B",
  "email": "<EMAIL>",
  "phone": "+84987654321"
}
```

### Response Success (201)
```json
{
  "success": true,
  "message": "Audience đã được tạo thành công thông qua integration admin",
  "data": {
    "id": 123,
    "name": "<PERSON><PERSON><PERSON><PERSON>",
    "email": "nguy<PERSON><EMAIL>",
    "phone": "+84912345678",
    "tagIds": [1, 2, 3],
    "createdAt": 1640995200,
    "updatedAt": 1640995200,
    "message": "Audience đã được tạo thành công thông qua integration admin"
  }
}
```

### Response Error Examples

#### 400 - Email đã tồn tại
```json
{
  "success": false,
  "message": "Email <EMAIL> đã tồn tại trong hệ thống",
  "error": "Bad Request"
}
```

#### 400 - Validation Error
```json
{
  "success": false,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "email",
      "message": "Email không hợp lệ"
    },
    {
      "field": "phone",
      "message": "Số điện thoại không hợp lệ"
    }
  ]
}
```

#### 404 - Tag không tồn tại
```json
{
  "success": false,
  "message": "Không tìm thấy tag với ID: 999",
  "error": "Not Found"
}
```

#### 401 - Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

## Validation Rules

### Required Fields
- `name`: Tên khách hàng (bắt buộc)
- `email`: Email hợp lệ (bắt buộc)
- `phone`: Số điện thoại hợp lệ (bắt buộc)

### Optional Fields
- `tagIds`: Mảng các ID tag (tùy chọn)

### Validation Details
- Email phải có định dạng hợp lệ
- Số điện thoại phải có định dạng quốc tế hợp lệ (hỗ trợ nhiều quốc gia)
- Tag IDs phải là mảng các số nguyên
- Tất cả tag IDs phải tồn tại trong hệ thống

## Use Cases

### 1. Tích hợp với CRM bên ngoài
API này có thể được sử dụng để đồng bộ khách hàng từ hệ thống CRM bên ngoài vào hệ thống marketing.

### 2. Import dữ liệu từ file Excel/CSV
Có thể sử dụng API này để import hàng loạt khách hàng từ file dữ liệu.

### 3. Webhook từ landing page
Khi có khách hàng mới đăng ký từ landing page, có thể gọi API này để tự động tạo audience.

## Authentication
API này yêu cầu JWT token của employee (admin). Token phải được gửi trong header Authorization với format:
```
Authorization: Bearer <JWT_TOKEN>
```
