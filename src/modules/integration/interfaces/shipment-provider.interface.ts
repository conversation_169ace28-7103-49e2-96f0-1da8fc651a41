/**
 * Interface cơ bản cho tất cả nhà cung cấp vận chuyển
 */
export interface BaseShipmentProvider {
  /** Tên hiển thị của cấu hình */
  name?: string;
  /** Trạng thái kích hoạt */
  isActive?: boolean;
  /** Ghi chú */
  notes?: string;
}

// Import các interfaces từ files riêng
import { GHNProviderConfig } from './ghn-provider-config.interface';
import { GHTKProviderConfig } from './ghtk-provider-config.interface';
import { AhamoveProviderConfig } from './ahamove-provider-config.interface';
import { JTProviderConfig } from './jt-provider-config.interface';

/**
 * Union type cho tất cả các loại cấu hình nhà cung cấp
 */
export type ShipmentProviderConfig =
  | GHNProviderConfig
  | GHTKProviderConfig
  | AhamoveProviderConfig
  | JTProviderConfig;

/**
 * Interface cho response chung của các API vận chuyển
 */
export interface ShipmentApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  code?: number;
}

/**
 * Interface cho thông tin đơn hàng cơ bản
 */
export interface BaseOrderInfo {
  /** Mã đơn hàng của khách */
  clientOrderCode: string;
  /** Thông tin người gửi */
  sender: {
    name: string;
    phone: string;
    address: string;
    ward?: string;
    district: string;
    province: string;
  };
  /** Thông tin người nhận */
  receiver: {
    name: string;
    phone: string;
    address: string;
    ward?: string;
    district: string;
    province: string;
  };
  /** Thông tin hàng hóa */
  items: Array<{
    name: string;
    quantity: number;
    weight: number;
    value?: number;
  }>;
  /** Tổng trọng lượng (gram) */
  totalWeight: number;
  /** Giá trị COD */
  codAmount?: number;
  /** Ghi chú */
  note?: string;
  /** Loại dịch vụ */
  serviceType?: string;
}
