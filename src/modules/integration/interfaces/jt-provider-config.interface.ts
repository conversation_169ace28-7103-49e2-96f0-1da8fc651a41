/**
 * Interface cho J&T Express
 * Yêu cầu: Username và API Key
 */
export interface JTProviderConfig {
  /** Tên đăng nhập từ J&T dashboard */
  username: string;
  /** API Key từ J&T dashboard */
  apiKey: string;
  /** <PERSON><PERSON><PERSON> trườ<PERSON> (staging/production) */
  environment?: 'staging' | 'production';
  /** Tên hiển thị của cấu hình */
  name?: string;
  /** Trạng thái kích hoạt */
  isActive?: boolean;
  /** Ghi chú */
  notes?: string;
}
