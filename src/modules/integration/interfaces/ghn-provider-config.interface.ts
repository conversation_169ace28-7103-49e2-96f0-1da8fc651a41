/**
 * Interface cho GHN (Giao Hàng Nhanh)
 * <PERSON><PERSON><PERSON> cầu: Token và ShopID
 */
export interface GHNProviderConfig {
  /** API Token từ GHN dashboard */
  token: string;
  /** Shop ID từ GHN */
  shopId: string;
  /** <PERSON><PERSON><PERSON> trườ<PERSON> (staging/production) */
  environment?: 'staging' | 'production';
  /** Tên hiển thị của cấu hình */
  name?: string;
  /** Trạng thái kích hoạt */
  isActive?: boolean;
  /** Ghi chú */
  notes?: string;
}
