import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserWebsite } from '@modules/integration/entities';
import { Agent } from '@modules/agent/entities';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@/common/response/api-response-dto';

@Injectable()
export class UserWebsiteRepository extends Repository<UserWebsite> {
  private readonly logger = new Logger(UserWebsiteRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserWebsite, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserWebsite
   * @returns SelectQueryBuilder cho UserWebsite
   */
  createBaseQuery(): SelectQueryBuilder<UserWebsite> {
    return this.createQueryBuilder('website');
  }

  /**
   * Tìm website theo ID và user ID
   * @param id ID của website
   * @param userId ID của người dùng
   * @returns Website nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserWebsite | null> {
    try {
      return this.createBaseQuery()
        .where('website.id = :id', { id })
        .andWhere('website.user_id = :userId', { userId })
        .select([
          'website.id',
          'website.websiteName',
          'website.host',
          'website.verify',
          'website.agentId',
          'website.logo'
        ])
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm website theo ID và user ID: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_FOUND);
    }
  }

  /**
   * Tìm website theo ID và agent ID
   * @param id ID của website
   * @param agentId ID của agent
   * @returns Website nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdAndAgentId(id: string, agentId: string): Promise<UserWebsite | null> {
    try {
      return this.createBaseQuery()
        .where('website.id = :id', { id })
        .andWhere('website.agent_id = :agentId', { agentId })
        .select([
          'website.id',
          'website.websiteName',
          'website.host',
          'website.verify',
          'website.agentId',
          'website.logo'
        ])
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm website theo ID và agent ID: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_FOUND);
    }
  }

  /**
   * Tìm tất cả website theo agent ID
   * @param agentId ID của agent
   * @returns Danh sách website
   */
  async findAllByAgentId(agentId: string): Promise<UserWebsite[]> {
    try {
      return this.createBaseQuery()
        .where('website.agent_id = :agentId', { agentId })
        .select([
          'website.id',
          'website.websiteName',
          'website.host',
          'website.verify',
          'website.agentId',
          'website.logo'
        ])
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tất cả website theo agent ID: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_FOUND);
    }
  }

  /**
   * Cập nhật agentId cho website
   * @param websiteId ID của website
   * @param agentId ID của agent (null để gỡ bỏ)
   */
  async updateAgentId(websiteId: string, agentId: string | null): Promise<void> {
    try {
      await this.createQueryBuilder()
        .update(UserWebsite)
        .set({ agentId })
        .where('id = :websiteId', { websiteId })
        .execute();
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật agentId cho website: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm website theo ID và user ID để xóa
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @returns Website nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdAndUserIdForDelete(websiteId: string, userId: number): Promise<UserWebsite | null> {
    try {
      return this.createBaseQuery()
        .where('website.id = :websiteId', { websiteId })
        .andWhere('website.user_id = :userId', { userId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm website để xóa: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm nhiều website theo danh sách ID và user ID
   * @param websiteIds Danh sách ID của các website
   * @param userId ID của người dùng
   * @returns Danh sách website
   */
  async findByIdsAndUserId(websiteIds: string[], userId: number): Promise<UserWebsite[]> {
    try {
      if (!websiteIds || websiteIds.length === 0) {
        return [];
      }

      return this.createBaseQuery()
        .where('website.id IN (:...websiteIds)', { websiteIds })
        .andWhere('website.user_id = :userId', { userId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm nhiều website theo IDs: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Lấy danh sách website với phân trang và join với agent để lấy tên agent
   * @param userId ID của người dùng
   * @param page Trang hiện tại
   * @param limit Số lượng item trên mỗi trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param hasAgent Lọc theo trạng thái gán agent (true: đã gán, false: chưa gán, undefined: tất cả)
   * @returns Kết quả phân trang với thông tin website và agent
   */
  async findWithPagination(
    userId: number,
    page: number,
    limit: number,
    search?: string,
    hasAgent?: boolean,
  ): Promise<PaginatedResult<{
    id: string;
    host: string;
    verify: boolean;
    agentId: string | null;
    agentName: string | null;
    logo: string | null;
    createdAt: Date;
  }>> {
    try {
      // Tạo base query chỉ với điều kiện user (không có search) để tính hasItems
      const baseQueryWithoutSearch = this.createBaseQuery()
        .leftJoin(Agent, 'agent', 'website.agent_id = agent.id')
        .where('website.user_id = :userId', { userId });

      // Đếm tổng số bản ghi của user (không filter search) để tính hasItems
      const totalUserItems = await baseQueryWithoutSearch.clone().getCount();
      const hasItems = totalUserItems > 0;

      // Tạo base query với điều kiện chung (bao gồm search nếu có)
      const baseQuery = baseQueryWithoutSearch.clone();

      // Thêm điều kiện tìm kiếm nếu có
      if (search && search.trim()) {
        baseQuery.andWhere(
          'LOWER(website.host) LIKE LOWER(:search)',
          { search: `%${search.trim()}%` }
        );
      }

      // Thêm điều kiện lọc theo trạng thái gán agent
      if (hasAgent !== undefined) {
        if (hasAgent) {
          // Lọc website đã gán agent (agentId không null)
          baseQuery.andWhere('website.agent_id IS NOT NULL');
        } else {
          // Lọc website chưa gán agent (agentId null)
          baseQuery.andWhere('website.agent_id IS NULL');
        }
      }

      // Đếm tổng số bản ghi sau khi filter search
      const countQuery = baseQuery.clone();
      const totalItems = await countQuery.getCount();

      // Lấy dữ liệu với phân trang (clone query và thêm select + pagination)
      const dataQuery = baseQuery.clone()
        .select([
          'website.id',
          'website.host',
          'website.verify',
          'website.createdAt',
          'website.agentId',
          'website.logo',
          'agent.name as agentName',
        ]);

      const items = await dataQuery
        .orderBy('website.created_at', 'DESC')
        .skip((page - 1) * limit)
        .take(limit)
        .getRawMany();

      // Tính toán metadata phân trang
      const totalPages = Math.ceil(totalItems / limit);
      const itemCount = items.length;

      return {
        items: items.map(item => ({
          id: item.website_id,
          host: item.website_host,
          verify: item.website_verify,
          agentId: item.website_agentId,
          agentName: item.agentName,
          logo: item.website_logo,
          createdAt: item.website_createdAt,
        })),
        meta: {
          totalItems,
          itemCount,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách website với phân trang: ${error.message}`, error.stack);
      throw error;
    }
  }
}
