import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WhatsAppTemplate } from '../entities';

/**
 * Repository quản lý mẫu tin nhắn WhatsApp
 */
@Injectable()
export class WhatsAppTemplateRepository {
  constructor(
    @InjectRepository(WhatsAppTemplate)
    private readonly repository: Repository<WhatsAppTemplate>,
  ) {}

  /**
   * Tìm mẫu tin nhắn WhatsApp theo ID
   * @param id ID của mẫu tin nhắn
   * @returns Thông tin mẫu tin nhắn
   */
  async findById(id: number): Promise<WhatsAppTemplate | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['whatsappAccount'],
    });
  }

  /**
   * Tìm mẫu tin nhắn WhatsApp theo ID tài khoản WhatsApp
   * @param whatsappAccountId ID của tài khoản WhatsApp
   * @returns Danh sách mẫu tin nhắn của tài khoản
   */
  async findByWhatsAppAccountId(whatsappAccountId: number): Promise<WhatsAppTemplate[]> {
    return this.repository.find({
      where: { whatsappAccountId },
      order: { name: 'ASC' },
    });
  }

  /**
   * Tìm mẫu tin nhắn WhatsApp theo tên và ID tài khoản
   * @param name Tên mẫu tin nhắn
   * @param whatsappAccountId ID của tài khoản WhatsApp
   * @returns Thông tin mẫu tin nhắn
   */
  async findByNameAndAccountId(name: string, whatsappAccountId: number): Promise<WhatsAppTemplate | null> {
    return this.repository.findOne({
      where: { name, whatsappAccountId },
    });
  }

  /**
   * Tạo mới mẫu tin nhắn WhatsApp
   * @param data Dữ liệu mẫu tin nhắn
   * @returns Mẫu tin nhắn đã tạo
   */
  async create(data: Partial<WhatsAppTemplate>): Promise<WhatsAppTemplate> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật mẫu tin nhắn WhatsApp
   * @param id ID của mẫu tin nhắn
   * @param data Dữ liệu cập nhật
   * @returns Mẫu tin nhắn đã cập nhật
   */
  async update(id: number, data: Partial<WhatsAppTemplate>): Promise<WhatsAppTemplate | null> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Math.floor(Date.now() / 1000),
    });
    return this.findById(id);
  }

  /**
   * Xóa mẫu tin nhắn WhatsApp
   * @param id ID của mẫu tin nhắn
   * @returns Kết quả xóa
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Cập nhật trạng thái mẫu tin nhắn WhatsApp
   * @param id ID của mẫu tin nhắn
   * @param status Trạng thái mới
   * @returns Mẫu tin nhắn đã cập nhật
   */
  async updateStatus(id: number, status: string): Promise<WhatsAppTemplate | null> {
    return this.update(id, { status });
  }
}
