import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { UserProviderShipment } from '../entities/user-provider-shipment.entity';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho UserProviderShipment entity
 * Xử lý các thao tác database liên quan đến cấu hình nhà cung cấp vận chuyển
 */
@Injectable()
export class UserProviderShipmentRepository extends Repository<UserProviderShipment> {
  constructor(
    @InjectRepository(UserProviderShipment)
    private readonly repository: Repository<UserProviderShipment>
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  /**
   * Tạo query builder c<PERSON> bản
   */
  createBaseQuery() {
    return this.createQueryBuilder('shipment');
  }

  /**
   * <PERSON><PERSON><PERSON> tất cả cấu hình của user với phân trang
   */
  async findByUserIdWithPagination(
    userId: number,
    page: number = 1,
    limit: number = 10,
    type?: ProviderShipmentType
  ): Promise<PaginatedResult<UserProviderShipment>> {
    const queryBuilder = this.createBaseQuery()
      .where('shipment.userId = :userId', { userId });

    // Thêm filter theo type nếu có
    if (type) {
      queryBuilder.andWhere('shipment.type = :type', { type });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Lấy dữ liệu phân trang với sorting
    const items = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('shipment.createdAt', 'DESC')
      .getMany();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page
      }
    };
  }

  /**
   * Tìm cấu hình theo ID và userId
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserProviderShipment | null> {
    return this.createBaseQuery()
      .where('shipment.id = :id', { id })
      .andWhere('shipment.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Tìm cấu hình theo type và userId
   */
  async findByTypeAndUserId(type: ProviderShipmentType, userId: number): Promise<UserProviderShipment[]> {
    return this.createBaseQuery()
      .where('shipment.type = :type', { type })
      .andWhere('shipment.userId = :userId', { userId })
      .orderBy('shipment.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Kiểm tra xem user đã có cấu hình cho type này chưa
   */
  async existsByTypeAndUserId(type: ProviderShipmentType, userId: number): Promise<boolean> {
    const count = await this.createQueryBuilder('shipment')
      .where('shipment.type = :type', { type })
      .andWhere('shipment.userId = :userId', { userId })
      .getCount();

    return count > 0;
  }

  /**
   * Xóa cấu hình theo ID và userId
   */
  async deleteByIdAndUserId(id: string, userId: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .delete()
      .from(UserProviderShipment)
      .where('id = :id', { id })
      .andWhere('userId = :userId', { userId })
      .execute();

    return (result.affected ?? 0) > 0;
  }

  /**
   * Đếm số lượng cấu hình của user
   */
  async countByUserId(userId: number): Promise<number> {
    return this.createQueryBuilder('shipment')
      .where('shipment.userId = :userId', { userId })
      .getCount();
  }




}
