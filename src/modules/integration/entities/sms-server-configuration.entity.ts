import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '@modules/user/entities';

/**
 * Entity đại diện cho bảng sms_server_configurations trong cơ sở dữ liệu
 * Lưu trữ thông tin cấu hình cho máy chủ SMS
 */
@Entity('sms_server_configurations')
export class SmsServerConfiguration {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tên nhà cung cấp SMS, ví dụ: "Twilio", "Vonage", "Nexmo", …
   */
  @Column({ name: 'provider_name', length: 100 })
  providerName: string;

  /**
   * Khoá (key) hoặc token của nhà cung cấp
   */
  @Column({ name: 'api_key', length: 255, nullable: true })
  apiKey: string;

  /**
   * URL endpoint để gọi API (có thể NULL nếu nhà cung cấp đã cố định)
   */
  @Column({ name: 'endpoint', length: 255, nullable: true })
  endpoint: string;

  /**
   * Lưu các cấu hình tùy biến, ví dụ: "Message Service SID", "Short Code", "Alphanumeric Sender ID", v.v.
   */
  @Column({ name: 'additional_settings', type: 'json', nullable: true })
  additionalSettings: any;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
