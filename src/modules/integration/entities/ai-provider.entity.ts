import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng ai_providers trong cơ sở dữ liệu
 * Lưu trữ thông tin về các nhà cung cấp AI
 */
@Entity('ai_providers')
export class AiProvider {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Key nội bộ định danh nhà cung cấp (openai, anthropic, gemini, etc)
   */
  @Column({ name: 'provider_key', length: 255, unique: true })
  providerKey: string;

  /**
   * Tên hiển thị của nhà cung cấp
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * Đường dẫn icon của nhà cung cấp
   */
  @Column({ name: 'icon', length: 255, nullable: true })
  icon: string;

  /**
   * URL mặc định để gọi API
   */
  @Column({ name: 'base_url', length: 255, nullable: true })
  baseUrl: string;

  /**
   * Đường dẫn tài liệu API chính thức
   */
  @Column({ name: 'documentation_url', length: 255, nullable: true })
  documentationUrl: string;

  /**
   * Trạng thái: active, inactive, deprecated
   */
  @Column({ name: 'status', length: 25, default: 'active' })
  status: string;

  /**
   * Thời điểm tạo (epoch time)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (epoch time)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
