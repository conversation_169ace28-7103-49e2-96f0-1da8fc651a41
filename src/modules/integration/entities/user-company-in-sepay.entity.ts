import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_company_in_sepay trong cơ sở dữ liệu
 * Bảng này là đại diện company trên sepay-hub đối với mỗi người dùng
 */
@Entity('user_company_in_sepay')
export class UserCompanyInSepay {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Mã công ty trên sepay-hub
   */
  @Column({ name: 'company_id', length: 20, nullable: true })
  companyId: string;

  /**
   * Mã người dùng
   */
  @Column({ name: 'user_id', unique: true })
  userId: number;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
