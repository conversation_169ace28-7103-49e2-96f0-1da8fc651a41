import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng payment_gateway trong cơ sở dữ liệu
 * Bảng lưu thông tin tài khoản ngân hàng liên kết
 */
@Entity('payment_gateway')
export class PaymentGateway {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Mã tài khoản ngân hàng trên sepay
   */
  @Column({ name: 'account_id', length: 30 })
  accountId: string;

  /**
   * ID công ty liên kết với user
   */
  @Column({ name: 'company_id', nullable: true })
  companyId: number;

  /**
   * Mã ngân hàng
   */
  @Column({ name: 'bank_code', length: 20, nullable: true })
  bankCode: string;

  /**
   * <PERSON>ố tài khoản
   */
  @Column({ name: 'account_number', length: 30, nullable: true })
  accountNumber: string;

  /**
   * <PERSON>ăn cước công dân
   */
  @Column({ name: 'identification_number', length: 30, nullable: true })
  identificationNumber: string;

  /**
   * Số điện thoại đăng ký tài khoản ngân hàng
   */
  @Column({ name: 'phone_number', length: 20, nullable: true })
  phoneNumber: string;

  /**
   * Nhãn
   */
  @Column({ name: 'label', length: 50, nullable: true })
  label: string;

  /**
   * Trạng thái
   */
  @Column({ name: 'status', length: 30, nullable: true })
  status: string;

  /**
   * Mã liên kết
   */
  @Column({ name: 'request_id', length: 100, nullable: true })
  requestId: string;

  /**
   * Họ tên tài khoản ngân hàng
   */
  @Column({ name: 'account_holder_name', length: 50, nullable: true })
  accountHolderName: string;

  /**
   * Nơi điểm bán
   */
  @Column({ name: 'merchant_address', length: 1000, nullable: true })
  merchantAddress: string;

  /**
   * Tên điểm bán
   */
  @Column({ name: 'merchant_name', length: 500, nullable: true })
  merchantName: string;

  /**
   * Có phải tài khoản VA hay không
   */
  @Column({ name: 'is_va', nullable: true })
  isVa: boolean;

  /**
   * Nếu là tài khoản VA thì tài khoản gốc là mã này
   */
  @Column({ name: 'main_id', nullable: true })
  mainId: number;

  /**
   * Mã VA
   */
  @Column({ name: 'va_id', length: 30, nullable: true })
  vaId: string;

  /**
   * Tài khoản này có tạo được tài khoản VA hay không
   */
  @Column({ name: 'can_create_va', default: false })
  canCreateVa: boolean;
}
