import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng ai_provider_usage trong cơ sở dữ liệu
 * L<PERSON><PERSON> lịch sử sử dụng API của người dùng
 */
@Entity('ai_provider_usage')
export class AiProviderUsage {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng thực hiện request
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Nhà cung cấp được dùng
   */
  @Column({ name: 'provider_id' })
  providerId: number;

  /**
   * Cấu hình sử dụng để gọi API
   */
  @Column({ name: 'config_id' })
  configId: number;

  /**
   * Model cụ thể được gọi
   */
  @Column({ name: 'model_id', type: 'text' })
  modelId: string;

  /**
   * Loại request: chat, image, audio...
   */
  @Column({ name: 'request_type', type: 'text' })
  requestType: string;

  /**
   * Số tokens đầu vào
   */
  @Column({ name: 'tokens_input', type: 'integer', default: 0 })
  tokensInput: number;

  /**
   * Số tokens đầu ra
   */
  @Column({ name: 'tokens_output', type: 'integer', default: 0 })
  tokensOutput: number;

  /**
   * Chi phí request (đơn vị tiền)
   */
  @Column({ name: 'cost', type: 'decimal', default: 0 })
  cost: number;

  /**
   * ID của request trong hệ thống gốc (nếu có)
   */
  @Column({ name: 'request_id', type: 'text', nullable: true })
  requestId: string;

  /**
   * Thông tin bổ sung của request (JSON)
   */
  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  /**
   * Thời điểm request (epoch time)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;
}
