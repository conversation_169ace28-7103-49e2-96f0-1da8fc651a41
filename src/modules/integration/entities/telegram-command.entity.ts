import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { TelegramBot } from './telegram-bot.entity';

/**
 * Entity đại diện cho bảng telegram_commands trong cơ sở dữ liệu
 * Lưu trữ các lệnh tùy chỉnh cho bot Telegram
 */
@Entity('telegram_commands')
@Unique(['telegramBotId', 'command'])
export class TelegramCommand {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của bot Telegram
   */
  @Column({ name: 'telegram_bot_id' })
  telegramBotId: number;

  /**
   * Tên lệnh (không bao gồm /)
   */
  @Column({ name: 'command', length: 50 })
  command: string;

  /**
   * <PERSON><PERSON> tả lệnh
   */
  @Column({ name: 'description', length: 255 })
  description: string;

  /**
   * <PERSON><PERSON><PERSON> hồ<PERSON> khi lệnh được gọi
   */
  @Column({ name: 'response', type: 'text', nullable: true })
  response: string;

  /**
   * Hành động tùy chỉnh (JSON)
   */
  @Column({ name: 'action', type: 'jsonb', nullable: true })
  action: Record<string, any>;

  /**
   * Thứ tự hiển thị
   */
  @Column({ name: 'order', default: 0 })
  order: number;

  /**
   * Trạng thái hoạt động
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
