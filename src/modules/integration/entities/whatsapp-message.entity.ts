import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { WhatsAppAccount } from './whatsapp-account.entity';

/**
 * Entity đại diện cho bảng whatsapp_messages trong cơ sở dữ liệu
 * Lưu trữ lịch sử tin nhắn trao đổi qua WhatsApp
 */
@Entity('whatsapp_messages')
export class WhatsAppMessage {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID tài khoản WhatsApp
   */
  @Column({ name: 'whatsapp_account_id' })
  whatsappAccountId: number;

  /**
   * ID tin nhắn trên WhatsApp
   */
  @Column({ name: 'message_id', length: 255 })
  messageId: string;

  /**
   * Số điện thoại người nhận/gửi
   */
  @Column({ name: 'phone_number', length: 20 })
  phoneNumber: string;

  /**
   * <PERSON><PERSON><PERSON> ng<PERSON>ời nhận/gửi (nếu có)
   */
  @Column({ name: 'contact_name', length: 255, nullable: true })
  contactName: string;

  /**
   * Nội dung tin nhắn
   */
  @Column({ name: 'content', type: 'text', nullable: true })
  content: string | null;

  /**
   * Loại tin nhắn (text, image, document, template, etc.)
   */
  @Column({ name: 'message_type', length: 20 })
  messageType: string;

  /**
   * Metadata của tin nhắn (JSON)
   */
  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  /**
   * Hướng tin nhắn (incoming/outgoing)
   */
  @Column({ name: 'direction', length: 10 })
  direction: string;

  /**
   * Trạng thái gửi tin nhắn (sent, delivered, read, failed)
   */
  @Column({ name: 'status', length: 20, nullable: true })
  status: string;

  /**
   * Thời điểm gửi tin nhắn (Unix timestamp)
   */
  @Column({ name: 'sent_at', type: 'bigint' })
  sentAt: number;

  /**
   * Thời điểm cập nhật trạng thái (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;

  /**
   * Quan hệ với bảng whatsapp_accounts
   */
  @ManyToOne(() => WhatsAppAccount)
  @JoinColumn({ name: 'whatsapp_account_id' })
  whatsappAccount: WhatsAppAccount;
}
