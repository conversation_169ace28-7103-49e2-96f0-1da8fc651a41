import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { TelegramChat } from './telegram-chat.entity';

/**
 * Entity đại diện cho bảng telegram_messages trong cơ sở dữ liệu
 * Lưu trữ lịch sử tin nhắn trao đổi qua Telegram
 */
@Entity('telegram_messages')
@Unique(['telegramChatId', 'messageId'])
export class TelegramMessage {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của cuộc trò chuyện Telegram
   */
  @Column({ name: 'telegram_chat_id' })
  telegramChatId: number;

  /**
   * ID tin nhắn trên Telegram
   */
  @Column({ name: 'message_id', type: 'bigint' })
  messageId: number;

  /**
   * ID người dùng gửi tin nhắn trên Telegram
   */
  @Column({ name: 'sender_id', type: 'bigint', nullable: true })
  senderId: number;

  /**
   * Tên người gửi
   */
  @Column({ name: 'sender_name', length: 255, nullable: true })
  senderName: string;

  /**
   * Nội dung tin nhắn
   */
  @Column({ name: 'text', type: 'text', nullable: true })
  text: string;

  /**
   * Loại tin nhắn (text, photo, video, document, etc.)
   */
  @Column({ name: 'message_type', length: 20, default: 'text' })
  messageType: string;

  /**
   * Metadata của tin nhắn (JSON)
   */
  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  /**
   * Hướng tin nhắn (incoming/outgoing)
   */
  @Column({ name: 'direction', length: 10 })
  direction: string;

  /**
   * Thời điểm gửi tin nhắn (Unix timestamp)
   */
  @Column({ name: 'sent_at', type: 'bigint' })
  sentAt: number;
}
