import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng integration trong cơ sở dữ liệu
 * Quản lý thông tin các tích hợp của người dùng
 */
@Entity('integration')
export class Integration {
  /**
   * ID của tích hợp
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên của tích hợp
   */
  @Column({
    name: 'integration_name',
    length: 255,
    nullable: false,
    comment: 'Tên của tích hợp'
  })
  integrationName: string;

  /**
   * Lo<PERSON>i tích hợp
   */
  @Column({
    name: 'type',
    length: 255,
    nullable: false,
    comment: 'Loại tích hợp'
  })
  type: string;

  /**
   * ID của người dùng
   */
  @Column({
    name: 'user_id',
    nullable: false,
    comment: 'ID của người dùng'
  })
  userId: number;

  /**
   * Thông tin bổ sung của tích hợp
   */
  @Column({
    name: 'info',
    type: 'json',
    nullable: true,
    comment: 'Thông tin bổ sung của tích hợp'
  })
  info: Record<string, any> | null;
} 