import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { TelegramBot } from './telegram-bot.entity';

/**
 * Entity đại diện cho bảng telegram_chats trong cơ sở dữ liệu
 * Lưu trữ thông tin về các cuộc trò chuyện Telegram mà bot tham gia
 */
@Entity('telegram_chats')
@Unique(['telegramBotId', 'chatId'])
export class TelegramChat {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của bot Telegram
   */
  @Column({ name: 'telegram_bot_id' })
  telegramBotId: number;

  /**
   * ID của cuộc trò chuyện trên Telegram
   */
  @Column({ name: 'chat_id', type: 'bigint' })
  chatId: number;

  /**
   * <PERSON><PERSON><PERSON> cu<PERSON> trò chuy<PERSON> (private, group, supergroup, channel)
   */
  @Column({ name: 'chat_type', length: 20 })
  chatType: string;

  /**
   * Tên của cuộc trò chuyện
   */
  @Column({ name: 'chat_title', length: 255, nullable: true })
  chatTitle: string;

  /**
   * Username của người dùng hoặc nhóm (nếu có)
   */
  @Column({ name: 'username', length: 255, nullable: true })
  username: string;

  /**
   * Tên đầy đủ của người dùng (nếu là chat private)
   */
  @Column({ name: 'full_name', length: 255, nullable: true })
  fullName: string;

  /**
   * Trạng thái hoạt động của cuộc trò chuyện
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
