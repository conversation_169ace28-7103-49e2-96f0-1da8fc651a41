import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpClientService } from '../http-client.service';
import { TestShipmentProviderResponseDto } from '../../user/dto/shipment';
import {
  getProviderBaseUrl,
  getProviderTimeout,
  PROVIDER_TEST_ENDPOINTS,
  PROVIDER_TEST_DATA
} from '../../constants/provider-config';
import { AhamoveProviderConfig } from '../../interfaces';

/**
 * Service validation cho Ahamove
 */
@Injectable()
export class AhamoveValidationService {
  private readonly logger = new Logger(AhamoveValidationService.name);
  private readonly baseUrl: string;
  private readonly timeout: number;

  constructor(
    private readonly httpClient: HttpClientService,
    private readonly configService: ConfigService,
  ) {
    // Lấy base URL từ env hoặc dùng default
    const envUrl = this.configService.get<string>('AHAMOVE_BASE_URL');
    const isProduction = this.configService.get<string>('NODE_ENV') === 'production';

    this.baseUrl = envUrl || getProviderBaseUrl('AHAMOVE', isProduction);
    this.timeout = getProviderTimeout('AHAMOVE');

    this.logger.log(`Ahamove Validation Service initialized with URL: ${this.baseUrl}`);
  }

  /**
   * Test connection với Ahamove API
   */
  async testConnection(config: AhamoveProviderConfig): Promise<TestShipmentProviderResponseDto> {
    try {
      this.logger.debug('Testing Ahamove connection', { mobile: config.mobile });

      // Validate input
      if (!config.apiKey || !config.mobile) {
        return {
          success: false,
          message: 'API Key và số điện thoại là bắt buộc cho Ahamove',
        };
      }

      // Nếu đã có token, test bằng API cities
      if (config.token) {
        const citiesResponse = await this.testWithToken(config.token);
        if (citiesResponse.success) {
          return {
            success: true,
            message: 'Kết nối Ahamove thành công với token hiện tại',
            data: citiesResponse.data,
          };
        }
      }

      // Nếu chưa có token hoặc token không hợp lệ, thử authenticate
      const authResponse = await this.authenticate(config);

      if (!authResponse.success) {
        return authResponse;
      }

      // Test với token mới
      const newToken = authResponse.data?.token;
      if (newToken) {
        const citiesResponse = await this.testWithToken(newToken);
        if (citiesResponse.success) {
          return {
            success: true,
            message: 'Kết nối Ahamove thành công. Token mới đã được tạo.',
            data: {
              ...citiesResponse.data,
              token: newToken,
              refreshToken: authResponse.data?.refresh_token,
            },
          };
        }
      }

      return {
        success: false,
        message: 'Xác thực thành công nhưng không thể truy cập dịch vụ Ahamove',
      };

    } catch (error) {
      this.logger.error('Ahamove connection test failed', error);

      return {
        success: false,
        message: `Lỗi kết nối Ahamove: ${error.message}`,
      };
    }
  }

  /**
   * Authenticate với Ahamove để lấy token
   */
  private async authenticate(config: AhamoveProviderConfig): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.AHAMOVE.AUTHENTICATE}`;

      const requestData = {
        api_key: config.apiKey,
        mobile: config.mobile,
      };

      const response = await this.httpClient.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: this.timeout,
      });

      // Kiểm tra response structure theo Ahamove API format
      if (response.status === 200 && response.data?.token) {
        return {
          success: true,
          message: 'Authentication successful',
          data: {
            token: response.data.token,
            refresh_token: response.data.refresh_token,
          },
        };
      }

      return {
        success: false,
        message: 'Phản hồi xác thực không hợp lệ từ Ahamove',
      };

    } catch (error) {
      this.logger.error('Error authenticating with Ahamove', error);

      // Handle specific error codes
      const status = error.response?.status;
      const errorData = error.response?.data;

      if (status === 404) {
        return {
          success: false,
          message: 'API Key hoặc số điện thoại không tồn tại trong hệ thống Ahamove',
        };
      }

      if (status === 401) {
        return {
          success: false,
          message: 'API Key hoặc số điện thoại không hợp lệ',
        };
      }

      if (status === 406) {
        return {
          success: false,
          message: 'Tài khoản Ahamove đã bị tạm ngưng',
        };
      }

      if (status >= 500) {
        return {
          success: false,
          message: 'Dịch vụ Ahamove đang bảo trì, vui lòng thử lại sau',
        };
      }

      return {
        success: false,
        message: `Lỗi xác thực Ahamove: ${errorData?.message || error.message}`,
      };
    }
  }

  /**
   * Test với token bằng cách gọi API cities
   */
  private async testWithToken(token: string): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.AHAMOVE.GET_CITIES}`;
      const countryId = PROVIDER_TEST_DATA.AHAMOVE.TEST_COUNTRY_ID;

      const response = await this.httpClient.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        params: {
          country_id: countryId,
        },
        timeout: this.timeout,
      });

      // Kiểm tra response structure
      if (response.status === 200 && Array.isArray(response.data) && response.data.length > 0) {
        const cities = response.data;

        return {
          success: true,
          message: 'Token valid',
          data: {
            cities: cities.slice(0, 3), // Chỉ trả về 3 thành phố đầu
            totalCities: cities.length,
          },
        };
      }

      return {
        success: false,
        message: 'Token không hợp lệ hoặc không có quyền truy cập',
      };

    } catch (error) {
      this.logger.error('Error testing Ahamove token', error);

      if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Token đã hết hạn hoặc không hợp lệ',
        };
      }

      return {
        success: false,
        message: `Lỗi kiểm tra token: ${error.message}`,
      };
    }
  }
}
