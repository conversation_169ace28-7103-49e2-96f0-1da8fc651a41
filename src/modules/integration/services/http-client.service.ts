import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * HTTP Client Service cho việc gọi API external
 * Cung cấp wrapper cho axios với logging và error handling
 */
@Injectable()
export class HttpClientService {
  private readonly logger = new Logger(HttpClientService.name);
  
  constructor(private readonly httpService: HttpService) {}

  /**
   * Thực hiện HTTP request với error handling và logging
   */
  async makeRequest<T = any>(config: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    data?: any;
    params?: Record<string, any>;
    timeout?: number;
  }): Promise<AxiosResponse<T>> {
    const requestConfig: AxiosRequestConfig = {
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data,
      params: config.params,
      timeout: config.timeout || 10000, // 10s timeout mặc định
      validateStatus: (status) => status < 500, // Chấp nhận 4xx errors
    };

    try {
      this.logger.debug(`Making ${config.method} request to ${config.url}`);
      
      const response = await this.httpService.axiosRef.request<T>(requestConfig);
      
      this.logger.debug(`Request successful: ${response.status} ${response.statusText}`);
      
      return response;
    } catch (error) {
      // Log error details for debugging
      this.logger.error('HTTP request failed:', {
        url: config.url,
        method: config.method,
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        // Không log response data để tránh expose sensitive info
      });
      
      throw error;
    }
  }

  /**
   * GET request wrapper
   */
  async get<T = any>(
    url: string, 
    options?: {
      headers?: Record<string, string>;
      params?: Record<string, any>;
      timeout?: number;
    }
  ): Promise<AxiosResponse<T>> {
    return this.makeRequest<T>({
      url,
      method: 'GET',
      ...options,
    });
  }

  /**
   * POST request wrapper
   */
  async post<T = any>(
    url: string,
    data?: any,
    options?: {
      headers?: Record<string, string>;
      timeout?: number;
    }
  ): Promise<AxiosResponse<T>> {
    return this.makeRequest<T>({
      url,
      method: 'POST',
      data,
      ...options,
    });
  }

  /**
   * POST request với form data (cho J&T)
   */
  async postForm<T = any>(
    url: string,
    formData: URLSearchParams,
    options?: {
      headers?: Record<string, string>;
      timeout?: number;
    }
  ): Promise<AxiosResponse<T>> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...options?.headers,
    };

    return this.makeRequest<T>({
      url,
      method: 'POST',
      data: formData,
      headers,
      timeout: options?.timeout,
    });
  }
}
