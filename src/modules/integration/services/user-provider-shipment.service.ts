import { Injectable, Logger } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import * as crypto from 'crypto';
import axios from 'axios';
import { UserProviderShipmentRepository } from '@modules/integration/repositories';
import { UserProviderShipment } from '@modules/integration/entities';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import {
  CreateUserProviderShipmentDto,
  GHNConfigResponseDto,
  GHTKConfigResponseDto,
  UpdateUserProviderShipmentDto,
  UserProviderShipmentResponseDto,
} from '../dto/user-provider-shipment';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response';
import { GHN_BASE_URLS, GHTK_BASE_URLS } from '@modules/business/constants';
import { ConfigService, ConfigType, ShipmentConfig } from '@config';

/**
 * Service xử lý cấu hình provider shipment của user
 */
@Injectable()
export class UserProviderShipmentService {
  private readonly logger = new Logger(UserProviderShipmentService.name);
  private readonly encryptionKey: string;

  constructor(
    private readonly repository: UserProviderShipmentRepository,
    private readonly configService: ConfigService
  ) {
    // Không cần ENCRYPTION_KEY chung nữa, sử dụng key riêng cho từng provider
    this.encryptionKey = 'fallback-encryption-key-32-chars!!';
  }

  /**
   * Lấy encryption key theo provider type
   */
  private getEncryptionKey(type: ProviderShipmentType): string {
    switch (type) {
      case ProviderShipmentType.GHTK:
        return this.configService.getConfig<ShipmentConfig>(ConfigType.Shipment).ghtk || this.encryptionKey;
      case ProviderShipmentType.GHN:
        return this.configService.getConfig<ShipmentConfig>(ConfigType.Shipment).ghn || this.encryptionKey;
      default:
        return this.encryptionKey;
    }
  }

  /**
   * Mã hóa dữ liệu cấu hình với key riêng cho từng provider
   */
  private encrypt(text: string, type: ProviderShipmentType): string {
    try {
      const algorithm = 'aes-256-cbc';
      const encryptionKey = this.getEncryptionKey(type);
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);
      const iv = crypto.randomBytes(16);

      const cipher = crypto.createCipheriv(algorithm, key, iv);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Lỗi khi mã hóa dữ liệu:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi mã hóa dữ liệu');
    }
  }

  /**
   * Giải mã dữ liệu cấu hình với key riêng cho từng provider
   */
  private decrypt(encryptedText: string, type: ProviderShipmentType): string {
    try {
      const algorithm = 'aes-256-cbc';
      const encryptionKey = this.getEncryptionKey(type);
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);

      // Validate encrypted text format
      if (!encryptedText || typeof encryptedText !== 'string') {
        throw new Error('Invalid encrypted text format');
      }

      const textParts = encryptedText.split(':');
      if (textParts.length < 2) {
        throw new Error('Invalid encrypted text format - missing IV separator');
      }

      const ivHex = textParts.shift()!;
      const encryptedData = textParts.join(':');

      // Validate IV length (should be 32 hex characters = 16 bytes)
      if (ivHex.length !== 32) {
        throw new Error(`Invalid IV length: expected 32 hex characters, got ${ivHex.length}`);
      }

      const iv = Buffer.from(ivHex, 'hex');
      if (iv.length !== 16) {
        throw new Error(`Invalid IV buffer length: expected 16 bytes, got ${iv.length}`);
      }

      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Lỗi khi giải mã dữ liệu:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi giải mã dữ liệu');
    }
  }

  /**
   * Tạo cấu hình mới
   */
  async create(userId: number, dto: CreateUserProviderShipmentDto): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Tạo cấu hình ${dto.type} cho user ${userId}`);

      // Validate cấu hình theo type
      this.validateConfigByType(dto);

      // Kiểm tra user chỉ được có tối đa 1 cấu hình cho mỗi type (GHN hoặc GHTK)
      await this.validateMaxOneConfigPerType(userId, dto.type);

      // Tạo object cấu hình JSON để mã hóa
      let configJson: any = {};

      if (dto.type === ProviderShipmentType.GHTK && dto.ghtkConfig) {
        const isTestMode = dto.ghtkConfig.isTestMode ?? true;
        configJson = {
          token: dto.ghtkConfig.token,
          baseUrl: this.getBaseUrlByMode(dto.type, isTestMode),
          timeout: dto.ghtkConfig.timeout || 30000,
          isTestMode: isTestMode
        };
      }

      if (dto.type === ProviderShipmentType.GHN && dto.ghnConfig) {
        const isTestMode = dto.ghnConfig.isTestMode ?? true;
        configJson = {
          token: dto.ghnConfig.token,
          shopId: dto.ghnConfig.shopId,
          baseUrl: this.getBaseUrlByMode(dto.type, isTestMode),
          timeout: dto.ghnConfig.timeout || 30000,
          isTestMode: isTestMode
        };
      }

      // Test connection trước khi lưu
      await this.testConnectionBeforeCreate(dto.type, configJson);

      // Kiểm tra token đã tồn tại chưa
      await this.validateTokenNotExists(configJson.token, dto.type, userId);

      // Mã hóa cấu hình JSON
      const encryptedKey = this.encrypt(JSON.stringify(configJson), dto.type);

      // Tạo entity
      const newEntity = this.repository.create({
        userId,
        name: dto.name || `Cấu hình ${dto.type}`,
        type: dto.type,
        key: encryptedKey,
        receiverPaysShipping: dto.receiverPaysShipping ?? false // Mặc định false (người gửi trả)
      });

      const entity = await this.repository.save(newEntity);

      return this.transformToResponse(entity);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi tạo cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách cấu hình với phân trang
   */
  async findByUserId(
    userId: number,
    page: number = 1,
    limit: number = 10,
    type?: ProviderShipmentType
  ): Promise<PaginatedResult<UserProviderShipmentResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách cấu hình cho user ${userId}`);

      const result = await this.repository.findByUserIdWithPagination(userId, page, limit, type);

      return {
        items: result.items.map(item => this.transformToResponse(item)),
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình:`, error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi lấy danh sách cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy cấu hình theo ID
   */
  async findById(userId: number, id: string): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Lấy cấu hình ${id} cho user ${userId}`);

      const entity = await this.repository.findByIdAndUserId(id, userId);
      if (!entity) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình');
      }

      return this.transformToResponse(entity);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi lấy cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy cấu hình theo type và userId (trả về entity thô)
   */
  async findByTypeAndUserId(type: ProviderShipmentType, userId: number): Promise<UserProviderShipment[]> {
    try {
      this.logger.log(`Lấy cấu hình ${type} cho user ${userId}`);
      return await this.repository.findByTypeAndUserId(type, userId);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình theo type:`, error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi lấy cấu hình: ${error.message}`);
    }
  }

  /**
   * Cập nhật cấu hình
   */
  async update(
    userId: number,
    id: string,
    dto: UpdateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Cập nhật cấu hình ${id} cho user ${userId}`);

      const entity = await this.repository.findByIdAndUserId(id, userId);
      if (!entity) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình');
      }

      // Kiểm tra xem có cần cập nhật cấu hình không
      const hasConfigUpdate = (entity.type === ProviderShipmentType.GHTK && dto.ghtkConfig) ||
                             (entity.type === ProviderShipmentType.GHN && dto.ghnConfig);

      let newEncryptedKey = entity.key; // Giữ nguyên key cũ nếu không có cập nhật config

      if (hasConfigUpdate) {
        // Chỉ giải mã khi thực sự cần cập nhật cấu hình
        let currentConfig: any;
        try {
          currentConfig = JSON.parse(this.decrypt(entity.key, entity.type));
        } catch (decryptError) {
          this.logger.warn(`Không thể giải mã cấu hình ${entity.id} để cập nhật:`, decryptError.message);
          try {
            currentConfig = this.tryFallbackDecryption(entity.key, entity.type);
          } catch (fallbackError) {
            this.logger.error(`Tất cả phương thức giải mã đều thất bại khi cập nhật:`, fallbackError.message);
            // Tạo cấu hình mới thay vì throw error
            currentConfig = this.createNewConfigFromDto(entity.type, dto);
            this.logger.warn(`Tạo cấu hình mới do không thể giải mã cấu hình cũ cho ${entity.id}`);
          }
        }

        // Cập nhật cấu hình
        if (dto.ghtkConfig && entity.type === ProviderShipmentType.GHTK) {
          const newIsTestMode = dto.ghtkConfig.isTestMode ?? currentConfig.isTestMode ?? true;
          Object.assign(currentConfig, {
            token: dto.ghtkConfig.token || currentConfig.token,
            baseUrl: this.getBaseUrlByMode(entity.type, newIsTestMode),
            timeout: dto.ghtkConfig.timeout ?? currentConfig.timeout ?? 30000,
            isTestMode: newIsTestMode
          });
        }

        if (dto.ghnConfig && entity.type === ProviderShipmentType.GHN) {
          const newIsTestMode = dto.ghnConfig.isTestMode ?? currentConfig.isTestMode ?? true;
          Object.assign(currentConfig, {
            token: dto.ghnConfig.token || currentConfig.token,
            shopId: dto.ghnConfig.shopId || currentConfig.shopId,
            baseUrl: this.getBaseUrlByMode(entity.type, newIsTestMode),
            timeout: dto.ghnConfig.timeout ?? currentConfig.timeout ?? 30000,
            isTestMode: newIsTestMode
          });
        }

        // Test connection nếu có thay đổi token
        if ((dto.ghtkConfig?.token || dto.ghnConfig?.token)) {
          await this.testConnectionBeforeCreate(entity.type, currentConfig);
          await this.validateTokenNotExists(currentConfig.token, entity.type, userId, id);
        }

        // Mã hóa lại
        newEncryptedKey = this.encrypt(JSON.stringify(currentConfig), entity.type);
      }

      // Cập nhật entity
      const updateData: Partial<UserProviderShipment> = {};

      // Chỉ cập nhật key nếu có thay đổi
      if (newEncryptedKey !== entity.key) {
        updateData.key = newEncryptedKey;
      }

      if (dto.name) {
        updateData.name = dto.name;
      }

      if (dto.receiverPaysShipping !== undefined) {
        updateData.receiverPaysShipping = dto.receiverPaysShipping;
      }

      // Chỉ save nếu có thay đổi
      if (Object.keys(updateData).length > 0) {
        Object.assign(entity, updateData);
        await this.repository.save(entity);
      }

      // Lấy entity đã cập nhật
      const updatedEntity = await this.repository.findByIdAndUserId(id, userId);
      return this.transformToResponse(updatedEntity!);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi cập nhật cấu hình: ${error.message}`);
    }
  }

  /**
   * Xóa cấu hình
   */
  async delete(userId: number, id: string): Promise<void> {
    try {
      this.logger.log(`Xóa cấu hình ${id} cho user ${userId}`);

      const exists = await this.repository.findByIdAndUserId(id, userId);
      if (!exists) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình');
      }

      const deleted = await this.repository.deleteByIdAndUserId(id, userId);
      if (!deleted) {
        throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xóa cấu hình');
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi xóa cấu hình: ${error.message}`);
    }
  }

  /**
   * Validate cấu hình theo type
   */
  private validateConfigByType(dto: CreateUserProviderShipmentDto): void {
    if (dto.type === ProviderShipmentType.GHTK && !dto.ghtkConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Cấu hình GHTK là bắt buộc khi type = GHTK');
    }

    if (dto.type === ProviderShipmentType.GHN && !dto.ghnConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Cấu hình GHN là bắt buộc khi type = GHN');
    }

    if (dto.type === ProviderShipmentType.GHTK && dto.ghnConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không thể có cấu hình GHN khi type = GHTK');
    }

    if (dto.type === ProviderShipmentType.GHN && dto.ghtkConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không thể có cấu hình GHTK khi type = GHN');
    }
  }

  /**
   * Validate user chỉ được có tối đa 1 cấu hình cho mỗi type
   */
  private async validateMaxOneConfigPerType(userId: number, type: ProviderShipmentType): Promise<void> {
    try {
      const existingConfigs = await this.repository.findByTypeAndUserId(type, userId);

      if (existingConfigs.length > 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Bạn đã có cấu hình cho nhà cung cấp ${type}. Mỗi user chỉ được có tối đa 1 cấu hình ${type}. Vui lòng cập nhật cấu hình hiện có hoặc xóa cấu hình cũ trước khi tạo mới.`
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi kiểm tra số lượng cấu hình:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi kiểm tra cấu hình hiện có');
    }
  }

  /**
   * Test connection trước khi tạo cấu hình
   */
  private async testConnectionBeforeCreate(type: ProviderShipmentType, configJson: any): Promise<void> {
    try {
      this.logger.log(`Testing connection for ${type} before creating config`);

      let testResult: any;

      if (type === ProviderShipmentType.GHTK) {
        // Test GHTK connection
        testResult = await this.testGHTKConnection(configJson);
      } else if (type === ProviderShipmentType.GHN) {
        // Test GHN connection
        testResult = await this.testGHNConnection(configJson);
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Loại nhà cung cấp ${type} chưa được hỗ trợ test connection`
        );
      }

      if (!testResult.success) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Test connection thất bại: ${testResult.message}. Vui lòng kiểm tra lại thông tin cấu hình.`
        );
      }

      this.logger.log(`Test connection successful for ${type}: ${testResult.message}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi test connection:', error);
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Không thể test connection: ${error.message}. Vui lòng kiểm tra lại thông tin cấu hình.`
      );
    }
  }

  /**
   * Validate token không bị trùng lặp
   */
  private async validateTokenNotExists(
    plainToken: string,
    type: ProviderShipmentType,
    currentUserId: number,
    excludeId?: string
  ): Promise<void> {
    try {
      // Lấy tất cả cấu hình của type này (trừ cấu hình hiện tại nếu đang update)
      const allConfigs = await this.repository.findByTypeAndUserId(type, currentUserId);

      // Lọc ra các cấu hình khác (nếu đang update)
      const configsToCheck = excludeId
        ? allConfigs.filter(config => config.id !== excludeId)
        : allConfigs;

      // Kiểm tra từng cấu hình xem có token trùng không
      for (const config of configsToCheck) {
        try {
          let existingConfig: any;
          try {
            existingConfig = JSON.parse(this.decrypt(config.key, config.type));
          } catch (decryptError) {
            // Thử fallback decryption
            try {
              existingConfig = this.tryFallbackDecryption(config.key, config.type);
            } catch (fallbackError) {
              this.logger.warn(`Không thể giải mã cấu hình ${config.id} để kiểm tra token trùng lặp`);
              continue; // Bỏ qua cấu hình này nếu không giải mã được
            }
          }

          // So sánh token
          if (existingConfig.token === plainToken) {
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              `Bạn đã có cấu hình "${config.name}" sử dụng token này cho nhà cung cấp ${type}. Vui lòng sử dụng token khác hoặc cập nhật cấu hình hiện có.`
            );
          }
        } catch (error) {
          if (error instanceof AppException) {
            throw error;
          }
          this.logger.warn(`Lỗi khi kiểm tra token cho cấu hình ${config.id}:`, error.message);
        }
      }

      // Kiểm tra token có bị trùng với user khác không
      const allConfigsOfType = await this.repository.createBaseQuery()
        .where('shipment.type = :type', { type })
        .andWhere('shipment.userId != :currentUserId', { currentUserId })
        .getMany();

      for (const config of allConfigsOfType) {
        try {
          let existingConfig: any;
          try {
            existingConfig = JSON.parse(this.decrypt(config.key, config.type));
          } catch (decryptError) {
            // Thử fallback decryption
            try {
              existingConfig = this.tryFallbackDecryption(config.key, config.type);
            } catch (fallbackError) {
              continue; // Bỏ qua cấu hình này nếu không giải mã được
            }
          }

          // So sánh token
          if (existingConfig.token === plainToken) {
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              `Token này đã được sử dụng bởi user khác cho nhà cung cấp ${type}. Vui lòng sử dụng token khác.`
            );
          }
        } catch (error) {
          if (error instanceof AppException) {
            throw error;
          }
          this.logger.warn(`Lỗi khi kiểm tra token cho cấu hình ${config.id} của user khác:`, error.message);
        }
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi validate token:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi kiểm tra token trùng lặp');
    }
  }

  /**
   * Transform entity thành response DTO
   */
  private transformToResponse(entity: UserProviderShipment): UserProviderShipmentResponseDto {
    try {
      let config: any = {};
      let decryptionSuccessful = false;

      // Thử giải mã với phương thức hiện tại
      try {
        config = JSON.parse(this.decrypt(entity.key, entity.type));
        decryptionSuccessful = true;
        this.logger.debug(`Giải mã thành công cho cấu hình ${entity.id}`);
      } catch (decryptError) {
        this.logger.warn(`Không thể giải mã cấu hình ${entity.id} với phương thức hiện tại:`, decryptError.message);

        // Thử giải mã với phương thức fallback (có thể data được mã hóa bằng cách khác)
        try {
          config = this.tryFallbackDecryption(entity.key, entity.type);
          decryptionSuccessful = true;
          this.logger.debug(`Giải mã thành công với phương thức fallback cho cấu hình ${entity.id}`);
        } catch (fallbackError) {
          this.logger.error(`Tất cả phương thức giải mã đều thất bại cho cấu hình ${entity.id}:`, fallbackError.message);
          // Tạo config mặc định để tránh lỗi
          config = this.createDefaultConfig(entity.type);
        }
      }

      const response = plainToClass(UserProviderShipmentResponseDto, {
        id: entity.id,
        name: entity.name,
        type: entity.type,
        receiverPaysShipping: entity.receiverPaysShipping,
        createdAt: entity.createdAt
      });

      // Thêm cấu hình response (luôn mask token và shopId để bảo mật)
      if (entity.type === ProviderShipmentType.GHTK) {
        response.ghtkConfig = plainToClass(GHTKConfigResponseDto, {
          baseUrl: config.baseUrl || this.getBaseUrlByMode(entity.type, true),
          timeout: config.timeout || 30000,
          isTestMode: config.isTestMode ?? true,
          token: '***ENCRYPTED***', // Luôn mask token để bảo mật
          hasToken: decryptionSuccessful ? !!config.token : true
        });
      }

      if (entity.type === ProviderShipmentType.GHN) {
        response.ghnConfig = plainToClass(GHNConfigResponseDto, {
          baseUrl: config.baseUrl || this.getBaseUrlByMode(entity.type, true),
          timeout: config.timeout || 30000,
          isTestMode: config.isTestMode ?? true,
          token: '***ENCRYPTED***', // Luôn mask token để bảo mật
          shopId: '***ENCRYPTED***', // Luôn mask shopId để bảo mật
          hasToken: decryptionSuccessful ? !!config.token : true,
          hasShopId: decryptionSuccessful ? !!config.shopId : true
        });
      }

      return response;
    } catch (error) {
      this.logger.error('Lỗi khi transform response:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi xử lý dữ liệu cấu hình');
    }
  }

  /**
   * Thử giải mã với các phương thức fallback
   */
  private tryFallbackDecryption(encryptedText: string, type: ProviderShipmentType): any {
    const fallbackMethods = [
      // Method 1: Base64 format từ shared EncryptionService
      () => {
        if (!encryptedText.includes(':')) {
          const buffer = Buffer.from(encryptedText, 'base64');
          if (buffer.length < 16) {
            throw new Error('Buffer too short for IV');
          }

          const iv = buffer.slice(0, 16);
          const encrypted = buffer.slice(16);

          const algorithm = 'aes-256-cbc';
          const encryptionKey = this.getEncryptionKey(type);
          const key = crypto.createHash('sha256').update(String(encryptionKey)).digest();

          const decipher = crypto.createDecipheriv(algorithm, key, iv);
          const decryptedBuffer1 = decipher.update(encrypted);
          const decryptedBuffer2 = decipher.final();
          const decryptedText = Buffer.concat([decryptedBuffer1, decryptedBuffer2]).toString('utf8');

          return JSON.parse(decryptedText);
        }
        throw new Error('Not base64 format');
      },

      // Method 2: Fallback key với validation
      () => {
        const algorithm = 'aes-256-cbc';
        const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);

        const textParts = encryptedText.split(':');
        if (textParts.length < 2) {
          throw new Error('Invalid format for fallback method 2');
        }

        const ivHex = textParts.shift()!;
        if (ivHex.length !== 32) {
          throw new Error(`Invalid IV length in fallback: expected 32, got ${ivHex.length}`);
        }

        const iv = Buffer.from(ivHex, 'hex');
        const encryptedData = textParts.join(':');

        const decipher = crypto.createDecipheriv(algorithm, key, iv);
        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        return JSON.parse(decrypted);
      },
    ];

    // Thử từng phương thức fallback
    for (let i = 0; i < fallbackMethods.length; i++) {
      try {
        const result = fallbackMethods[i]();
        this.logger.debug(`Fallback decryption method ${i + 1} succeeded`);
        return result;
      } catch (error) {
        this.logger.debug(`Fallback decryption method ${i + 1} failed:`, error.message);
      }
    }

    throw new Error('Tất cả phương thức giải mã fallback đều thất bại');
  }

  /**
   * Tạo config mặc định khi không thể giải mã
   */
  private createDefaultConfig(type: ProviderShipmentType): any {
    const baseConfig = {
      baseUrl: this.getBaseUrlByMode(type, true),
      timeout: 30000,
      isTestMode: true
    };

    if (type === ProviderShipmentType.GHTK) {
      return {
        ...baseConfig,
        token: null
      };
    }

    if (type === ProviderShipmentType.GHN) {
      return {
        ...baseConfig,
        token: null,
        shopId: null
      };
    }

    return baseConfig;
  }

  /**
   * Tạo config mới từ DTO khi không thể giải mã config cũ
   */
  private createNewConfigFromDto(type: ProviderShipmentType, dto: UpdateUserProviderShipmentDto): any {
    if (type === ProviderShipmentType.GHTK && dto.ghtkConfig) {
      const isTestMode = dto.ghtkConfig.isTestMode ?? true;
      return {
        token: dto.ghtkConfig.token || null,
        baseUrl: this.getBaseUrlByMode(type, isTestMode),
        timeout: dto.ghtkConfig.timeout || 30000,
        isTestMode: isTestMode
      };
    }

    if (type === ProviderShipmentType.GHN && dto.ghnConfig) {
      const isTestMode = dto.ghnConfig.isTestMode ?? true;
      return {
        token: dto.ghnConfig.token || null,
        shopId: dto.ghnConfig.shopId || null,
        baseUrl: this.getBaseUrlByMode(type, isTestMode),
        timeout: dto.ghnConfig.timeout || 30000,
        isTestMode: isTestMode
      };
    }

    // Fallback to default config
    return this.createDefaultConfig(type);
  }

  /**
   * Lấy cấu hình đã giải mã cho internal use
   */
  async getDecryptedConfig(userId: number, type: ProviderShipmentType): Promise<any | null> {
    try {
      const entities = await this.repository.findByTypeAndUserId(type, userId);
      if (entities.length === 0) {
        return null;
      }

      // Lấy cấu hình đầu tiên (mới nhất)
      const entity = entities[0];

      try {
        const config = JSON.parse(this.decrypt(entity.key, entity.type));

        // Trả về config với format chuẩn cho business module
        return {
          ...config,
          type: entity.type,
          name: entity.name
        };
      } catch (decryptError) {
        this.logger.warn(`Không thể giải mã cấu hình ${entity.id} cho internal use:`, decryptError.message);

        try {
          const config = this.tryFallbackDecryption(entity.key, entity.type);
          return {
            ...config,
            type: entity.type,
            name: entity.name
          };
        } catch (fallbackError) {
          this.logger.error(`Tất cả phương thức giải mã đều thất bại cho internal use:`, fallbackError.message);
          return null;
        }
      }
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình giải mã:`, error);
      return null;
    }
  }

  /**
   * Test GHTK connection
   */
  private async testGHTKConnection(config: any): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${config.baseUrl}/services/shipment/fee`;

      // Test với một request đơn giản để validate token
      const testData = {
        pick_province: 'Hà Nội',
        pick_district: 'Quận Ba Đình',
        province: 'TP Hồ Chí Minh',
        district: 'Quận 1',
        weight: 1000,
        value: 100000
      };

      const response = await axios.post(url, testData, {
        headers: {
          'Token': config.token,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout || 30000
      });

      if (response.data && response.data.success !== false) {
        return {
          success: true,
          message: 'Kết nối GHTK thành công'
        };
      } else {
        return {
          success: false,
          message: response.data?.message || 'Token GHTK không hợp lệ'
        };
      }
    } catch (error) {
      this.logger.error('GHTK connection test failed:', error);
      return {
        success: false,
        message: `Lỗi kết nối GHTK: ${error.response?.data?.message || error.message}`
      };
    }
  }

  /**
   * Test GHN connection
   */
  private async testGHNConnection(config: any): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${config.baseUrl}/shiip/public-api/v2/shop/all`;

      const response = await axios.get(url, {
        headers: {
          'Token': config.token,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout || 30000
      });

      if (response.data && response.data.code === 200) {
        // Kiểm tra shopId có tồn tại không
        const shops = response.data.data?.shops || [];
        const shopExists = shops.some(shop => shop._id?.toString() === config.shopId);

        if (!shopExists) {
          return {
            success: false,
            message: `Shop ID ${config.shopId} không tồn tại trong tài khoản GHN của bạn`
          };
        }

        const shop = shops.find(s => s._id?.toString() === config.shopId);
        return {
          success: true,
          message: `Kết nối GHN thành công. Shop: ${shop?.name || config.shopId}`
        };
      } else {
        return {
          success: false,
          message: response.data?.message || 'Token GHN không hợp lệ'
        };
      }
    } catch (error) {
      this.logger.error('GHN connection test failed:', error);
      return {
        success: false,
        message: `Lỗi kết nối GHN: ${error.response?.data?.message || error.message}`
      };
    }
  }

  /**
   * Lấy BASE_URL dựa trên provider type và test mode
   */
  private getBaseUrlByMode(type: ProviderShipmentType, isTestMode: boolean): string {
    switch (type) {
      case ProviderShipmentType.GHTK:
        return isTestMode ? GHTK_BASE_URLS.TEST : GHTK_BASE_URLS.PRODUCTION;

      case ProviderShipmentType.GHN:
        return isTestMode ? GHN_BASE_URLS.TEST : GHN_BASE_URLS.PRODUCTION;

      default:
        throw new AppException(ErrorCode.VALIDATION_ERROR, `Unsupported provider type: ${type}`);
    }
  }
}
