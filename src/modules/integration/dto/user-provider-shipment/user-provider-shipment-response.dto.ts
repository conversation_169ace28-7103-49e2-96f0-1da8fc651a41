import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { ProviderShipmentType } from '../../constants/provider-shipment-type.enum';

/**
 * DTO cho response cấu hình GHTK (không bao gồm thông tin nhạy cảm)
 */
export class GHTKConfigResponseDto {
  @ApiProperty({
    description: 'GHTK Base URL',
    example: 'https://services-staging.ghtklab.com'
  })
  @Expose()
  baseUrl: string;

  @ApiProperty({
    description: 'GHTK Timeout (milliseconds)',
    example: 30000
  })
  @Expose()
  timeout: number;

  @ApiProperty({
    description: 'GHTK Environment Mode (true = test/staging, false = production)',
    example: true
  })
  @Expose()
  isTestMode: boolean;

  @ApiProperty({
    description: 'GHTK API Token (đã giải mã)',
    example: '8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou'
  })
  @Expose()
  token: string;

  @ApiProperty({
    description: 'Token có được cấu hình hay không',
    example: true
  })
  @Expose()
  hasToken: boolean;
}

/**
 * DTO cho response cấu hình GHN (không bao gồm thông tin nhạy cảm)
 */
export class GHNConfigResponseDto {
  @ApiProperty({
    description: 'GHN Base URL',
    example: 'https://dev-online-gateway.ghn.vn'
  })
  @Expose()
  baseUrl: string;

  @ApiProperty({
    description: 'GHN Timeout (milliseconds)',
    example: 30000
  })
  @Expose()
  timeout: number;

  @ApiProperty({
    description: 'GHN Environment Mode (true = test/dev, false = production)',
    example: true
  })
  @Expose()
  isTestMode: boolean;

  @ApiProperty({
    description: 'GHN API Token (đã giải mã)',
    example: '42d0fc57-402d-11f0-9b81-222185cb68c8'
  })
  @Expose()
  token: string;

  @ApiProperty({
    description: 'GHN Shop ID (đã giải mã)',
    example: '196768'
  })
  @Expose()
  shopId: string;

  @ApiProperty({
    description: 'Token có được cấu hình hay không',
    example: true
  })
  @Expose()
  hasToken: boolean;

  @ApiProperty({
    description: 'Shop ID có được cấu hình hay không',
    example: true
  })
  @Expose()
  hasShopId: boolean;
}

/**
 * DTO cho response cấu hình provider shipment
 */
export class UserProviderShipmentResponseDto {
  @ApiProperty({
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của cấu hình',
    example: 'Cấu hình GHTK chính'
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Loại nhà cung cấp vận chuyển',
    enum: ProviderShipmentType,
    example: ProviderShipmentType.GHTK
  })
  @Expose()
  type: ProviderShipmentType;

  @ApiProperty({
    description: 'Cấu hình GHTK (nếu type = GHTK)',
    type: GHTKConfigResponseDto,
    required: false
  })
  @Expose()
  ghtkConfig?: GHTKConfigResponseDto;

  @ApiProperty({
    description: 'Cấu hình GHN (nếu type = GHN)',
    type: GHNConfigResponseDto,
    required: false
  })
  @Expose()
  ghnConfig?: GHNConfigResponseDto;

  @ApiProperty({
    description: 'Người nhận có trả phí vận chuyển không',
    type: 'boolean',
    example: false
  })
  @Expose()
  receiverPaysShipping: boolean;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995200000
  })
  @Expose()
  @Transform(({ value }) => Number(value))
  createdAt: number;
}
