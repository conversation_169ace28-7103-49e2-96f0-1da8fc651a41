import { DataMaskingUtil } from './data-masking.util';

describe('DataMaskingUtil', () => {
  describe('maskSensitiveData', () => {
    it('should mask data with length > 8 characters correctly', () => {
      const input = 'abcdefghijklmnop'; // 16 characters
      const result = DataMaskingUtil.maskSensitiveData(input);
      expect(result).toBe('abcd********mnop');
    });

    it('should mask data with length = 8 characters correctly', () => {
      const input = 'abcdefgh'; // 8 characters
      const result = DataMaskingUtil.maskSensitiveData(input);
      expect(result).toBe('a******h');
    });

    it('should mask data with length < 8 characters correctly', () => {
      const input = 'abcde'; // 5 characters
      const result = DataMaskingUtil.maskSensitiveData(input);
      expect(result).toBe('a***e');
    });

    it('should mask data with length = 2 characters correctly', () => {
      const input = 'ab'; // 2 characters
      const result = DataMaskingUtil.maskSensitiveData(input);
      expect(result).toBe('**');
    });

    it('should mask data with length = 1 character correctly', () => {
      const input = 'a'; // 1 character
      const result = DataMaskingUtil.maskSensitiveData(input);
      expect(result).toBe('*');
    });

    it('should handle empty string', () => {
      const result = DataMaskingUtil.maskSensitiveData('');
      expect(result).toBe('');
    });

    it('should handle null/undefined input', () => {
      expect(DataMaskingUtil.maskSensitiveData(null as any)).toBe('');
      expect(DataMaskingUtil.maskSensitiveData(undefined as any)).toBe('');
    });

    it('should use custom mask character', () => {
      const input = 'abcdefghijklmnop';
      const result = DataMaskingUtil.maskSensitiveData(input, '#');
      expect(result).toBe('abcd########mnop');
    });
  });

  describe('maskApiKey', () => {
    it('should mask API key correctly', () => {
      const apiKey = 'sk_test_1234567890abcdef';
      const result = DataMaskingUtil.maskApiKey(apiKey);
      expect(result).toBe('sk_t**********cdef');
    });
  });

  describe('maskPhoneNumber', () => {
    it('should mask phone number with 10 digits', () => {
      const phone = '0123456789';
      const result = DataMaskingUtil.maskPhoneNumber(phone);
      expect(result).toBe('012***6789');
    });

    it('should mask phone number with formatting', () => {
      const phone = '+84-123-456-789';
      const result = DataMaskingUtil.maskPhoneNumber(phone);
      expect(result).toBe('841*****789');
    });

    it('should handle short phone numbers', () => {
      const phone = '123';
      const result = DataMaskingUtil.maskPhoneNumber(phone);
      expect(result).toBe('***');
    });
  });

  describe('maskEmail', () => {
    it('should mask email correctly', () => {
      const email = '<EMAIL>';
      const result = DataMaskingUtil.maskEmail(email);
      expect(result).toBe('te****<EMAIL>');
    });

    it('should handle short email local part', () => {
      const email = '<EMAIL>';
      const result = DataMaskingUtil.maskEmail(email);
      expect(result).toBe('<EMAIL>');
    });

    it('should handle very short email local part', () => {
      const email = '<EMAIL>';
      const result = DataMaskingUtil.maskEmail(email);
      expect(result).toBe('<EMAIL>');
    });

    it('should handle invalid email', () => {
      const result = DataMaskingUtil.maskEmail('invalid-email');
      expect(result).toBe('');
    });
  });

  describe('maskObject', () => {
    it('should mask specified sensitive fields', () => {
      const obj = {
        name: 'Test User',
        apiKey: 'sk_test_1234567890',
        shopId: 'shop_123456789',
        email: '<EMAIL>'
      };

      const result = DataMaskingUtil.maskObject(obj, ['apiKey', 'shopId']);

      expect(result.name).toBe('Test User');
      expect(result.apiKey).toBe('sk_t******90');
      expect(result.shopId).toBe('shop******89');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should handle non-object input', () => {
      const result = DataMaskingUtil.maskObject(null as any, ['field']);
      expect(result).toBeNull();
    });
  });

  describe('maskArray', () => {
    it('should mask array of objects', () => {
      const arr = [
        { name: 'User 1', token: 'token123456789' },
        { name: 'User 2', token: 'token987654321' }
      ];

      const result = DataMaskingUtil.maskArray(arr, ['token']);

      expect(result[0].name).toBe('User 1');
      expect(result[0].token).toBe('toke******89');
      expect(result[1].name).toBe('User 2');
      expect(result[1].token).toBe('toke******21');
    });

    it('should handle non-array input', () => {
      const result = DataMaskingUtil.maskArray(null as any, ['field']);
      expect(result).toBeNull();
    });
  });

  describe('isSensitiveField', () => {
    it('should identify sensitive fields', () => {
      expect(DataMaskingUtil.isSensitiveField('apiKey')).toBe(true);
      expect(DataMaskingUtil.isSensitiveField('API_KEY')).toBe(true);
      expect(DataMaskingUtil.isSensitiveField('shop_id')).toBe(true);
      expect(DataMaskingUtil.isSensitiveField('access_token')).toBe(true);
      expect(DataMaskingUtil.isSensitiveField('password')).toBe(true);
      expect(DataMaskingUtil.isSensitiveField('secret')).toBe(true);
    });

    it('should not identify non-sensitive fields', () => {
      expect(DataMaskingUtil.isSensitiveField('name')).toBe(false);
      expect(DataMaskingUtil.isSensitiveField('email')).toBe(false);
      expect(DataMaskingUtil.isSensitiveField('id')).toBe(false);
      expect(DataMaskingUtil.isSensitiveField('status')).toBe(false);
    });
  });

  describe('autoMaskObject', () => {
    it('should automatically mask sensitive fields', () => {
      const obj = {
        name: 'Test User',
        api_key: 'sk_test_1234567890',
        shop_id: 'shop_123456789',
        email: '<EMAIL>',
        token: 'access_token_123'
      };

      const result = DataMaskingUtil.autoMaskObject(obj);

      expect(result.name).toBe('Test User');
      expect(result.api_key).toBe('sk_t******90');
      expect(result.shop_id).toBe('shop******89');
      expect(result.email).toBe('<EMAIL>');
      expect(result.token).toBe('acce*******23');
    });
  });
});
