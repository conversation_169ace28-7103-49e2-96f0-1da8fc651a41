import { Body, Controller, Delete, Get, HttpStatus, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { PaymentGatewayUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import {
  AccountLookupDto,
  BankAccountResponseDto,
  BankListResponseDto,
  CompanyResponseDto,
  ConfirmDeleteBankAccountDto,
  CreateBankAccountDto,
  CreateBankAccountIndividualDto,
  CreateCompanyDto,
  EligibleVAAccountResponseDto,
  OtpConfirmDto,
  RequestVAConnectionDto
} from '../dto';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@Controller('integration/payment')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class PaymentGatewayUserController {
  constructor(
    private readonly paymentGatewayUserService: PaymentGatewayUserService,
  ) {}

  /**
   * Lấy danh sách ngân hàng
   */
  @Get('banks')
  @ApiOperation({ summary: 'Lấy danh sách ngân hàng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách ngân hàng thành công',
  })
  async getBanks(): Promise<ApiResponseDto<BankListResponseDto[]>> {
    const banks = await this.paymentGatewayUserService.getBanks();
    return ApiResponseDto.success(banks, 'Lấy danh sách ngân hàng thành công');
  }

  /**
   * Tra cứu thông tin tài khoản ngân hàng
   */
  @Post('banks/lookup')
  @ApiOperation({ summary: 'Tra cứu thông tin tài khoản ngân hàng' })
  @ApiBody({ type: AccountLookupDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tra cứu thông tin tài khoản ngân hàng thành công',
  })
  async lookupAccountHolder(
    @Body() accountLookupDto: AccountLookupDto
  ): Promise<ApiResponseDto<{ accountHolderName: string }>> {
    const result = await this.paymentGatewayUserService.lookupAccountHolder(accountLookupDto);
    return ApiResponseDto.success(result, 'Tra cứu thông tin tài khoản ngân hàng thành công');
  }

  /**
   * Tạo công ty mới
   */
  @Post('company')
  @ApiOperation({ summary: 'Tạo công ty mới' })
  @ApiBody({ type: CreateCompanyDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo công ty mới thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ hoặc người dùng đã có công ty',
  })
  async createCompany(
    @Body() createCompanyDto: CreateCompanyDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<CompanyResponseDto>> {
    const company = await this.paymentGatewayUserService.createCompany(user.id, createCompanyDto);
    return ApiResponseDto.success(company, 'Tạo công ty mới thành công');
  }

  /**
   * Lấy thông tin công ty của người dùng
   */
  @Get('company')
  @ApiOperation({ summary: 'Lấy thông tin công ty của người dùng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin công ty thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Người dùng chưa có công ty',
  })
  async getCompany(
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<CompanyResponseDto>> {
    const company = await this.paymentGatewayUserService.getCompany(user.id);
    return ApiResponseDto.success(company, 'Lấy thông tin công ty thành công');
  }

  /**
   * Tạo tài khoản ngân hàng mới
   */
  @Post('bank-accounts')
  @ApiOperation({ summary: 'Tạo tài khoản ngân hàng mới' })
  @ApiBody({ type: CreateBankAccountDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo tài khoản ngân hàng mới thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Người dùng chưa có công ty',
  })
  async createBankAccount(
    @Body() createBankAccountDto: CreateBankAccountDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ bankAccount: BankAccountResponseDto; requestId?: string }>> {
    const result = await this.paymentGatewayUserService.createBankAccount(user.id, createBankAccountDto);
    return ApiResponseDto.success(result, 'Tạo tài khoản ngân hàng mới thành công');
  }

  /**
   * Tạo tài khoản ngân hàng cá nhân
   */
  @Post('bank-accounts/individual')
  @ApiOperation({ summary: 'Tạo tài khoản ngân hàng cá nhân' })
  @ApiBody({ type: CreateBankAccountIndividualDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo tài khoản ngân hàng cá nhân thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Người dùng chưa có công ty',
  })
  async createBankAccountIndividual(
    @Body() createBankAccountDto: CreateBankAccountIndividualDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ bankAccount: BankAccountResponseDto; requestId?: string }>> {
    const result = await this.paymentGatewayUserService.createBankAccount(user.id, createBankAccountDto);
    return ApiResponseDto.success(result, 'Tạo tài khoản ngân hàng cá nhân thành công');
  }

  /**
   * Xác nhận kết nối API ngân hàng
   */
  @Post('bank-accounts/:bankAccountId/confirm/:requestId')
  @ApiOperation({ summary: 'Xác nhận kết nối API ngân hàng' })
  @ApiParam({ name: 'bankAccountId', description: 'ID của tài khoản ngân hàng' })
  @ApiParam({ name: 'requestId', description: 'ID của yêu cầu' })
  @ApiBody({ type: OtpConfirmDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xác nhận kết nối API ngân hàng thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài khoản ngân hàng',
  })
  async confirmBankAccountConnection(
    @Param('bankAccountId') bankAccountId: string,
    @Param('requestId') requestId: string,
    @Body() otpConfirmDto: OtpConfirmDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ success: boolean; message: string }>> {
    const result = await this.paymentGatewayUserService.confirmBankAccountConnection(
      user.id,
      bankAccountId,
      requestId,
      otpConfirmDto
    );
    return ApiResponseDto.success(result, 'Xác nhận kết nối API ngân hàng thành công');
  }

  /**
   * Lấy danh sách tài khoản ngân hàng của người dùng
   */
  @Get('bank-accounts')
  @ApiOperation({ summary: 'Lấy danh sách tài khoản ngân hàng của người dùng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách tài khoản ngân hàng thành công',
  })
  async getBankAccounts(
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<BankAccountResponseDto[]>> {
    const bankAccounts = await this.paymentGatewayUserService.getBankAccounts(user.id);
    return ApiResponseDto.success(bankAccounts, 'Lấy danh sách tài khoản ngân hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết tài khoản ngân hàng
   */
  @Get('bank-accounts/:bankAccountId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tài khoản ngân hàng' })
  @ApiParam({ name: 'bankAccountId', description: 'ID của tài khoản ngân hàng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết tài khoản ngân hàng thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài khoản ngân hàng',
  })
  async getBankAccountDetails(
    @Param('bankAccountId') bankAccountId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<BankAccountResponseDto>> {
    const bankAccount = await this.paymentGatewayUserService.getBankAccountDetails(user.id, bankAccountId);
    return ApiResponseDto.success(bankAccount, 'Lấy thông tin chi tiết tài khoản ngân hàng thành công');
  }

  /**
   * Yêu cầu kết nối API ngân hàng
   */
  @Put('bank-accounts/:bankAccountId/request-connection')
  @ApiOperation({ summary: 'Yêu cầu kết nối API ngân hàng' })
  @ApiParam({ name: 'bankAccountId', description: 'ID của tài khoản ngân hàng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Yêu cầu kết nối API ngân hàng thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài khoản ngân hàng',
  })
  async requestBankAccountConnection(
    @Param('bankAccountId') bankAccountId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ requestId: string; message: string }>> {
    const result = await this.paymentGatewayUserService.requestBankAccountConnection(user.id, bankAccountId);
    return ApiResponseDto.success(result, 'Yêu cầu kết nối API ngân hàng thành công');
  }

  /**
   * Yêu cầu xóa tài khoản ngân hàng
   */
  @Delete('bank-accounts/:bankAccountId/request-delete')
  @ApiOperation({ summary: 'Yêu cầu xóa tài khoản ngân hàng' })
  @ApiParam({ name: 'bankAccountId', description: 'ID của tài khoản ngân hàng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Yêu cầu xóa tài khoản ngân hàng thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài khoản ngân hàng',
  })
  async requestDeleteBankAccount(
    @Param('bankAccountId') bankAccountId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ message: string; requestId?: string }>> {
    const result = await this.paymentGatewayUserService.requestDeleteBankAccount(user.id, bankAccountId);
    return ApiResponseDto.success(result, 'Yêu cầu xóa tài khoản ngân hàng thành công');
  }

  /**
   * Xác nhận xóa tài khoản ngân hàng
   */
  @Post('bank-accounts/:bankAccountId/confirm-delete')
  @ApiOperation({ summary: 'Xác nhận xóa tài khoản ngân hàng' })
  @ApiParam({ name: 'bankAccountId', description: 'ID của tài khoản ngân hàng' })
  @ApiBody({ type: ConfirmDeleteBankAccountDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xác nhận xóa tài khoản ngân hàng thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài khoản ngân hàng',
  })
  async confirmDeleteBankAccount(
    @Param('bankAccountId') bankAccountId: string,
    @Body() confirmDeleteDto: ConfirmDeleteBankAccountDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ message: string }>> {
    const result = await this.paymentGatewayUserService.confirmDeleteBankAccount(
      user.id,
      bankAccountId,
      confirmDeleteDto
    );
    return ApiResponseDto.success(result, 'Xác nhận xóa tài khoản ngân hàng thành công');
  }

  /**
   * Yêu cầu liên kết tài khoản VA
   */
  @Post('bank-accounts/:bankAccountId/request-va-connection')
  @ApiOperation({ summary: 'Yêu cầu liên kết tài khoản VA' })
  @ApiParam({ name: 'bankAccountId', description: 'ID của tài khoản ngân hàng' })
  @ApiBody({ type: RequestVAConnectionDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Yêu cầu liên kết tài khoản VA thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài khoản ngân hàng',
  })
  async requestVAConnection(
    @Param('bankAccountId') bankAccountId: string,
    @Body() requestVADto: RequestVAConnectionDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ message: string; requestId?: string }>> {
    const result = await this.paymentGatewayUserService.requestVAConnection(
      user.id,
      bankAccountId,
      requestVADto.email
    );
    return ApiResponseDto.success(result, 'Yêu cầu liên kết tài khoản VA thành công');
  }

  /**
   * Lấy danh sách tài khoản đủ điều kiện tạo tài khoản VA
   */
  @Get('va-accounts/eligible')
  @ApiOperation({ summary: 'Danh sách tài khoản đủ điều kiện tạo tài khoản VA' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách tài khoản đủ điều kiện tạo tài khoản VA thành công',
    type: [EligibleVAAccountResponseDto],
  })
  async getEligibleVAAccounts(
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<EligibleVAAccountResponseDto[]>> {
    const accounts = await this.paymentGatewayUserService.getEligibleVAAccounts(user.id);
    return ApiResponseDto.success(accounts, 'Lấy danh sách tài khoản đủ điều kiện tạo tài khoản VA thành công');
  }

  /**
   * Lấy danh sách tài khoản đủ điều kiện liên kết với chatbot
   */
  @Get('chatbots/eligible-payments')
  @ApiOperation({ summary: 'Danh sách tài khoản đủ điều kiện liên kết với chatbot' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách tài khoản đủ điều kiện liên kết với chatbot thành công',
    type: [EligibleVAAccountResponseDto],
  })
  async getEligiblePaymentAccountsForChatBot(
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<EligibleVAAccountResponseDto[]>> {
    const accounts = await this.paymentGatewayUserService.getEligiblePaymentAccountsForChatBot(user.id);
    return ApiResponseDto.success(accounts, 'Danh sách tài khoản đủ điều kiện!');
  }
}
