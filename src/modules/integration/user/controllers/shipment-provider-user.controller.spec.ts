import { Test, TestingModule } from '@nestjs/testing';
import { ShipmentProviderUserController } from './shipment-provider-user.controller';
import { ShipmentProviderUserService } from '../services/shipment-provider-user.service';
import { ProviderShipmentType } from '../../entities/provider-shipment-type.enum';
import { User } from '@modules/user/entities';

describe('ShipmentProviderUserController - Data Masking Integration', () => {
  let controller: ShipmentProviderUserController;
  let mockService: jest.Mocked<ShipmentProviderUserService>;

  beforeEach(async () => {
    const mockShipmentService = {
      findUserShipmentProviders: jest.fn(),
      findShipmentProviderById: jest.fn(),
      createShipmentProvider: jest.fn(),
      updateShipmentProvider: jest.fn(),
      deleteShipmentProvider: jest.fn(),
      testShipmentProvider: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ShipmentProviderUserController],
      providers: [
        {
          provide: ShipmentProviderUserService,
          useValue: mockShipmentService,
        },
      ],
    }).compile();

    controller = module.get<ShipmentProviderUserController>(ShipmentProviderUserController);
    mockService = module.get(ShipmentProviderUserService);
  });

  describe('GET /api/v1/user/integration/shipment-providers', () => {
    it('should return paginated list with masked sensitive data', async () => {
      // Arrange
      const mockUser: User = { id: 1 } as User;
      const mockQueryDto = { page: 1, limit: 10 };
      
      const mockServiceResponse = {
        items: [
          {
            id: 'test-id-1',
            userId: 1,
            name: 'GHN Config',
            type: ProviderShipmentType.GHN,
            config: {
              token: 'ghn_**********cdef',
              shopId: 'shop******21',
              environment: 'production'
            },
            createdAt: Date.now()
          },
          {
            id: 'test-id-2',
            userId: 1,
            name: 'GHTK Config',
            type: ProviderShipmentType.GHTK,
            config: {
              apiToken: 'ghtk**********90',
              partnerCode: 'part**********ef',
              environment: 'sandbox'
            },
            createdAt: Date.now()
          }
        ],
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1
        }
      };

      mockService.findUserShipmentProviders.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await controller.findUserShipmentProviders(mockUser, mockQueryDto);

      // Assert
      expect(result.code).toBe(200);
      expect(result.message).toBe('Lấy danh sách cấu hình vận chuyển thành công');
      expect(result.result.items).toHaveLength(2);
      
      // Verify GHN config masking
      const ghnConfig = result.result.items[0];
      expect(ghnConfig.config.token).toBe('ghn_**********cdef');
      expect(ghnConfig.config.shopId).toBe('shop******21');
      expect(ghnConfig.config.environment).toBe('production');
      
      // Verify GHTK config masking
      const ghtkConfig = result.result.items[1];
      expect(ghtkConfig.config.apiToken).toBe('ghtk**********90');
      expect(ghtkConfig.config.partnerCode).toBe('part**********ef');
      expect(ghtkConfig.config.environment).toBe('sandbox');
    });
  });

  describe('GET /api/v1/user/integration/shipment-providers/:id', () => {
    it('should return single provider with masked sensitive data', async () => {
      // Arrange
      const mockUser: User = { id: 1 } as User;
      const providerId = 'test-id';
      
      const mockServiceResponse = {
        id: 'test-id',
        userId: 1,
        name: 'Ahamove Config',
        type: ProviderShipmentType.AHAMOVE,
        config: {
          apiKey: 'aham**********90',
          token: 'acce**********jk',
          refreshToken: 'refr**********56',
          mobile: '012***6789',
          environment: 'production'
        },
        createdAt: Date.now()
      };

      mockService.findShipmentProviderById.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await controller.findShipmentProviderById(mockUser, providerId);

      // Assert
      expect(result.code).toBe(200);
      expect(result.message).toBe('Lấy thông tin cấu hình vận chuyển thành công');
      expect(result.result.config.apiKey).toBe('aham**********90');
      expect(result.result.config.token).toBe('acce**********jk');
      expect(result.result.config.refreshToken).toBe('refr**********56');
      expect(result.result.config.mobile).toBe('012***6789');
      expect(result.result.config.environment).toBe('production');
    });
  });

  describe('POST /api/v1/user/integration/shipment-providers', () => {
    it('should create provider and return response with masked data', async () => {
      // Arrange
      const mockUser: User = { id: 1 } as User;
      const createDto = {
        name: 'New GHN Config',
        type: ProviderShipmentType.GHN,
        config: {
          token: 'ghn_token_1234567890abcdef',
          shopId: 'shop_987654321',
          environment: 'production'
        }
      };
      
      const mockServiceResponse = {
        id: 'new-id',
        userId: 1,
        name: 'New GHN Config',
        type: ProviderShipmentType.GHN,
        config: {
          token: 'ghn_**********cdef',
          shopId: 'shop******21',
          environment: 'production'
        },
        createdAt: Date.now()
      };

      mockService.createShipmentProvider.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await controller.createShipmentProvider(mockUser, createDto);

      // Assert
      expect(result.code).toBe(201);
      expect(result.message).toBe('Tạo cấu hình vận chuyển thành công');
      expect(result.result.config.token).toBe('ghn_**********cdef');
      expect(result.result.config.shopId).toBe('shop******21');
      expect(result.result.config.environment).toBe('production');
    });
  });

  describe('PATCH /api/v1/user/integration/shipment-providers/:id', () => {
    it('should update provider and return response with masked data', async () => {
      // Arrange
      const mockUser: User = { id: 1 } as User;
      const providerId = 'test-id';
      const updateDto = {
        name: 'Updated Config',
        config: {
          apiKey: 'new_api_key_1234567890abcdef',
          secretKey: 'new_secret_key_xyz123456789'
        }
      };
      
      const mockServiceResponse = {
        id: 'test-id',
        userId: 1,
        name: 'Updated Config',
        type: ProviderShipmentType.JT,
        config: {
          apiKey: 'new_**********ef',
          secretKey: 'new_**********89'
        },
        createdAt: Date.now()
      };

      mockService.updateShipmentProvider.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await controller.updateShipmentProvider(mockUser, providerId, updateDto);

      // Assert
      expect(result.code).toBe(200);
      expect(result.message).toBe('Cập nhật cấu hình vận chuyển thành công');
      expect(result.result.config.apiKey).toBe('new_**********ef');
      expect(result.result.config.secretKey).toBe('new_**********89');
    });
  });

  describe('POST /api/v1/user/integration/shipment-providers/test', () => {
    it('should test connection without exposing sensitive data in logs', async () => {
      // Arrange
      const testDto = {
        type: ProviderShipmentType.GHN,
        config: {
          token: 'ghn_token_1234567890abcdef',
          shopId: 'shop_987654321'
        }
      };
      
      const mockServiceResponse = {
        success: true,
        message: 'Kết nối thành công với GHN',
        data: {
          shopInfo: {
            name: 'Test Shop',
            status: 'active'
          }
        }
      };

      mockService.testShipmentProvider.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await controller.testShipmentProvider(testDto);

      // Assert
      expect(result.code).toBe(200);
      expect(result.message).toBe('Test kết nối thành công');
      expect(result.result.success).toBe(true);
      expect(result.result.message).toBe('Kết nối thành công với GHN');
      
      // Verify service was called with original (unmasked) config for testing
      expect(mockService.testShipmentProvider).toHaveBeenCalledWith(testDto);
    });
  });
});
