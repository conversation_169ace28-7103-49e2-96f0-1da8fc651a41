import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { User } from '@modules/user/entities';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ShipmentProviderUserService } from '../services/shipment-provider-user.service';
import {
  CreateShipmentProviderDto,
  UpdateShipmentProviderDto,
  ShipmentProviderQueryDto,
  ShipmentProviderResponseDto,
  TestShipmentProviderDto,
  TestShipmentProviderResponseDto
} from '../dto/shipment';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.USER_INTEGRATION_SHIPMENT)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/shipment-providers')
export class ShipmentProviderUserController {
  constructor(
    private readonly shipmentProviderUserService: ShipmentProviderUserService
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách cấu hình nhà cung cấp vận chuyển',
    description: 'Lấy danh sách tất cả cấu hình nhà cung cấp vận chuyển của user với phân trang và filter'
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (mặc định: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang (mặc định: 10)' })
  @ApiQuery({ name: 'type', required: false, description: 'Lọc theo loại nhà cung cấp' })
  @ApiQuery({ name: 'name', required: false, description: 'Tìm kiếm theo tên cấu hình' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto<ShipmentProviderResponseDto[]>
  })
  async findUserShipmentProviders(
    @CurrentUser() user: User,
    @Query() queryDto: ShipmentProviderQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<ShipmentProviderResponseDto>>> {
    const result = await this.shipmentProviderUserService.findUserShipmentProviders(
      user.id,
      queryDto
    );

    return ApiResponseDto.paginated(result, 'Lấy danh sách cấu hình vận chuyển thành công');
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết cấu hình nhà cung cấp vận chuyển',
    description: 'Lấy thông tin chi tiết của một cấu hình nhà cung cấp vận chuyển'
  })
  @ApiParam({ name: 'id', description: 'ID của cấu hình vận chuyển' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin thành công',
    type: ApiResponseDto<ShipmentProviderResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async findShipmentProviderById(
    @CurrentUser() user: User,
    @Param('id') id: string
  ): Promise<ApiResponseDto<ShipmentProviderResponseDto>> {
    const result = await this.shipmentProviderUserService.findShipmentProviderById(
      user.id,
      id
    );

    return {
      code: 200,
      message: 'Lấy thông tin cấu hình vận chuyển thành công',
      result
    };
  }

  @Post()
  @ApiOperation({
    summary: 'Tạo cấu hình nhà cung cấp vận chuyển mới',
    description: 'Tạo cấu hình tích hợp mới với nhà cung cấp vận chuyển'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo cấu hình thành công',
    type: ApiResponseDto<ShipmentProviderResponseDto>
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ'
  })
  @ApiResponse({
    status: 409,
    description: 'Đã tồn tại cấu hình cho nhà cung cấp này'
  })
  @HttpCode(HttpStatus.CREATED)
  async createShipmentProvider(
    @CurrentUser() user: User,
    @Body() createDto: CreateShipmentProviderDto
  ): Promise<ApiResponseDto<ShipmentProviderResponseDto>> {
    const result = await this.shipmentProviderUserService.createShipmentProvider(
      user.id,
      createDto
    );

    return {
      code: 201,
      message: 'Tạo cấu hình vận chuyển thành công',
      result
    };
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật cấu hình nhà cung cấp vận chuyển',
    description: 'Cập nhật thông tin cấu hình nhà cung cấp vận chuyển'
  })
  @ApiParam({ name: 'id', description: 'ID của cấu hình vận chuyển' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    type: ApiResponseDto<ShipmentProviderResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async updateShipmentProvider(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() updateDto: UpdateShipmentProviderDto
  ): Promise<ApiResponseDto<ShipmentProviderResponseDto>> {
    const result = await this.shipmentProviderUserService.updateShipmentProvider(
      user.id,
      id,
      updateDto
    );

    return {
      code: 200,
      message: 'Cập nhật cấu hình vận chuyển thành công',
      result
    };
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa cấu hình nhà cung cấp vận chuyển',
    description: 'Xóa cấu hình tích hợp với nhà cung cấp vận chuyển'
  })
  @ApiParam({ name: 'id', description: 'ID của cấu hình vận chuyển' })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  @HttpCode(HttpStatus.OK)
  async deleteShipmentProvider(
    @CurrentUser() user: User,
    @Param('id') id: string
  ): Promise<ApiResponseDto<null>> {
    await this.shipmentProviderUserService.deleteShipmentProvider(user.id, id);

    return {
      code: 200,
      message: 'Xóa cấu hình vận chuyển thành công',
      result: null
    };
  }

  @Post('test-connection')
  @ApiOperation({
    summary: 'Test kết nối với nhà cung cấp vận chuyển',
    description: 'Kiểm tra kết nối và xác thực với nhà cung cấp vận chuyển'
  })
  @ApiResponse({
    status: 200,
    description: 'Test kết nối thành công',
    type: ApiResponseDto<TestShipmentProviderResponseDto>
  })
  @ApiResponse({
    status: 400,
    description: 'Thông tin cấu hình không hợp lệ'
  })
  @HttpCode(HttpStatus.OK)
  async testShipmentProvider(
    @Body() testDto: TestShipmentProviderDto
  ): Promise<ApiResponseDto<TestShipmentProviderResponseDto>> {
    const result = await this.shipmentProviderUserService.testShipmentProvider(testDto);

    return {
      code: 200,
      message: 'Test kết nối hoàn tất',
      result
    };
  }
}
