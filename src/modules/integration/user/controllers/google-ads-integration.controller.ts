import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response';
import { GoogleAdsIntegrationService } from '../services/google-ads-integration.service';
import {
  GoogleAdsAuthUrlResponseDto,
  GoogleAdsAuthUrlRequestDto,
  GoogleAdsAuthCallbackDto,
  GoogleAdsAccountResponseDto,
  UpdateGoogleAdsAccountDto,
  GoogleAdsCustomerResponseDto,
  SelectGoogleAdsCustomerDto,
} from '../dto';

@ApiTags('Google Ads Integration')
@Controller('integration/user/google-ads')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class GoogleAdsIntegrationController {
  constructor(
    private readonly googleAdsIntegrationService: GoogleAdsIntegrationService,
  ) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Lấy URL xác thực Google Ads' })
  @ApiResponse({
    status: 200,
    description: 'URL xác thực Google Ads',
    type: GoogleAdsAuthUrlResponseDto,
  })
  @ApiBody({ type: GoogleAdsAuthUrlRequestDto })
  getAuthUrl(
    @CurrentUser() user: JWTPayload,
    @Body() body: GoogleAdsAuthUrlRequestDto,
  ): ApiResponseDto<GoogleAdsAuthUrlResponseDto> {
    const { url, state } = this.googleAdsIntegrationService.generateAuthUrl(user.id, body.redirectUri);
    return ApiResponseDto.success(
      { url, state },
      'Lấy URL xác thực Google Ads thành công',
    );
  }

  @Post('auth-callback')
  @ApiOperation({ summary: 'Xử lý callback xác thực Google Ads' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tài khoản Google Ads đã tích hợp',
    type: GoogleAdsAccountResponseDto,
  })
  async handleAuthCallback(
    @Body() body: GoogleAdsAuthCallbackDto,
  ): Promise<ApiResponseDto<GoogleAdsAccountResponseDto>> {
    const account = await this.googleAdsIntegrationService.handleAuthCallback(
      body.code,
      body.state,
      body.name,
    );

    const response: GoogleAdsAccountResponseDto = {
      id: account.id,
      customerId: account.customerId,
      name: account.name,
      status: account.status,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    };

    return ApiResponseDto.success(
      response,
      'Xác thực Google Ads thành công',
    );
  }

  @Get('accounts')
  @ApiOperation({ summary: 'Lấy danh sách tài khoản Google Ads' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tài khoản Google Ads',
    type: [GoogleAdsAccountResponseDto],
  })
  async getAccounts(
    @CurrentUser() user: JWTPayload,
  ): Promise<ApiResponseDto<GoogleAdsAccountResponseDto[]>> {
    const accounts = await this.googleAdsIntegrationService.getAccounts(user.id);

    const response = accounts.map(account => ({
      id: account.id,
      customerId: account.customerId,
      name: account.name,
      status: account.status,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    }));

    return ApiResponseDto.success(
      response,
      'Lấy danh sách tài khoản Google Ads thành công',
    );
  }

  @Get('accounts/:id')
  @ApiOperation({ summary: 'Lấy thông tin tài khoản Google Ads' })
  @ApiParam({
    name: 'id',
    description: 'ID của tài khoản Google Ads',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tài khoản Google Ads',
    type: GoogleAdsAccountResponseDto,
  })
  async getAccount(
    @CurrentUser() user: JWTPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<GoogleAdsAccountResponseDto>> {
    const account = await this.googleAdsIntegrationService.getAccount(id, user.id);

    const response: GoogleAdsAccountResponseDto = {
      id: account.id,
      customerId: account.customerId,
      name: account.name,
      status: account.status,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    };

    return ApiResponseDto.success(
      response,
      'Lấy thông tin tài khoản Google Ads thành công',
    );
  }

  @Put('accounts/:id')
  @ApiOperation({ summary: 'Cập nhật tài khoản Google Ads' })
  @ApiParam({
    name: 'id',
    description: 'ID của tài khoản Google Ads',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả cập nhật tài khoản Google Ads',
    type: Boolean,
  })
  async updateAccount(
    @CurrentUser() user: JWTPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateGoogleAdsAccountDto,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.googleAdsIntegrationService.updateAccount(id, user.id, body);
    return ApiResponseDto.success(
      result,
      'Cập nhật tài khoản Google Ads thành công',
    );
  }

  @Delete('accounts/:id')
  @ApiOperation({ summary: 'Xóa tài khoản Google Ads' })
  @ApiParam({
    name: 'id',
    description: 'ID của tài khoản Google Ads',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa tài khoản Google Ads',
    type: Boolean,
  })
  async deleteAccount(
    @CurrentUser() user: JWTPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.googleAdsIntegrationService.deleteAccount(id, user.id);
    return ApiResponseDto.success(
      result,
      'Xóa tài khoản Google Ads thành công',
    );
  }

  @Get('customers')
  @ApiOperation({ summary: 'Lấy danh sách customer Google Ads' })
  @ApiQuery({
    name: 'refreshToken',
    description: 'Refresh token của Google',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách customer Google Ads',
    type: [GoogleAdsCustomerResponseDto],
  })
  async listCustomers(
    @Query('refreshToken') refreshToken: string,
  ): Promise<ApiResponseDto<GoogleAdsCustomerResponseDto[]>> {
    const customers = await this.googleAdsIntegrationService.listCustomers(refreshToken);
    return ApiResponseDto.success(
      customers,
      'Lấy danh sách customer Google Ads thành công',
    );
  }

  @Post('customers/select')
  @ApiOperation({ summary: 'Chọn customer Google Ads để tích hợp' })
  @ApiQuery({
    name: 'refreshToken',
    description: 'Refresh token của Google',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tài khoản Google Ads đã tích hợp',
    type: GoogleAdsAccountResponseDto,
  })
  async selectCustomer(
    @CurrentUser() user: JWTPayload,
    @Query('refreshToken') refreshToken: string,
    @Body() body: SelectGoogleAdsCustomerDto,
  ): Promise<ApiResponseDto<GoogleAdsAccountResponseDto>> {
    const account = await this.googleAdsIntegrationService.selectCustomer(
      user.id,
      refreshToken,
      body,
    );

    const response: GoogleAdsAccountResponseDto = {
      id: account.id,
      customerId: account.customerId,
      name: account.name,
      status: account.status,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    };

    return ApiResponseDto.success(
      response,
      'Tích hợp tài khoản Google Ads thành công',
    );
  }
}
