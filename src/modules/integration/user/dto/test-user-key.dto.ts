import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc kiểm tra API key
 */
export class TestUserKeyDto {
  @ApiProperty({
    description: 'Key nội bộ định danh nhà cung cấp',
    example: 'openai'
  })
  @IsNotEmpty()
  @IsString()
  providerKey: string;

  @ApiProperty({
    description: 'Thông tin xác thực API',
    example: {
      api_key: 'sk-abcdefghijklmnopqrstuvwxyz',
      organization_id: 'org-123456789'
    }
  })
  @IsNotEmpty()
  @IsObject()
  credentials: Record<string, any>;

  @ApiProperty({
    description: 'Mô hình để kiểm tra (tùy chọn)',
    example: 'gpt-3.5-turbo',
    required: false
  })
  @IsOptional()
  @IsString()
  model?: string;
}
