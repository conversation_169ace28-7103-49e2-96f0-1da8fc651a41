import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho request callback xác thực Google Ads
 */
export class GoogleAdsAuthCallbackDto {
  @ApiProperty({
    description: 'Authorization code từ Google',
    example: '4/0AY0e-g6_DUip4j8KhO...',
  })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({
    description: 'State token để xác thực callback',
    example: 'abc123xyz456',
  })
  @IsNotEmpty()
  @IsString()
  state: string;

  @ApiProperty({
    description: 'Tên tài khoản (tùy chọn)',
    example: 'Tài khoản quảng cáo chính',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;
}
