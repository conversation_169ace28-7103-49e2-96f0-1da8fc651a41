import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho phản hồi thông tin tích hợp
 */
export class IntegrationResponseDto {
  /**
   * ID của tích hợp
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tích hợp',
    example: 1
  })
  @Expose()
  id: number;

  /**
   * Tên của tích hợp
   * @example "Google Analytics Integration"
   */
  @ApiProperty({
    description: 'Tên của tích hợp',
    example: 'Google Analytics Integration'
  })
  @Expose()
  integrationName: string;

  /**
   * <PERSON><PERSON><PERSON> tích hợp
   * @example "ANALYTICS"
   */
  @ApiProperty({
    description: 'Loại tích hợp',
    example: 'ANALYTICS'
  })
  @Expose()
  type: string;

  /**
   * ID của người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  @Expose()
  userId: number;

  /**
   * Thông tin bổ sung của tích hợp
   * @example { "apiKey": "GA-12345", "propertyId": "123456789" }
   */
  @ApiProperty({
    description: 'Thông tin bổ sung của tích hợp',
    example: { apiKey: 'GA-12345', propertyId: '123456789' }
  })
  @Expose()
  info: Record<string, any> | null;
} 