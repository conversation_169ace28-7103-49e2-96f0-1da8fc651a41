import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc cập nhật tích hợp
 */
export class UpdateIntegrationDto {
  /**
   * Tên của tích hợp
   * @example "Google Analytics Integration Updated"
   */
  @ApiProperty({
    description: 'Tên của tích hợp',
    example: 'Google Analytics Integration Updated',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Tên tích hợp phải là chuỗi' })
  integrationName?: string;

  /**
   * Loại tích hợp
   * @example "ANALYTICS"
   */
  @ApiProperty({
    description: 'Loại tích hợp',
    example: 'ANALYTICS',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Loại tích hợp phải là chuỗi' })
  type?: string;

  /**
   * Thông tin bổ sung của tích hợp
   * @example { "apiKey": "GA-12345-UPDATED", "propertyId": "123456789" }
   */
  @ApiProperty({
    description: 'Thông tin bổ sung của tích hợp',
    example: { apiKey: 'GA-12345-UPDATED', propertyId: '123456789' },
    required: false
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin tích hợp phải là đối tượng JSON' })
  info?: Record<string, any>;
} 