import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách tích hợp
 */
export class QueryIntegrationDto extends QueryDto {
  /**
   * Tìm kiếm theo tên tích hợp
   * @example "Google"
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên tích hợp',
    example: 'Google'
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  /**
   * Lọc theo loại tích hợp
   * @example "ANALYTICS"
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại tích hợp',
    example: 'ANALYTICS'
  })
  @IsOptional()
  @IsString()
  type?: string;
} 