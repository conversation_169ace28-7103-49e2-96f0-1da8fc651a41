import { ApiProperty } from '@nestjs/swagger';
import { ProviderShipmentType } from '../../../constants/provider-shipment-type.enum';

/**
 * DTO response cho thông tin cấu hình nhà cung cấp vận chuyển
 */
export class ShipmentProviderResponseDto {
  @ApiProperty({ description: 'ID của cấu hình' })
  id: string;

  @ApiProperty({ description: 'ID người dùng' })
  userId: number;

  @ApiProperty({ description: 'Tên hiển thị của cấu hình' })
  name: string;

  @ApiProperty({
    description: 'Loại nhà cung cấp vận chuyển',
    enum: ProviderShipmentType
  })
  type: ProviderShipmentType;

  @ApiProperty({
    description: 'Cấu hình nhà cung cấp với thông tin nhạy cảm đã được ẩn (chỉ hiển thị 4 ký tự đầu và 4 ký tự cuối)',
    example: {
      token: 'ghn_**********cdef',
      shopId: 'shop******21',
      environment: 'production'
    }
  })
  config: any;

  @ApiProperty({ description: 'Thời gian tạo (Unix timestamp)' })
  createdAt: number;

  @ApiProperty({ description: 'Trạng thái kết nối', required: false })
  connectionStatus?: 'connected' | 'disconnected' | 'error';

  @ApiProperty({ description: 'Thông báo trạng thái', required: false })
  statusMessage?: string;
}

/**
 * DTO response cho test kết nối nhà cung cấp vận chuyển
 */
export class TestShipmentProviderResponseDto {
  @ApiProperty({ description: 'Kết quả test kết nối' })
  success: boolean;

  @ApiProperty({ description: 'Thông báo kết quả' })
  message: string;

  @ApiProperty({ description: 'Dữ liệu chi tiết (nếu có)', required: false })
  data?: any;
}
