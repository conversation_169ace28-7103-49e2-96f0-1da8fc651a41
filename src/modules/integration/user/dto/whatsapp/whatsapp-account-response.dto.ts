import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin tài khoản WhatsApp Business
 */
export class WhatsAppAccountResponseDto {
  @ApiProperty({
    description: 'ID của tài khoản WhatsApp',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID số điện thoại WhatsApp Business',
    example: '***************',
  })
  phoneNumberId: string;

  @ApiProperty({
    description: 'Số điện thoại WhatsApp Business (định dạng quốc tế)',
    example: '+***********',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'ID tài khoản doanh nghiệp WhatsApp',
    example: '***************',
  })
  businessAccountId: string;

  @ApiProperty({
    description: 'Tên hiển thị của tài khoản',
    example: 'RedAI Support',
  })
  displayName: string;

  @ApiProperty({
    description: 'Trạng thái hoạt động của tài khoản',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'ID agent được kết nối với tài khoản WhatsApp (nếu có)',
    example: '550e8400-e29b-41d4-a716-************',
    nullable: true,
  })
  agentId: string | null | undefined;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: **********,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: **********,
  })
  updatedAt: number;
}
