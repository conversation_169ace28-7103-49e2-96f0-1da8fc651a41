import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsIn, IsOptional, IsString, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * Enum cho các trường sắp xếp
 */
export enum FacebookPageSortBy {
  CREATED_AT = 'created_at',
  PAGE_NAME = 'page_name',
  PERSONAL_NAME = 'personal_name',
}

/**
 * DTO cho truy vấn danh sách trang Facebook
 */
export class FacebookPageQueryDto extends QueryDto {
  // Override sortBy từ QueryDto để sử dụng enum
  @ApiProperty({
    description: 'Trường sắp xếp',
    required: false,
    enum: FacebookPageSortBy,
    default: FacebookPageSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(FacebookPageSortBy)
  sortBy?: FacebookPageSortBy = FacebookPageSortBy.CREATED_AT;

  // Disable search field từ QueryDto
  declare search?: never;

  // Disable sortDirection field từ QueryDto
  declare sortDirection?: never;
}
