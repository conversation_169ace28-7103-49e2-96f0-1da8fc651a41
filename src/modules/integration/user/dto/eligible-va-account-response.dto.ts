import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

/**
 * DTO cho response thông tin tài khoản đủ điều kiện tạo tài khoản VA
 */
export class EligibleVAAccountResponseDto {
  @ApiProperty({
    description: 'ID của tài khoản ngân hàng',
    example: 1
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'ID tài khoản trên SePay Hub',
    example: 'acc_123456789'
  })
  @IsString()
  accountId: string;

  @ApiProperty({
    description: 'Đường dẫn đến icon ngân hàng',
    example: 'https://example.com/bank-icon.png'
  })
  @IsString()
  iconBank: string;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'OCB'
  })
  @IsString()
  bankCode: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tài khoản',
    example: '**********'
  })
  @IsString()
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A'
  })
  @IsString()
  accountName: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    example: 'ACTIVE'
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Là tài khoản VA hay không',
    example: false
  })
  @IsBoolean()
  isVA: boolean;

  @ApiProperty({
    description: 'Có thể xóa hay không',
    example: true
  })
  @IsBoolean()
  isDelete: boolean;
}
