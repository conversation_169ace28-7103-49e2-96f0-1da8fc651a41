import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsObject, IsOptional, IsString, Max, <PERSON> } from 'class-validator';

/**
 * DTO cho việc cập nhật cấu hình máy chủ email
 */
export class UpdateEmailServerDto {
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình, ví dụ: "Mailgun Server #1" hoặc "AWS SES"',
    example: 'Gmail SMTP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên máy chủ phải là chuỗi' })
  serverName?: string;

  @ApiProperty({
    description: 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…',
    example: 'smtp.gmail.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ máy chủ phải là chuỗi' })
  host?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON>, ví dụ: 465, 587, …',
    example: 587,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '<PERSON><PERSON>ng phải là số' })
  @Min(1, { message: 'Cổng phải lớn hơn 0' })
  @Max(65535, { message: 'Cổng không được vượt quá 65535' })
  port?: number;

  @ApiProperty({
    description: 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
  username?: string;

  @ApiProperty({
    description: 'Mật khẩu hoặc token xác thực cho SMTP',
    example: 'app-password-or-token',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  password?: string;

  @ApiProperty({
    description: 'Xác định có sử dụng SSL/TLS hay không',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường useSsl phải là boolean' })
  useSsl?: boolean;

  @ApiProperty({
    description: 'Xác định có sử dụng STARTTLS hay không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường useStartTls phải là boolean' })
  useStartTls?: boolean;

  @ApiProperty({
    description: 'Trạng thái hoạt động của cấu hình',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường isActive phải là boolean' })
  isActive?: boolean;

  @ApiProperty({
    description: 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.',
    example: { auth: 'login', tls: { rejectUnauthorized: false } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình nâng cao phải là đối tượng' })
  additionalSettings?: Record<string, any>;
}
