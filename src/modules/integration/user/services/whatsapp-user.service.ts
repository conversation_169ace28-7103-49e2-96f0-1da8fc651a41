import { PaginatedResult } from '@/common/response';
import { AgentRepository } from '@/modules/agent/repositories/agent.repository';
import { AppException } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import {
  WhatsAppAccount,
  WhatsAppMessage
} from '../../entities';
import {
  WhatsAppAccountRepository,
  WhatsAppMessageRepository,
  WhatsAppTemplateRepository,
  WhatsAppWebhookLogRepository,
} from '../../repositories';
import {
  ConnectWhatsAppAgentDto,
  CreateWhatsAppAccountDto,
  SendWhatsAppMessageDto,
  UpdateWhatsAppAccountDto,
  WhatsAppAccountQueryDto,
  WhatsAppAccountResponseDto,
  WhatsAppMessageResponseDto,
  WhatsAppMessageType
} from '../dto/whatsapp';

/**
 * Service quản lý tích hợp WhatsApp cho người dùng
 */
@Injectable()
export class WhatsAppUserService {
  private readonly logger = new Logger(WhatsAppUserService.name);

  constructor(
    private readonly whatsappAccountRepository: WhatsAppAccountRepository,
    private readonly whatsappTemplateRepository: WhatsAppTemplateRepository,
    private readonly whatsappMessageRepository: WhatsAppMessageRepository,
    private readonly whatsappWebhookLogRepository: WhatsAppWebhookLogRepository,
    private readonly agentRepository: AgentRepository,
    private readonly configService: ConfigService,
  ) { }

  /**
   * Lấy danh sách tài khoản WhatsApp của người dùng
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách tài khoản WhatsApp
   */
  async findWhatsAppAccounts(
    userId: number,
    query: WhatsAppAccountQueryDto,
  ): Promise<PaginatedResult<WhatsAppAccountResponseDto>> {
    try {
      // Lấy danh sách tài khoản WhatsApp từ repository
      const accounts = await this.whatsappAccountRepository.findByUserId(userId);

      // Lọc theo trạng thái nếu có
      let filteredAccounts = accounts;
      if (query.isActive !== undefined) {
        filteredAccounts = accounts.filter(account => account.isActive === query.isActive);
      }

      // Lọc theo kết nối agent nếu có
      if (query.hasAgent !== undefined) {
        filteredAccounts = filteredAccounts.filter(account => {
          return query.hasAgent ? !!account.agentId : !account.agentId;
        });
      }

      // Lọc theo từ khóa tìm kiếm nếu có
      if (query.search) {
        const searchLower = query.search.toLowerCase();
        filteredAccounts = filteredAccounts.filter(account => {
          return (
            account.displayName.toLowerCase().includes(searchLower) ||
            account.phoneNumber.includes(searchLower)
          );
        });
      }

      // Tính toán phân trang
      const totalItems = filteredAccounts.length;
      const page = query.page || 1;
      const limit = query.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedAccounts = filteredAccounts.slice(startIndex, endIndex);

      // Chuyển đổi sang DTO
      const items = paginatedAccounts.map(account => this.mapToWhatsAppAccountResponseDto(account));

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding WhatsApp accounts: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_LIST_ERROR,
        'Lỗi khi lấy danh sách tài khoản WhatsApp',
      );
    }
  }

  /**
   * Lấy thông tin tài khoản WhatsApp theo ID
   * @param userId ID của người dùng
   * @param accountId ID của tài khoản WhatsApp
   * @returns Thông tin tài khoản WhatsApp
   */
  async findWhatsAppAccountById(
    userId: number,
    accountId: number,
  ): Promise<WhatsAppAccountResponseDto> {
    try {
      const account = await this.whatsappAccountRepository.findById(accountId);
      if (!account) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản WhatsApp',
        );
      }

      if (account.userId !== userId) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ACCESS_DENIED,
          'Bạn không có quyền truy cập tài khoản WhatsApp này',
        );
      }

      return this.mapToWhatsAppAccountResponseDto(account);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding WhatsApp account: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_FIND_ERROR,
        'Lỗi khi lấy thông tin tài khoản WhatsApp',
      );
    }
  }

  /**
   * Tạo mới tài khoản WhatsApp
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo mới
   * @returns Tài khoản WhatsApp đã tạo
   */
  async createWhatsAppAccount(
    userId: number,
    createDto: CreateWhatsAppAccountDto,
  ): Promise<WhatsAppAccountResponseDto> {
    try {
      // Kiểm tra xem số điện thoại đã tồn tại chưa
      const existingAccount = await this.whatsappAccountRepository.findByPhoneNumber(createDto.phoneNumber);
      if (existingAccount) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ALREADY_EXISTS,
          'Số điện thoại WhatsApp này đã được đăng ký',
        );
      }

      // Kiểm tra agent nếu có
      if (createDto.agentId) {
        const agent = await this.agentRepository.findById(createDto.agentId);
        if (!agent) {
          throw new AppException(
            INTEGRATION_ERROR_CODES.AGENT_NOT_FOUND,
            'Không tìm thấy agent',
          );
        }

        // Kiểm tra quyền sở hữu agent
        const userAgent = await this.agentRepository.findOneByIdAndUserId(createDto.agentId, userId);
        if (!userAgent) {
          throw new AppException(
            INTEGRATION_ERROR_CODES.AGENT_ACCESS_DENIED,
            'Bạn không có quyền sử dụng agent này',
          );
        }
      }

      // Tạo mới tài khoản WhatsApp
      const newAccount = await this.whatsappAccountRepository.create({
        userId,
        phoneNumberId: createDto.phoneNumberId,
        phoneNumber: createDto.phoneNumber,
        businessAccountId: createDto.businessAccountId,
        displayName: createDto.displayName,
        accessToken: createDto.accessToken,
        agentId: createDto.agentId,
        isActive: true,
      });

      return this.mapToWhatsAppAccountResponseDto(newAccount);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating WhatsApp account: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_CREATE_ERROR,
        'Lỗi khi tạo tài khoản WhatsApp',
      );
    }
  }

  /**
   * Cập nhật tài khoản WhatsApp
   * @param userId ID của người dùng
   * @param accountId ID của tài khoản WhatsApp
   * @param updateDto Dữ liệu cập nhật
   * @returns Tài khoản WhatsApp đã cập nhật
   */
  async updateWhatsAppAccount(
    userId: number,
    accountId: number,
    updateDto: UpdateWhatsAppAccountDto,
  ): Promise<WhatsAppAccountResponseDto | null> {
    try {
      // Kiểm tra tài khoản tồn tại và quyền sở hữu
      const account = await this.whatsappAccountRepository.findById(accountId);
      if (!account) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản WhatsApp',
        );
      }

      if (account.userId !== userId) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ACCESS_DENIED,
          'Bạn không có quyền cập nhật tài khoản WhatsApp này',
        );
      }

      // Cập nhật tài khoản
      const updatedAccount = await this.whatsappAccountRepository.update(accountId, updateDto);

      return updatedAccount ? this.mapToWhatsAppAccountResponseDto(updatedAccount) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating WhatsApp account: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_UPDATE_ERROR,
        'Lỗi khi cập nhật tài khoản WhatsApp',
      );
    }
  }

  /**
   * Xóa tài khoản WhatsApp
   * @param userId ID của người dùng
   * @param accountId ID của tài khoản WhatsApp
   * @returns Kết quả xóa
   */
  async deleteWhatsAppAccount(userId: number, accountId: number): Promise<void> {
    try {
      // Kiểm tra tài khoản tồn tại và quyền sở hữu
      const account = await this.whatsappAccountRepository.findById(accountId);
      if (!account) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản WhatsApp',
        );
      }

      if (account.userId !== userId) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ACCESS_DENIED,
          'Bạn không có quyền xóa tài khoản WhatsApp này',
        );
      }

      // Xóa tài khoản
      const result = await this.whatsappAccountRepository.delete(accountId);
      if (!result) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_DELETE_ERROR,
          'Lỗi khi xóa tài khoản WhatsApp',
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting WhatsApp account: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_DELETE_ERROR,
        'Lỗi khi xóa tài khoản WhatsApp',
      );
    }
  }

  /**
   * Kết nối tài khoản WhatsApp với agent
   * @param userId ID của người dùng
   * @param accountId ID của tài khoản WhatsApp
   * @param connectDto Dữ liệu kết nối
   * @returns Tài khoản WhatsApp đã cập nhật
   */
  async connectAgent(
    userId: number,
    accountId: number,
    connectDto: ConnectWhatsAppAgentDto,
  ): Promise<WhatsAppAccountResponseDto | null> {
    try {
      // Kiểm tra tài khoản tồn tại và quyền sở hữu
      const account = await this.whatsappAccountRepository.findById(accountId);
      if (!account) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản WhatsApp',
        );
      }

      if (account.userId !== userId) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ACCESS_DENIED,
          'Bạn không có quyền kết nối tài khoản WhatsApp này',
        );
      }

      // Kiểm tra agent
      const agent = await this.agentRepository.findById(connectDto.agentId);
      if (!agent) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.AGENT_NOT_FOUND,
          'Không tìm thấy agent',
        );
      }

      // Kiểm tra quyền sở hữu agent
      const userAgent = await this.agentRepository.findOneByIdAndUserId(connectDto.agentId, userId);
      if (!userAgent) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.AGENT_ACCESS_DENIED,
          'Bạn không có quyền sử dụng agent này',
        );
      }

      // Kết nối với agent
      const updatedAccount = await this.whatsappAccountRepository.connectAgent(accountId, connectDto.agentId);

      return updatedAccount ? this.mapToWhatsAppAccountResponseDto(updatedAccount) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error connecting WhatsApp account to agent: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_CONNECT_ERROR,
        'Lỗi khi kết nối tài khoản WhatsApp với agent',
      );
    }
  }

  /**
   * Ngắt kết nối tài khoản WhatsApp với agent
   * @param userId ID của người dùng
   * @param accountId ID của tài khoản WhatsApp
   * @returns Tài khoản WhatsApp đã cập nhật
   */
  async disconnectAgent(
    userId: number,
    accountId: number,
  ): Promise<WhatsAppAccountResponseDto | null> {
    try {
      // Kiểm tra tài khoản tồn tại và quyền sở hữu
      const account = await this.whatsappAccountRepository.findById(accountId);
      if (!account) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản WhatsApp',
        );
      }

      if (account.userId !== userId) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ACCESS_DENIED,
          'Bạn không có quyền ngắt kết nối tài khoản WhatsApp này',
        );
      }

      // Ngắt kết nối với agent
      const updatedAccount = await this.whatsappAccountRepository.disconnectAgent(accountId);

      return updatedAccount ? this.mapToWhatsAppAccountResponseDto(updatedAccount) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error disconnecting WhatsApp account from agent: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_DISCONNECT_ERROR,
        'Lỗi khi ngắt kết nối tài khoản WhatsApp với agent',
      );
    }
  }

  /**
   * Gửi tin nhắn WhatsApp
   * @param userId ID của người dùng
   * @param accountId ID của tài khoản WhatsApp
   * @param sendDto Dữ liệu gửi tin nhắn
   * @returns Thông tin tin nhắn đã gửi
   */
  async sendMessage(
    userId: number,
    accountId: number,
    sendDto: SendWhatsAppMessageDto,
  ): Promise<WhatsAppMessageResponseDto> {
    try {
      // Kiểm tra tài khoản tồn tại và quyền sở hữu
      const account = await this.whatsappAccountRepository.findById(accountId);
      if (!account) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản WhatsApp',
        );
      }

      if (account.userId !== userId) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.WHATSAPP_ACCOUNT_ACCESS_DENIED,
          'Bạn không có quyền sử dụng tài khoản WhatsApp này',
        );
      }

      // Chuẩn bị dữ liệu gửi tin nhắn
      let messageData: Record<string, unknown>;
      let metadata: Record<string, unknown> = {};

      switch (sendDto.messageType) {
        case WhatsAppMessageType.TEXT:
          if (!sendDto.content) {
            throw new AppException(
              INTEGRATION_ERROR_CODES.WHATSAPP_MESSAGE_INVALID,
              'Nội dung tin nhắn không được để trống',
            );
          }
          messageData = {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: sendDto.phoneNumber,
            type: 'text',
            text: {
              preview_url: true,
              body: sendDto.content,
            },
          };
          break;

        case WhatsAppMessageType.TEMPLATE:
          if (!sendDto.template) {
            throw new AppException(
              INTEGRATION_ERROR_CODES.WHATSAPP_MESSAGE_INVALID,
              'Thông tin mẫu tin nhắn không được để trống',
            );
          }
          messageData = {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: sendDto.phoneNumber,
            type: 'template',
            template: sendDto.template,
          };
          metadata.templateName = sendDto.template.name;
          break;

        case WhatsAppMessageType.IMAGE:
        case WhatsAppMessageType.DOCUMENT:
        case WhatsAppMessageType.AUDIO:
        case WhatsAppMessageType.VIDEO:
          if (!sendDto.media) {
            throw new AppException(
              INTEGRATION_ERROR_CODES.WHATSAPP_MESSAGE_INVALID,
              'Thông tin media không được để trống',
            );
          }
          messageData = {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: sendDto.phoneNumber,
            type: sendDto.messageType,
            [sendDto.messageType]: {
              link: sendDto.media.link,
              caption: sendDto.media.caption,
            },
          };
          metadata.mediaUrl = sendDto.media.link;
          break;

        default:
          throw new AppException(
            INTEGRATION_ERROR_CODES.WHATSAPP_MESSAGE_INVALID,
            'Loại tin nhắn không hợp lệ',
          );
      }

      // Gửi tin nhắn qua WhatsApp API
      const apiUrl = `https://graph.facebook.com/v17.0/${account.phoneNumberId}/messages`;
      const response = await axios.post(apiUrl, messageData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${account.accessToken}`,
        },
      });

      // Lưu thông tin tin nhắn vào database
      const message = await this.whatsappMessageRepository.create({
        whatsappAccountId: accountId,
        messageId: response.data.messages[0].id,
        phoneNumber: sendDto.phoneNumber,
        content: sendDto.content,
        messageType: sendDto.messageType,
        metadata,
        direction: 'outgoing',
        status: 'sent',
        sentAt: Math.floor(Date.now() / 1000),
      });

      return this.mapToWhatsAppMessageResponseDto(message);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error sending WhatsApp message: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.WHATSAPP_MESSAGE_SEND_ERROR,
        'Lỗi khi gửi tin nhắn WhatsApp',
      );
    }
  }

  /**
   * Chuyển đổi entity WhatsAppAccount sang DTO
   * @param account Entity WhatsAppAccount
   * @returns WhatsAppAccountResponseDto
   */
  private mapToWhatsAppAccountResponseDto(account: WhatsAppAccount): WhatsAppAccountResponseDto {
    return {
      id: account.id,
      phoneNumberId: account.phoneNumberId,
      phoneNumber: account.phoneNumber,
      businessAccountId: account.businessAccountId,
      displayName: account.displayName,
      isActive: account.isActive,
      agentId: account.agentId,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    };
  }

  /**
   * Chuyển đổi entity WhatsAppMessage sang DTO
   * @param message Entity WhatsAppMessage
   * @returns WhatsAppMessageResponseDto
   */
  private mapToWhatsAppMessageResponseDto(message: WhatsAppMessage): WhatsAppMessageResponseDto {
    return {
      id: message.id,
      whatsappAccountId: message.whatsappAccountId,
      messageId: message.messageId,
      phoneNumber: message.phoneNumber,
      contactName: message.contactName,
      content: message.content,
      messageType: message.messageType as WhatsAppMessageType,
      metadata: message.metadata,
      direction: message.direction,
      status: message.status,
      sentAt: message.sentAt,
      updatedAt: message.updatedAt,
    };
  }
}
