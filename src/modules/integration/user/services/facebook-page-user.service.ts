import { FacebookService } from '@/shared/services/facebook/facebook.service';
import {
  FacebookPageInfo,
  FacebookUserInfo,
} from '@/shared/services/facebook/interfaces/facebook.interface';
import { AppException, ErrorCode } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { FacebookPage } from '../../entities';
import {
  FacebookPageRepository,
  FacebookPersonalRepository,
} from '../../repositories';
import { FacebookAuthResponseDto, FacebookPageResponseDto } from '../dto';
import { CallbackResponseDto } from '../dto/facebook/callback-response.dto';
import { FacebookPageMapper } from '../mappers';
import { FacebookPageQueryDto } from '../dto/facebook/facebook-page-query.dto';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import { PaginatedResult } from '@common/response';

@Injectable()
export class FacebookPageUserService {
  private readonly logger = new Logger(FacebookPageUserService.name);

  constructor(
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly facebookPersonalRepository: FacebookPersonalRepository,
    private readonly facebookService: FacebookService,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách trang Facebook của người dùng với phân trang và lọc
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trang Facebook với phân trang
   */
  async findFacebookPages(
    userId: number,
    queryDto: FacebookPageQueryDto = new FacebookPageQueryDto(),
  ): Promise<PaginatedResult<FacebookPageResponseDto>> {
    try {
      // Sử dụng repository để lấy danh sách trang Facebook chưa bị xóa mềm
      const { page = 1, limit = 10, sortBy } = queryDto;

      // Gọi repository với các tham số truy vấn
      const result = await this.facebookPageRepository.findPagesByUserId(
        userId,
        {
          page,
          limit,
          sortBy,
          includeDeleted: false,
        },
      );

      if (!result.items || result.items.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Sử dụng mapper để chuyển đổi kết quả thành DTO và thêm CDN URL cho avatar
      const items = FacebookPageMapper.toDtoList(result.items, this.cdnService);

      return {
        items,
        meta: {
          totalItems: result.total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(result.total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách trang Facebook: ${error.message}`,
        error.stack,
      );
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED);
    }
  }

  /**
   * Tạo URL xác thực Facebook
   * @param userId ID của người dùng
   * @returns URL xác thực Facebook
   */
  async createFacebookAuthUrl(
    userId: number,
    redirectUrl: string,
  ): Promise<FacebookAuthResponseDto> {
    try {
      // Tạo URL xác thực Facebook
      const scopes = [
        'public_profile',
        'pages_manage_metadata',
        'email',
        'pages_messaging',
        'pages_show_list',
      ];

      const authUrl = this.facebookService.createAuthUrl(
        redirectUrl,
        userId.toString(),
        scopes,
      );

      return { authUrl };
    } catch (error) {
      this.logger.error(
        `Error creating Facebook auth URL: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_CREATE_FAILED,
        'Lỗi khi tạo URL xác thực Facebook',
      );
    }
  }

  /**
   * Xử lý callback từ Facebook
   * @param userId ID của người dùng
   * @param code
   * @param redirectUri
   * @param state
   * @returns Thông tin tài khoản Facebook cá nhân đã tạo
   */
  async handleFacebookCallback(
    userId: number,
    code: string,
    redirectUri: string,
    state: string,
  ): Promise<CallbackResponseDto> {
    this.logger.log(`[DEBUG] Bắt đầu xử lý Facebook callback cho userId: ${userId}`);
    this.logger.log(`[DEBUG] Input parameters:`, {
      userId,
      code: code ? `${code.substring(0, 20)}...` : 'null',
      redirectUri,
      state,
    });

    try {
      // Kiểm tra state
      this.logger.log(`[DEBUG] Kiểm tra state: ${state} vs userId: ${userId}`);
      if (state !== userId.toString()) {
        this.logger.error(`[DEBUG] State validation failed: state=${state}, userId=${userId}`);
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_AUTH_INVALID,
          'State không hợp lệ',
        );
      }
      this.logger.log(`[DEBUG] State validation passed`);

      // Kiểm tra xem code có rỗng hoặc quá ngắn không
      if (!code || code.length < 10) {
        this.logger.error(`[DEBUG] Invalid authorization code: ${code}`);
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_AUTH_INVALID,
          'Authorization code không hợp lệ',
        );
      }

      // Đổi code lấy access token
      this.logger.log(`[DEBUG] Bắt đầu gọi FacebookService.handleCallback với code và redirectUri`);
      const tokenShort = await this.facebookService.handleCallback(
        code,
        redirectUri,
      );
      this.logger.log(`[DEBUG] Nhận được short-lived token:`, {
        hasAccessToken: !!tokenShort.access_token,
        tokenType: tokenShort.token_type,
        expiresIn: tokenShort.expires_in,
      });

      this.logger.log(`[DEBUG] Bắt đầu chuyển đổi sang long-lived token`);
      const { access_token, expires_in } =
        await this.facebookService.getLongLivedToken(tokenShort.access_token);
      this.logger.log(`[DEBUG] Nhận được long-lived token:`, {
        hasAccessToken: !!access_token,
        expiresIn: expires_in,
      });

      if (!access_token) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy access token từ Facebook',
        );
      }

      // Lấy thông tin người dùng Facebook
      this.logger.log(`[DEBUG] Bắt đầu lấy thông tin người dùng Facebook`);
      const userResponse: FacebookUserInfo =
        await this.facebookService.getUserInfo(access_token);
      this.logger.log(`[DEBUG] Thông tin người dùng Facebook:`, {
        facebookPersonalId: userResponse.id,
        name: userResponse.name,
        hasEmail: !!userResponse.email,
      });

      const { id: facebookPersonalId, name } = userResponse;

      if (!name) {
        this.logger.error(`[DEBUG] Không thể lấy tên người dùng từ Facebook response:`, userResponse);
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy tên người dùng từ Facebook',
        );
      }

      // Tính thời gian hết hạn
      const expirationDate = new Date();
      expirationDate.setSeconds(expirationDate.getSeconds() + expires_in);
      const expirationDateUnix = Math.floor(expirationDate.getTime());
      this.logger.log(`[DEBUG] Tính toán thời gian hết hạn:`, {
        expiresIn: expires_in,
        expirationDateUnix,
        expirationDate: expirationDate.toISOString(),
      });

      // Kiểm tra xem đã có tài khoản Facebook cá nhân này chưa
      this.logger.log(`[DEBUG] Kiểm tra tài khoản Facebook cá nhân đã tồn tại`);
      let facebookPersonal = await this.facebookPersonalRepository.findOne({
        where: { userId, facebookPersonalId },
      });
      this.logger.log(`[DEBUG] Kết quả tìm kiếm tài khoản:`, {
        found: !!facebookPersonal,
        existingId: facebookPersonal?.id,
      });

      if (facebookPersonal) {
        this.logger.log(`[DEBUG] Cập nhật thông tin tài khoản Facebook cá nhân đã tồn tại`);
        // Cập nhật thông tin
        facebookPersonal.accessToken = access_token;
        facebookPersonal.expirationDate = expirationDate;
        facebookPersonal.expirationDateUnix = expirationDateUnix;
        facebookPersonal.name = name; // Cập nhật tên người dùng
      } else {
        this.logger.log(`[DEBUG] Tạo mới tài khoản Facebook cá nhân`);
        // Tạo mới
        facebookPersonal = this.facebookPersonalRepository.create({
          userId,
          facebookPersonalId,
          name, // Thêm tên người dùng
          accessToken: access_token,
          expirationDate,
          expirationDateUnix,
        });
      }

      // Lưu vào database
      this.logger.log(`[DEBUG] Lưu tài khoản Facebook cá nhân vào database`);
      const facebookPersonalSaved =
        await this.facebookPersonalRepository.save(facebookPersonal);
      this.logger.log(`[DEBUG] Đã lưu tài khoản Facebook cá nhân:`, {
        id: facebookPersonalSaved.id,
        facebookPersonalId: facebookPersonalSaved.facebookPersonalId,
      });

      // Lấy danh sách trang Facebook
      this.logger.log(`[DEBUG] Bắt đầu đồng bộ danh sách trang Facebook`);
      const result = await this.syncFacebookPages(
        facebookPersonalSaved.id,
        access_token,
      );
      this.logger.log(`[DEBUG] Kết quả đồng bộ trang Facebook:`, result);

      return {
        pageCount: result.pageCount,
        pageSuccess: result.pageSuccess,
        pageError: result.pageError,
      };
    } catch (error) {
      this.logger.error(
        `Error handling Facebook callback: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `'Lỗi khi xử lý callback từ Facebook:' ${error.message}`,
      );
    }
  }

  /**
   * Đồng bộ danh sách trang Facebook
   * @param facebookPersonalId ID của tài khoản Facebook cá nhân
   * @param accessToken Access token của tài khoản Facebook
   * @returns Thông tin về số lượng trang, trang lỗi và trang thành công
   */
  private async syncFacebookPages(
    facebookPersonalId: string,
    accessToken: string,
  ): Promise<{
    pageCount: number;
    pageError?: string[];
    pageSuccess?: string[];
  }> {
    try {
      // Mảng lưu trữ kết quả
      const pageSuccess: string[] = [];
      const pageError: string[] = [];

      // Bước 1: Lấy dữ liệu cần thiết
      // Lấy danh sách trang Facebook từ API
      const pagesResponse: FacebookPageInfo[] =
        await this.facebookService.getUserAssignedPages(accessToken);

      if (!pagesResponse || pagesResponse.length === 0) {
        return { pageCount: 0 };
      }

      // Lấy danh sách ID trang Facebook từ API
      const apiPageIds = pagesResponse.map((page) => page.id);

      // Lấy tất cả các trang Facebook trong database có ID nằm trong danh sách từ API
      // Bao gồm cả các trang đã bị xóa mềm để có thể khôi phục nếu cần
      const allExistingPages =
        await this.facebookPageRepository.findAllByFacebookPageIds(
          apiPageIds,
          true,
        );

      // Bước 2: Phân loại các trang Facebook
      // Danh sách 1: Các trang của người dùng khác có trong API
      const otherUserPages = allExistingPages.filter(
        (page) =>
          page.facebookPersonalId !== facebookPersonalId &&
          apiPageIds.includes(page.facebookPageId),
      );

      // Danh sách 2: Các trang của người dùng hiện tại có trong API (bao gồm cả đã xóa mềm)
      const currentUserPages = allExistingPages.filter(
        (page) =>
          page.facebookPersonalId === facebookPersonalId &&
          apiPageIds.includes(page.facebookPageId),
      );

      // Tạo map để dễ dàng tìm kiếm trang hiện có của người dùng hiện tại
      const currentUserPagesMap = new Map<string, FacebookPage>();
      currentUserPages.forEach((page) => {
        currentUserPagesMap.set(page.facebookPageId, page);
      });

      // Danh sách 3: Các ID trang từ API chưa có trong database cho người dùng hiện tại
      const newPageIds = apiPageIds.filter(
        (id) => !currentUserPagesMap.has(id),
      );

      this.logger.log(`Tổng số trang từ API: ${apiPageIds.length}`);
      this.logger.log(`Số trang của người dùng khác: ${otherUserPages.length}`);
      this.logger.log(
        `Số trang của người dùng hiện tại: ${currentUserPages.length}`,
      );
      this.logger.log(`Số trang cần tạo mới: ${newPageIds.length}`);

      // Bước 3: Xử lý từng danh sách

      // Xử lý Danh sách 1: Các trang của người dùng khác
      try {
        // Lọc ra các trang đang active để unsubscribe
        const activePages = otherUserPages.filter((page) => page.isActive);

        // Unsubscribe các trang đang active
        if (activePages.length > 0) {
          this.logger.log(
            `Cần hủy đăng ký webhook cho ${activePages.length} trang của người dùng khác`,
          );

          // Xử lý unsubscribe (vẫn phải lặp vì cần gọi API riêng cho mỗi trang)
          for (const page of activePages) {
            try {
              await this.facebookService.unsubscribeApp(
                page.facebookPageId,
                page.pageAccessToken,
              );
              this.logger.log(
                `Đã hủy đăng ký webhook cho trang ${page.pageName} (ID: ${page.facebookPageId}) của người dùng khác`,
              );
            } catch (unsubError) {
              this.logger.error(
                `Lỗi khi hủy đăng ký webhook cho trang ${page.pageName}: ${unsubError.message}`,
                unsubError.stack,
              );
            }
          }
        }

        // Cập nhật hàng loạt tất cả các trang của người dùng khác
        if (otherUserPages.length > 0) {
          const pageIds = otherUserPages.map((page) => page.id);

          // Sử dụng một câu lệnh cập nhật hàng loạt
          await this.facebookPageRepository
            .createQueryBuilder()
            .update(FacebookPage)
            .set({
              isError: true,
              isActive: false,
            })
            .whereInIds(pageIds)
            .execute();

          this.logger.log(
            `Đã cập nhật trạng thái cho ${pageIds.length} trang của người dùng khác`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Lỗi khi xử lý các trang của người dùng khác: ${error.message}`,
          error.stack,
        );
      }

      // Xử lý Danh sách 2 và 3: Cập nhật các trang hiện có và tạo mới các trang chưa có
      // Tạo mảng để lưu trữ các trang cần cập nhật và tạo mới
      const pagesToUpdate: FacebookPage[] = [];
      const pagesToCreate: FacebookPage[] = [];
      const pageAvatarMap = new Map<string, string>(); // Map để lưu trữ avatar cho mỗi trang

      // Xử lý từng trang từ API
      for (const page of pagesResponse) {
        const { id, name, access_token } = page;

        try {
          // Kiểm tra xem trang này có trong danh sách trang hiện có của người dùng không
          const existingPage = currentUserPagesMap.get(id);

          if (existingPage) {
            // Lấy avatar và upload lên S3
            await this.facebookService.getPageAvatarAndUploadToS3(
              id,
              access_token,
              existingPage.avatarPage,
            );
            pageAvatarMap.set(id, existingPage.avatarPage);

            // Danh sách 2: Cập nhật thông tin
            existingPage.pageAccessToken = access_token;
            existingPage.pageName = name;
            existingPage.isActive = true;
            existingPage.isError = false;

            // Nếu trang đã bị xóa mềm trước đó, khôi phục lại
            if (existingPage.deletedAt !== null) {
              existingPage.deletedAt = null;
              existingPage.createdAt = Date.now();
              this.logger.log(
                `Khôi phục trang đã bị xóa mềm: ${name} (ID: ${id})`,
              );
            }

            pagesToUpdate.push(existingPage);
            pageSuccess.push(name);
          } else {
            // Lấy avatar và upload lên S3
            const { key } =
              await this.facebookService.getPageAvatarAndUploadToS3(
                id,
                access_token,
              );
            pageAvatarMap.set(id, key);

            // Danh sách 3: Tạo mới trang
            const newPage = new FacebookPage();
            newPage.facebookPageId = id;
            newPage.facebookPersonalId = facebookPersonalId;
            newPage.pageAccessToken = access_token;
            newPage.pageName = name;
            newPage.avatarPage = key;
            newPage.isActive = true;
            newPage.isError = false;
            newPage.deletedAt = null; // Đảm bảo trang mới không bị đánh dấu là đã xóa mềm

            pagesToCreate.push(newPage);
            pageSuccess.push(name);
          }
        } catch (error) {
          this.logger.error(
            `Lỗi khi xử lý trang ${id}: ${error.message}`,
            error.stack,
          );
          pageError.push(name);
        }
      }

      // Cập nhật hàng loạt các trang hiện có
      if (pagesToUpdate.length > 0) {
        try {
          await this.facebookPageRepository.save(pagesToUpdate);
          this.logger.log(
            `Đã cập nhật thông tin cho ${pagesToUpdate.length} trang hiện có`,
          );

          // Log chi tiết
          pagesToUpdate.forEach((page) => {
            this.logger.log(
              `Đã cập nhật thông tin cho trang ${page.pageName} (ID: ${page.facebookPageId})`,
            );
          });
        } catch (error) {
          this.logger.error(
            `Lỗi khi cập nhật hàng loạt các trang hiện có: ${error.message}`,
            error.stack,
          );
        }
      }

      // Tạo mới hàng loạt các trang
      if (pagesToCreate.length > 0) {
        try {
          await this.facebookPageRepository.save(pagesToCreate);
          this.logger.log(`Đã tạo mới ${pagesToCreate.length} trang`);

          // Log chi tiết
          pagesToCreate.forEach((page) => {
            this.logger.log(
              `Đã tạo mới trang ${page.pageName} (ID: ${page.facebookPageId})`,
            );
          });
        } catch (error) {
          this.logger.error(
            `Lỗi khi tạo mới hàng loạt các trang: ${error.message}`,
            error.stack,
          );
        }
      }

      return {
        pageCount: pagesResponse.length,
        pageSuccess: pageSuccess.length > 0 ? pageSuccess : undefined,
        pageError: pageError.length > 0 ? pageError : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi đồng bộ danh sách trang Facebook: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UPDATE_FAILED,
        'Lỗi khi đồng bộ danh sách trang Facebook',
      );
    }
  }

  /**
   * Xóa mềm một trang Facebook
   * @param userId ID của người dùng
   * @param pageId ID của trang Facebook
   */
  async deleteFacebookPage(userId: number, pageId: string): Promise<void> {
    try {
      // Tìm trang Facebook thuộc về người dùng
      const page = await this.facebookPageRepository.findPageByUserIdAndPageId(
        userId,
        pageId,
      );

      if (!page) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
          `Không tìm thấy trang Facebook với ID ${pageId}`,
        );
      }

      // Nếu trang đang active, thực hiện unsubscribe trước khi xóa
      if (page.isActive) {
        try {
          await this.facebookService.unsubscribeApp(
            page.facebookPageId,
            page.pageAccessToken,
          );
          this.logger.log(
            `Đã hủy đăng ký webhook cho trang ${page.pageName} (ID: ${page.facebookPageId})`,
          );
        } catch (unsubError) {
          this.logger.error(
            `Lỗi khi hủy đăng ký webhook cho trang ${page.pageName}: ${unsubError.message}`,
            unsubError.stack,
          );
          // Ghi log lỗi nhưng không throw exception để vẫn tiếp tục xóa trang
          // Nếu muốn dừng quá trình xóa khi unsubscribe thất bại, uncomment dòng dưới
          // throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UNSUBSCRIBE_FAILED);
        }
      }

      // Xóa mềm trang Facebook bằng cách cập nhật trường deletedAt
      page.deletedAt = Date.now();
      page.isActive = false; // Đảm bảo trang không còn active

      await this.facebookPageRepository.save(page);
      this.logger.log(
        `Đã xóa mềm trang Facebook ${page.pageName} (ID: ${page.facebookPageId})`,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi xóa trang Facebook: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_DELETE_FAILED,
        'Lỗi khi xóa trang Facebook',
      );
    }
  }

  /**
   * Xóa mềm nhiều trang Facebook cùng một lúc
   * @param userId ID của người dùng
   * @param pageIds Danh sách ID của các trang Facebook cần xóa
   * @returns Thông tin về số lượng trang đã xóa và danh sách trang lỗi
   */
  async deleteManyFacebookPages(
    userId: number,
    pageIds: string[],
  ): Promise<{
    deletedCount: number;
    errorPages?: string[];
  }> {
    try {
      if (!pageIds || pageIds.length === 0) {
        return { deletedCount: 0 };
      }

      // Tìm tất cả các trang Facebook thuộc về người dùng
      const pages =
        await this.facebookPageRepository.findPagesByUserIdAndPageIds(
          userId,
          pageIds,
        );

      if (pages.length === 0) {
        return { deletedCount: 0 };
      }

      this.logger.log(`Tìm thấy ${pages.length} trang Facebook cần xóa mềm`);

      // Lọc ra các trang đang active để unsubscribe
      const activePages = pages.filter((page) => page.isActive);
      const errorPages: string[] = [];

      // Unsubscribe các trang đang active
      if (activePages.length > 0) {
        this.logger.log(
          `Cần hủy đăng ký webhook cho ${activePages.length} trang trước khi xóa mềm`,
        );

        // Xử lý unsubscribe (vẫn phải lặp vì cần gọi API riêng cho mỗi trang)
        for (const page of activePages) {
          try {
            await this.facebookService.unsubscribeApp(
              page.facebookPageId,
              page.pageAccessToken,
            );
            this.logger.log(
              `Đã hủy đăng ký webhook cho trang ${page.pageName} (ID: ${page.facebookPageId})`,
            );
          } catch (unsubError) {
            this.logger.error(
              `Lỗi khi hủy đăng ký webhook cho trang ${page.pageName}: ${unsubError.message}`,
              unsubError.stack,
            );
            errorPages.push(page.pageName);
            // Vẫn tiếp tục xử lý các trang khác
          }
        }
      }

      // Xóa mềm tất cả các trang Facebook
      const deletedCount =
        await this.facebookPageRepository.softDeleteMany(pages);
      this.logger.log(`Đã xóa mềm ${deletedCount} trang Facebook`);

      return {
        deletedCount: pages.length,
        errorPages: errorPages.length > 0 ? errorPages : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa mềm nhiều trang Facebook: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_DELETE_FAILED,
        'Lỗi khi xóa mềm nhiều trang Facebook',
      );
    }
  }
}
