# Tests for Affiliate Registration State Machine

Thư mục này chứa các test cho module Affiliate Registration State Machine, tập trung vào việc kiểm tra các chức năng:
1. <PERSON><PERSON> lại hợp đồng sau khi bị từ chối
2. <PERSON><PERSON><PERSON> cấp từ tài khoản cá nhân lên doanh nghiệp

## Cấu trúc thư mục

```
tests/
├── affiliate-registration.service.spec.ts     # Unit tests cho service
├── affiliate-registration.controller.spec.ts  # Unit tests cho controller
├── affiliate-registration.state-machine.spec.ts # Unit tests cho state machine
├── affiliate-registration.integration.spec.ts # Integration tests
└── README.md                                  # Tài liệu hướng dẫn
```

## Các test case

### Service Tests
- Kiểm tra việc ký lại hợp đồng sau khi bị từ chối
  - Nên ném lỗi nếu không tìm thấy hợp đồng
  - Nên ném lỗi nếu hợp đồng không ở trạng thái bị từ chối
  - <PERSON>ên tạo hợp đồng mới và gửi sự kiện restart cho tài khoản cá nhân
  - Nên tạo hợp đồng mới và gửi sự kiện restart cho tài khoản doanh nghiệp

- Kiểm tra việc nâng cấp lên tài khoản doanh nghiệp
  - Nên ném lỗi nếu không tìm thấy tài khoản affiliate
  - Nên ném lỗi nếu tài khoản đã là loại doanh nghiệp
  - Nên ném lỗi nếu không tìm thấy hợp đồng
  - Nên ném lỗi nếu hợp đồng không ở trạng thái đã được phê duyệt
  - Nên tạo hợp đồng mới và gửi sự kiện nâng cấp

### Controller Tests
- Kiểm tra endpoint `POST /user/affiliate/registration/restart`
  - Nên gọi service.restartAfterRejection và trả về trạng thái hiện tại

- Kiểm tra endpoint `POST /user/affiliate/registration/upgrade-to-business`
  - Nên gọi service.upgradeToBusinessAccount và trả về trạng thái hiện tại

### State Machine Tests
- Kiểm tra chuyển đổi trạng thái cho hợp đồng bị từ chối
  - Nên chuyển từ rejected sang citizenIdUpload cho tài khoản cá nhân
  - Nên chuyển từ rejected sang businessLicenseUpload cho tài khoản doanh nghiệp

- Kiểm tra chuyển đổi trạng thái khi nâng cấp lên tài khoản doanh nghiệp
  - Nên chuyển từ approved sang infoInput khi nâng cấp lên tài khoản doanh nghiệp

### Integration Tests
- Kiểm tra endpoint `POST /user/affiliate/registration/restart`
  - Nên trả về phản hồi thành công với trạng thái mới

- Kiểm tra endpoint `POST /user/affiliate/registration/upgrade-to-business`
  - Nên trả về phản hồi thành công với trạng thái mới

## Cách chạy tests

### Chạy tất cả các test

```bash
npm run test -- src/modules/affiliate/state-machine/tests
```

### Chạy một file test cụ thể

```bash
npm run test -- src/modules/affiliate/state-machine/tests/affiliate-registration.service.spec.ts
```

### Chạy tests với coverage

```bash
npm run test:cov -- src/modules/affiliate/state-machine/tests
```

## Lưu ý

- Các test sử dụng Jest làm testing framework
- Các repository được mock để tránh phụ thuộc vào database
- JwtUserGuard được mock để tránh phụ thuộc vào authentication
- State machine được mock để kiểm tra các chuyển đổi trạng thái
