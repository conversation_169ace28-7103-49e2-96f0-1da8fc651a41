# Sơ đồ quy trình đăng ký Affiliate

## Quy trình đăng ký Affiliate cá nhân

```mermaid
stateDiagram-v2
    [*] --> selectAccountType
    selectAccountType --> termsAcceptance: SELECT_PERSONAL
    termsAcceptance --> personalInfo: ACCEPT_TERMS
    personalInfo --> signatureUpload: SUBMIT_INFO
    signatureUpload --> contractReview: SUBMIT_SIGNATURE
    contractReview --> otpVerification: PROCEED_TO_SIGN
    otpVerification --> pendingApproval: VERIFY_OTP
    pendingApproval --> approved: ADMIN_APPROVE
    pendingApproval --> rejected: ADMIN_REJECT
    approved --> [*]
    rejected --> [*]
```

## Mô tả các trạng thái

### 1. selectAccountType
- **Mô tả**: Người dùng chọn loại tài khoản affiliate (cá nhân hoặc doanh nghiệp)
- **Sự kiện**: 
  - `SELECT_PERSONAL`: Chọn tài khoản cá nhân
  - `SELECT_BUSINESS`: Chọn tài khoản doanh nghiệp
- **Dữ liệu lưu trữ**: 
  - `accountType`: Loại tài khoản đã chọn ('PERSONAL' hoặc 'BUSINESS')

### 2. termsAcceptance
- **Mô tả**: Người dùng xem và chấp nhận điều khoản và điều kiện
- **Sự kiện**: 
  - `ACCEPT_TERMS`: Chấp nhận điều khoản
- **Dữ liệu lưu trữ**: 
  - `termsAccepted`: Trạng thái chấp nhận điều khoản (true/false)
- **Thao tác database**:
  - Cập nhật `termsAccepted = true` trong bảng `affiliate_contracts`

### 3. personalInfo
- **Mô tả**: Người dùng nhập thông tin cá nhân
- **Sự kiện**: 
  - `SUBMIT_INFO`: Gửi thông tin cá nhân
- **Dữ liệu lưu trữ**: 
  - `userData`: Thông tin người dùng (fullName, email, address, phoneNumber, citizenId, dateOfBirth, citizenIssuePlace, citizenIssueDate)
  - `contractId`: ID của hợp đồng đã tạo
  - `contractPath`: Đường dẫn đến tài liệu hợp đồng
- **Thao tác database**:
  - Lưu thông tin người dùng vào database
  - Tạo hợp đồng và lưu đường dẫn

### 4. signatureUpload
- **Mô tả**: Người dùng tải lên chữ ký viết tay
- **Sự kiện**: 
  - `SUBMIT_SIGNATURE`: Gửi chữ ký
- **Dữ liệu lưu trữ**: 
  - `signatureUrl`: Đường dẫn đến hình ảnh chữ ký
- **Thao tác database**:
  - Lưu đường dẫn chữ ký vào database
  - Cập nhật `signMethod = 'ELECTRONIC'` trong bảng `affiliate_contracts`

### 5. contractReview
- **Mô tả**: Người dùng xem lại hợp đồng trước khi ký
- **Sự kiện**: 
  - `PROCEED_TO_SIGN`: Tiến hành ký hợp đồng
- **Dữ liệu lưu trữ**: Không có dữ liệu mới

### 6. otpVerification
- **Mô tả**: Người dùng xác thực OTP để ký hợp đồng
- **Sự kiện**: 
  - `VERIFY_OTP`: Xác thực OTP
- **Dữ liệu lưu trữ**: 
  - `otpVerified`: Trạng thái xác thực OTP (true/false)
- **Thao tác database**:
  - Cập nhật trạng thái hợp đồng thành `PENDING_REVIEW`
  - Tải hợp đồng lên cloud

### 7. pendingApproval
- **Mô tả**: Hợp đồng đang chờ phê duyệt từ admin
- **Sự kiện**: 
  - `ADMIN_APPROVE`: Admin phê duyệt
  - `ADMIN_REJECT`: Admin từ chối
- **Dữ liệu lưu trữ**: Không có dữ liệu mới

### 8. approved
- **Mô tả**: Hợp đồng đã được phê duyệt
- **Sự kiện**: Không có (trạng thái cuối)
- **Thao tác database**:
  - Cập nhật trạng thái hợp đồng thành `APPROVED`
  - Cập nhật trạng thái tài khoản affiliate thành `ACTIVE`
  - Gửi email thông báo phê duyệt

### 9. rejected
- **Mô tả**: Hợp đồng đã bị từ chối
- **Sự kiện**: Không có (trạng thái cuối)
- **Thao tác database**:
  - Cập nhật trạng thái hợp đồng thành `REJECTED`
  - Cập nhật trạng thái tài khoản affiliate thành `REJECTED`
  - Gửi email thông báo từ chối

## Quy trình đăng ký Affiliate doanh nghiệp

Quy trình đăng ký Affiliate doanh nghiệp tương tự như quy trình đăng ký cá nhân, nhưng có thêm một số trường thông tin doanh nghiệp như:
- Tên doanh nghiệp
- Mã số thuế
- Địa chỉ doanh nghiệp
- Người đại diện
- Chức vụ

```mermaid
stateDiagram-v2
    [*] --> selectAccountType
    selectAccountType --> termsAcceptance: SELECT_BUSINESS
    termsAcceptance --> businessInfo: ACCEPT_TERMS
    businessInfo --> representativeInfo: SUBMIT_BUSINESS_INFO
    representativeInfo --> signatureUpload: SUBMIT_REPRESENTATIVE_INFO
    signatureUpload --> contractReview: SUBMIT_SIGNATURE
    contractReview --> otpVerification: PROCEED_TO_SIGN
    otpVerification --> pendingApproval: VERIFY_OTP
    pendingApproval --> approved: ADMIN_APPROVE
    pendingApproval --> rejected: ADMIN_REJECT
    approved --> [*]
    rejected --> [*]
```

## Luồng dữ liệu

1. **Frontend** gửi yêu cầu chọn loại tài khoản (cá nhân/doanh nghiệp)
2. **Backend** khởi tạo máy trạng thái và lưu loại tài khoản
3. **Frontend** gửi yêu cầu chấp nhận điều khoản
4. **Backend** cập nhật trạng thái chấp nhận điều khoản trong database
5. **Frontend** gửi thông tin cá nhân/doanh nghiệp
6. **Backend** lưu thông tin và tạo hợp đồng
7. **Frontend** gửi chữ ký
8. **Backend** lưu chữ ký và cập nhật phương thức ký
9. **Frontend** gửi yêu cầu xem xét hợp đồng
10. **Backend** chuyển sang trạng thái xác thực OTP
11. **Frontend** gửi mã OTP để xác thực
12. **Backend** xác thực OTP, cập nhật trạng thái hợp đồng và tải lên cloud
13. **Admin** phê duyệt hoặc từ chối hợp đồng
14. **Backend** cập nhật trạng thái cuối cùng và gửi email thông báo

## Lưu ý triển khai

- Đảm bảo xử lý lỗi và rollback khi cần thiết
- Lưu log đầy đủ cho mỗi bước chuyển đổi trạng thái
- Cung cấp API để admin có thể xem danh sách các đơn đăng ký đang chờ phê duyệt
- Triển khai cơ chế nhắc nhở cho các đơn đăng ký chưa hoàn thành
- Đảm bảo bảo mật cho dữ liệu người dùng và hợp đồng
