import { Column, <PERSON>tity, Join<PERSON><PERSON>um<PERSON>, <PERSON>ToOne, PrimaryColumn } from 'typeorm';
import { AffiliateAccount } from './affiliate-account.entity';
import { ContractType } from '@modules/affiliate/enums';

/**
 * Entity đại diện cho bảng affiliate_withdraw_history trong cơ sở dữ liệu
 * Danh sách yêu cầu rút tiền của người dùng
 */
@Entity('affiliate_withdraw_history')
export class AffiliateWithdrawHistory {
  /**
   * ID của yêu cầu rút tiền
   */
  @PrimaryColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * <PERSON><PERSON><PERSON> hợp đồng
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: ContractType,
    comment: 'Loại hợp đồng'
  })
  type: ContractType;

  /**
   * ID tài khoản affiliate
   */
  @Column({
    name: 'affiliate_account_id',
    comment: 'Mã tài khoản affiliate'
  })
  affiliateAccountId: number;

  /**
   * <PERSON><PERSON> tiền rút
   */
  @Column({
    name: 'amount',
    type: 'float',
    comment: 'Số tiền rút'
  })
  amount: number;

  /**
   * Số dư trước giao dịch
   */
  @Column({
    name: 'balance_before',
    type: 'float',
    nullable: true,
    comment: 'Số dư trước giao dịch'
  })
  balanceBefore: number;

  /**
   * Số dư sau giao dịch
   */
  @Column({
    name: 'balance_after',
    type: 'float',
    nullable: true,
    comment: 'Số dư sau giao dịch'
  })
  balanceAfter: number;

  /**
   * Mã ngân hàng
   */
  @Column({
    name: 'bank_code',
    length: 20,
    comment: 'Mã ngân hàng'
  })
  bankCode: string;

  /**
   * Số tài khoản ngân hàng
   */
  @Column({
    name: 'account_number',
    length: 50,
    comment: 'Số tài khoản ngân hàng'
  })
  accountNumber: string;

  /**
   * Tên tài khoản ngân hàng
   */
  @Column({
    name: 'account_name',
    length: 255,
    comment: 'Tên tài khoản ngân hàng'
  })
  accountName: string;

  /**
   * Trạng thái yêu cầu
   */
  @Column({
    name: 'status',
    length: 20,
    comment: 'Trạng thái yêu cầu'
  })
  status: string; // Trạng thái yêu cầu ('PENDING', 'INVOICE_NOT_UPLOADED', 'REJECTED', 'PAID')

  /**
   * Thời gian tạo yêu cầu (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    comment: 'Thời gian tạo yêu cầu'
  })
  createdAt: number;

  /**
   * Thời gian kết thúc yêu cầu (Unix timestamp)
   */
  @Column({
    name: 'finish_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian kết thúc yêu cầu'
  })
  finishAt: number;

  /**
   * Tiền thuế VAT
   */
  @Column({
    name: 'vat_amount',
    type: 'float',
    comment: 'Tiền thuế VAT'
  })
  vatAmount: number;

  /**
   * Tiền thực nhận
   */
  @Column({
    name: 'net_payment',
    type: 'float',
    comment: 'Tiền thực nhận'
  })
  netPayment: number;

  /**
   * Lý do từ chối
   */
  @Column({
    name: 'reject_reason',
    length: 2000,
    nullable: true,
    comment: 'Lý do từ chối'
  })
  rejectReason: string;

  /**
   * Đường dẫn hóa đơn đầu vào
   */
  @Column({
    name: 'purchase_invoice',
    length: 255,
    nullable: true,
    comment: 'Đường dẫn hóa đơn đầu vào'
  })
  purchaseInvoice: string;
  
  /**
   * ID nhân viên xử lý
   */
  @Column({
    name: 'processed_by_employee_id',
    nullable: true,
    comment: 'ID nhân viên xử lý'
  })
  processedByEmployeeId: number;
}
