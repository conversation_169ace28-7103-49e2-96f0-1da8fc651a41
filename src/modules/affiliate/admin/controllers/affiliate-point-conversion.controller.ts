import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliatePointConversionService } from '../services';
import {
  AffiliatePointConversionQueryDto,
  AffiliatePointConversionDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';

@Controller('admin/affiliate/point-conversions')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_POINT_CONVERSION)
@ApiExtraModels(ApiResponseDto, AffiliatePointConversionDto)
export class AffiliatePointConversionController {
  constructor(
    private readonly affiliatePointConversionService: AffiliatePointConversionService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách lịch sử chuyển đổi điểm' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách lịch sử chuyển đổi điểm thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(AffiliatePointConversionDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy danh sách lịch sử chuyển đổi điểm',
  })
  async getPointConversions(
    @Query() queryDto: AffiliatePointConversionQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliatePointConversionDto>>> {
    const conversions = await this.affiliatePointConversionService.getPointConversions(
      queryDto,
    );
    return ApiResponseDto.success(
      conversions,
      'Lấy danh sách lịch sử chuyển đổi điểm thành công',
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết lịch sử chuyển đổi điểm' })
  @ApiParam({
    name: 'id',
    description: 'ID của bản ghi chuyển đổi',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết lịch sử chuyển đổi điểm thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliatePointConversionDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy lịch sử chuyển đổi điểm',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy chi tiết lịch sử chuyển đổi điểm',
  })
  async getPointConversionById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<AffiliatePointConversionDto>> {
    const conversion = await this.affiliatePointConversionService.getPointConversionById(
      id,
    );
    return ApiResponseDto.success(
      conversion,
      'Lấy chi tiết lịch sử chuyển đổi điểm thành công',
    );
  }
}
