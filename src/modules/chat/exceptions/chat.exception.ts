import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module chat
 */
export const CHAT_ERROR_CODES = {
  AGENT_NOT_FOUND: new ErrorCode(
    10401,
    'Không tìm thấy agent',
    HttpStatus.NOT_FOUND,
  ),
  RUN_CREATION_FAILED: new ErrorCode(
    10402,
    'Tạo run chat thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVALID_AGENT_TYPE: new ErrorCode(
    10403,
    'Loại agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  AGENT_ACCESS_DENIED: new ErrorCode(
    10404,
    'Không có quyền truy cập agent',
    HttpStatus.FORBIDDEN,
  ),
  INVALID_THREAD_ID: new ErrorCode(
    10405,
    'threadId không đúng định dạng',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_RUN_ID: new ErrorCode(
    10405,
    'runId không đúng định dạng',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_PROVIDER: new ErrorCode(
    10405,
    'lỗi xử lý tên nhà cung cấp',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  TOO_MANY_SUPERVISORS: new ErrorCode(
    10405,
    'Không được có nhiều hơn 1 supervisor',
    HttpStatus.BAD_REQUEST,
  ),
  MESSAGE_PROCESSING_FAILED: new ErrorCode(
    10405,
    'Xử lý message thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
