import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để cập nhật cột data_type từ varchar thành enum
 * trong bảng audience_user_custom_fields và audience_admin_custom_fields
 */
export class UpdateCustomFieldDataTypeToEnum1720000000006 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum type cho CustomFieldDataType
    await queryRunner.query(`
      CREATE TYPE "custom_field_data_type_enum" AS ENUM (
        'text', 
        'number', 
        'boolean', 
        'date', 
        'select', 
        'object'
      )
    `);

    // Cập nhật dữ liệu hiện có để map từ giá trị cũ sang giá trị mới
    // string -> text
    await queryRunner.query(`
      UPDATE "audience_user_custom_fields" 
      SET "data_type" = 'text' 
      WHERE "data_type" = 'string'
    `);

    await queryRunner.query(`
      UPDATE "audience_admin_custom_fields" 
      SET "data_type" = 'text' 
      WHERE "data_type" = 'string'
    `);

    // integer -> number
    await queryRunner.query(`
      UPDATE "audience_user_custom_fields" 
      SET "data_type" = 'number' 
      WHERE "data_type" = 'integer'
    `);

    await queryRunner.query(`
      UPDATE "audience_admin_custom_fields" 
      SET "data_type" = 'number' 
      WHERE "data_type" = 'integer'
    `);

    // json -> object
    await queryRunner.query(`
      UPDATE "audience_user_custom_fields" 
      SET "data_type" = 'object' 
      WHERE "data_type" = 'json'
    `);

    await queryRunner.query(`
      UPDATE "audience_admin_custom_fields" 
      SET "data_type" = 'object' 
      WHERE "data_type" = 'json'
    `);

    // Cập nhật cột data_type trong bảng audience_user_custom_fields
    await queryRunner.query(`
      ALTER TABLE "audience_user_custom_fields" 
      ALTER COLUMN "data_type" TYPE "custom_field_data_type_enum" 
      USING "data_type"::"custom_field_data_type_enum"
    `);

    // Cập nhật cột data_type trong bảng audience_admin_custom_fields
    await queryRunner.query(`
      ALTER TABLE "audience_admin_custom_fields" 
      ALTER COLUMN "data_type" TYPE "custom_field_data_type_enum" 
      USING "data_type"::"custom_field_data_type_enum"
    `);

    // Thêm comment cho enum type
    await queryRunner.query(`
      COMMENT ON TYPE "custom_field_data_type_enum" 
      IS 'Enum cho các kiểu dữ liệu của trường tùy chỉnh: text, number, boolean, date, select, object'
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Chuyển đổi cột data_type về varchar
    await queryRunner.query(`
      ALTER TABLE "audience_user_custom_fields" 
      ALTER COLUMN "data_type" TYPE VARCHAR(50)
    `);

    await queryRunner.query(`
      ALTER TABLE "audience_admin_custom_fields" 
      ALTER COLUMN "data_type" TYPE VARCHAR(50)
    `);

    // Cập nhật dữ liệu về giá trị cũ
    // text -> string
    await queryRunner.query(`
      UPDATE "audience_user_custom_fields" 
      SET "data_type" = 'string' 
      WHERE "data_type" = 'text'
    `);

    await queryRunner.query(`
      UPDATE "audience_admin_custom_fields" 
      SET "data_type" = 'string' 
      WHERE "data_type" = 'text'
    `);

    // number -> integer
    await queryRunner.query(`
      UPDATE "audience_user_custom_fields" 
      SET "data_type" = 'integer' 
      WHERE "data_type" = 'number'
    `);

    await queryRunner.query(`
      UPDATE "audience_admin_custom_fields" 
      SET "data_type" = 'integer' 
      WHERE "data_type" = 'number'
    `);

    // object -> json
    await queryRunner.query(`
      UPDATE "audience_user_custom_fields" 
      SET "data_type" = 'json' 
      WHERE "data_type" = 'object'
    `);

    await queryRunner.query(`
      UPDATE "audience_admin_custom_fields" 
      SET "data_type" = 'json' 
      WHERE "data_type" = 'object'
    `);

    // Xóa enum type
    await queryRunner.query(`DROP TYPE "custom_field_data_type_enum"`);
  }
}
