import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm cột country_code vào bảng users
 * Cột này sẽ lưu mã quốc gia của số điện thoại người dùng
 */
export class AddCountryCodeToUsers1720000000004 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột country_code vào bảng users
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN IF NOT EXISTS "country_code" VARCHAR(10) DEFAULT '+84'
    `);

    // Thêm comment cho cột
    await queryRunner.query(`
      COMMENT ON COLUMN "users"."country_code" 
      IS 'Mã quốc gia của số điện thoại người dùng (ví dụ: +84, +1, +86)'
    `);

    // Cập nhật dữ liệu hiện có với giá trị mặc định
    await queryRunner.query(`
      UPDATE "users" 
      SET "country_code" = '+84' 
      WHERE "country_code" IS NULL
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột country_code
    await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN IF EXISTS "country_code"
    `);
  }
}
