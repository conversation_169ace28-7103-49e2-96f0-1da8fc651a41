-- Rollback Migration: Remove country_code field from audience tables
-- Date: 2024-12-19
-- Description: Xóa trường country_code khỏi bảng user_audience và admin_audience

-- Kiểm tra và xóa cột country_code từ bảng user_audience
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user_audience' 
        AND column_name = 'country_code'
    ) THEN
        -- Xóa cột country_code từ user_audience
        ALTER TABLE user_audience DROP COLUMN country_code;
        RAISE NOTICE 'Đã xóa cột country_code khỏi bảng user_audience';
    ELSE
        RAISE NOTICE 'Cột country_code không tồn tại trong bảng user_audience';
    END IF;
END $$;

-- Kiểm tra và xóa cột country_code từ bảng admin_audience
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'admin_audience' 
        AND column_name = 'country_code'
    ) THEN
        -- <PERSON><PERSON>a cột country_code từ admin_audience
        ALTER TABLE admin_audience DROP COLUMN country_code;
        RAISE NOTICE 'Đã xóa cột country_code khỏi bảng admin_audience';
    ELSE
        RAISE NOTICE 'Cột country_code không tồn tại trong bảng admin_audience';
    END IF;
END $$;

-- Kiểm tra kết quả cho user_audience
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'user_audience' 
AND column_name = 'country_code';

-- Kiểm tra kết quả cho admin_audience
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'admin_audience' 
AND column_name = 'country_code';
