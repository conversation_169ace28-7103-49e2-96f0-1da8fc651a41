-- Rollback Migration: Revert data_type column from enum to varchar
-- Date: 2024-12-19
-- Description: Rollback cột data_type từ enum về varchar trong bảng audience_user_custom_fields và audience_admin_custom_fields

-- <PERSON><PERSON><PERSON><PERSON> đổi cột data_type về varchar
ALTER TABLE audience_user_custom_fields 
ALTER COLUMN data_type TYPE VARCHAR(50);

ALTER TABLE audience_admin_custom_fields 
ALTER COLUMN data_type TYPE VARCHAR(50);

-- Cập nhật dữ liệu về giá trị cũ
-- text -> string
UPDATE audience_user_custom_fields 
SET data_type = 'string' 
WHERE data_type = 'text';

UPDATE audience_admin_custom_fields 
SET data_type = 'string' 
WHERE data_type = 'text';

-- number -> integer
UPDATE audience_user_custom_fields 
SET data_type = 'integer' 
WHERE data_type = 'number';

UPDATE audience_admin_custom_fields 
SET data_type = 'integer' 
WHERE data_type = 'number';

-- object -> json
UPDATE audience_user_custom_fields 
SET data_type = 'json' 
WHERE data_type = 'object';

UPDATE audience_admin_custom_fields 
SET data_type = 'json' 
WHERE data_type = 'object';

-- Xóa enum type
DROP TYPE IF EXISTS custom_field_data_type_enum;

-- Kiểm tra kết quả cho audience_user_custom_fields
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'audience_user_custom_fields' 
AND column_name = 'data_type';

-- Kiểm tra kết quả cho audience_admin_custom_fields
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'audience_admin_custom_fields' 
AND column_name = 'data_type';
