import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm cột country_code vào bảng user_audience và admin_audience
 * Cột này sẽ lưu mã quốc gia của số điện thoại khách hàng
 */
export class AddCountryCodeToAudienceTables1720000000005 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột country_code vào bảng user_audience
    await queryRunner.query(`
      ALTER TABLE "user_audience" 
      ADD COLUMN IF NOT EXISTS "country_code" VARCHAR(10) DEFAULT '+84'
    `);

    // Thêm comment cho cột user_audience
    await queryRunner.query(`
      COMMENT ON COLUMN "user_audience"."country_code" 
      IS 'Mã quốc gia của số điện thoạ<PERSON> kh<PERSON>ch hàng (ví dụ: +84, +1, +86)'
    `);

    // Thêm cột country_code vào bảng admin_audience
    await queryRunner.query(`
      ALTER TABLE "admin_audience" 
      ADD COLUMN IF NOT EXISTS "country_code" VARCHAR(10) DEFAULT '+84'
    `);

    // Thêm comment cho cột admin_audience
    await queryRunner.query(`
      COMMENT ON COLUMN "admin_audience"."country_code" 
      IS 'Mã quốc gia của số điện thoại khách hàng (ví dụ: +84, +1, +86)'
    `);

    // Cập nhật dữ liệu hiện có với giá trị mặc định cho user_audience
    await queryRunner.query(`
      UPDATE "user_audience" 
      SET "country_code" = '+84' 
      WHERE "country_code" IS NULL
    `);

    // Cập nhật dữ liệu hiện có với giá trị mặc định cho admin_audience
    await queryRunner.query(`
      UPDATE "admin_audience" 
      SET "country_code" = '+84' 
      WHERE "country_code" IS NULL
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột country_code từ bảng user_audience
    await queryRunner.query(`
      ALTER TABLE "user_audience" 
      DROP COLUMN IF EXISTS "country_code"
    `);

    // Xóa cột country_code từ bảng admin_audience
    await queryRunner.query(`
      ALTER TABLE "admin_audience" 
      DROP COLUMN IF EXISTS "country_code"
    `);
  }
}
