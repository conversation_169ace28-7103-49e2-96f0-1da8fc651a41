-- Migration: Add country_code field to audience tables
-- Date: 2024-12-19
-- Description: Thêm trường country_code vào bảng user_audience và admin_audience để lưu mã quốc gia của số điện thoại

-- Thêm cột country_code vào bảng user_audience
ALTER TABLE user_audience 
ADD COLUMN IF NOT EXISTS country_code VARCHAR(10) DEFAULT '+84';

-- Thêm comment cho cột user_audience
COMMENT ON COLUMN user_audience.country_code 
IS 'Mã quốc gia của số điện thoại khách hàng (ví dụ: +84, +1, +86)';

-- Thêm cột country_code vào bảng admin_audience
ALTER TABLE admin_audience 
ADD COLUMN IF NOT EXISTS country_code VARCHAR(10) DEFAULT '+84';

-- Thêm comment cho cột admin_audience
COMMENT ON COLUMN admin_audience.country_code 
IS 'Mã quốc gia của số điện thoạ<PERSON> kh<PERSON>ch hàng (ví dụ: +84, +1, +86)';

-- <PERSON><PERSON><PERSON> nhật dữ liệu hiện có với giá trị mặc định cho user_audience
UPDATE user_audience 
SET country_code = '+84' 
WHERE country_code IS NULL;

-- Cập nhật dữ liệu hiện có với giá trị mặc định cho admin_audience
UPDATE admin_audience 
SET country_code = '+84' 
WHERE country_code IS NULL;

-- Kiểm tra kết quả cho user_audience
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'user_audience' 
AND column_name = 'country_code';

-- Kiểm tra kết quả cho admin_audience
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'admin_audience' 
AND column_name = 'country_code';
