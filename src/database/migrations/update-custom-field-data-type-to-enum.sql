-- Migration: Update data_type column from varchar to enum
-- Date: 2024-12-19
-- Description: C<PERSON><PERSON> nhật cột data_type từ varchar thành enum trong bảng audience_user_custom_fields và audience_admin_custom_fields

-- Tạo enum type cho CustomFieldDataType
CREATE TYPE custom_field_data_type_enum AS ENUM (
    'text', 
    'number', 
    'boolean', 
    'date', 
    'select', 
    'object'
);

-- Thêm comment cho enum type
COMMENT ON TYPE custom_field_data_type_enum 
IS 'Enum cho các kiểu dữ liệu của trường tùy chỉnh: text, number, boolean, date, select, object';

-- Cập nhật dữ liệu hiện có để map từ giá trị cũ sang giá trị mới
-- string -> text
UPDATE audience_user_custom_fields 
SET data_type = 'text' 
WHERE data_type = 'string';

UPDATE audience_admin_custom_fields 
SET data_type = 'text' 
WHERE data_type = 'string';

-- integer -> number
UPDATE audience_user_custom_fields 
SET data_type = 'number' 
WHERE data_type = 'integer';

UPDATE audience_admin_custom_fields 
SET data_type = 'number' 
WHERE data_type = 'integer';

-- json -> object
UPDATE audience_user_custom_fields 
SET data_type = 'object' 
WHERE data_type = 'json';

UPDATE audience_admin_custom_fields 
SET data_type = 'object' 
WHERE data_type = 'json';

-- Cập nhật cột data_type trong bảng audience_user_custom_fields
ALTER TABLE audience_user_custom_fields 
ALTER COLUMN data_type TYPE custom_field_data_type_enum 
USING data_type::custom_field_data_type_enum;

-- Cập nhật cột data_type trong bảng audience_admin_custom_fields
ALTER TABLE audience_admin_custom_fields 
ALTER COLUMN data_type TYPE custom_field_data_type_enum 
USING data_type::custom_field_data_type_enum;

-- Kiểm tra kết quả cho audience_user_custom_fields
SELECT 
    column_name, 
    data_type, 
    udt_name,
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'audience_user_custom_fields' 
AND column_name = 'data_type';

-- Kiểm tra kết quả cho audience_admin_custom_fields
SELECT 
    column_name, 
    data_type, 
    udt_name,
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'audience_admin_custom_fields' 
AND column_name = 'data_type';

-- Kiểm tra enum values
SELECT 
    t.typname,
    e.enumlabel
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname = 'custom_field_data_type_enum'
ORDER BY e.enumsortorder;
