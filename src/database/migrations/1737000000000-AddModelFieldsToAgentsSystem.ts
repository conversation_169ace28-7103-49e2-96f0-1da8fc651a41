import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm các cột model_base_id, model_finetuning_id và role_id vào bảng agents_system
 * Cùng với các foreign key constraints tương ứng
 */
export class AddModelFieldsToAgentsSystem1737000000000 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột role_id (nếu chưa có)
    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "role_id" UUID NOT NULL
    `);

    // Thêm cột model_base_id
    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "model_base_id" UUID NULL
    `);

    // Thêm cột model_finetuning_id
    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "model_finetuning_id" UUID NULL
    `);

    // Thêm cột prompt_task (nếu chưa có)
    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "prompt_task" TEXT NULL
    `);

    // Thêm comment cho các cột
    await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."role_id" 
      IS 'UUID của vai trò liên kết - Mỗi agent system chỉ có 1 role duy nhất'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."model_base_id" 
      IS 'UUID tham chiếu đến bảng base_models - ID của model base (nếu có)'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."model_finetuning_id" 
      IS 'UUID tham chiếu đến bảng fine_tuning_models - ID của fine-tuning model (nếu có)'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."prompt_task" 
      IS 'Prompt cho task'
    `);

    // Thêm foreign key constraint cho model_base_id
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_base_models_id_fk"
      FOREIGN KEY ("model_base_id") REFERENCES "base_models"("id")
      ON DELETE SET NULL
    `);

    // Thêm foreign key constraint cho model_finetuning_id
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_fine_tuning_models_id_fk"
      FOREIGN KEY ("model_finetuning_id") REFERENCES "fine_tuning_models"("id")
      ON DELETE SET NULL
    `);

    // Thêm foreign key constraint cho role_id
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_agent_roles_id_fk"
      FOREIGN KEY ("role_id") REFERENCES "agent_roles"("id")
      ON DELETE CASCADE
    `);

    // Thêm composite primary key (id, role_id)
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_pkey"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_pkey"
      PRIMARY KEY ("id", "role_id")
    `);

    // Thêm index cho các cột foreign key
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_agents_system_model_base_id"
      ON "agents_system" ("model_base_id")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_agents_system_model_finetuning_id"
      ON "agents_system" ("model_finetuning_id")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_agents_system_role_id"
      ON "agents_system" ("role_id")
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_agents_system_role_id"
    `);

    await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_agents_system_model_finetuning_id"
    `);

    await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_agents_system_model_base_id"
    `);

    // Xóa composite primary key
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_pkey"
    `);

    // Khôi phục primary key cũ
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_pkey"
      PRIMARY KEY ("id")
    `);

    // Xóa các foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_agent_roles_id_fk"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_fine_tuning_models_id_fk"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_base_models_id_fk"
    `);

    // Xóa các cột
    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "prompt_task"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "model_finetuning_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "model_base_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "role_id"
    `);
  }
}
