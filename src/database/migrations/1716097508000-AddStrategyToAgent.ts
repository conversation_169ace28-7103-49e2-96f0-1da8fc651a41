import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm cột strategy_id và strategy_config vào bảng agents
 */
export class AddStrategyToAgent1716097508000 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột strategy_id
    await queryRunner.query(`
      ALTER TABLE "agents" 
      ADD COLUMN IF NOT EXISTS "strategy_id" VARCHAR(100) NULL
    `);

    // Thêm cột strategy_config
    await queryRunner.query(`
      ALTER TABLE "agents" 
      ADD COLUMN IF NOT EXISTS "strategy_config" JSONB NULL DEFAULT '{}'
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // X<PERSON>a cột strategy_config
    await queryRunner.query(`
      ALTER TABLE "agents" 
      DROP COLUMN IF EXISTS "strategy_config"
    `);

    // Xóa cột strategy_id
    await queryRunner.query(`
      ALTER TABLE "agents" 
      DROP COLUMN IF EXISTS "strategy_id"
    `);
  }
}
