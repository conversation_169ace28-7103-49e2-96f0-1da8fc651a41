import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm cột updated_at vào bảng rule_contract
 * Cột này sẽ lưu thời gian cập nhật hợp đồng (Unix timestamp)
 */
export class AddUpdatedAtToRuleContract1720000000003 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột updated_at vào bảng rule_contract
    await queryRunner.query(`
      ALTER TABLE "rule_contract" 
      ADD COLUMN IF NOT EXISTS "updated_at" bigint
    `);

    // Thêm comment cho cột
    await queryRunner.query(`
      COMMENT ON COLUMN "rule_contract"."updated_at" 
      IS 'Thời gian cập nhật hợp đồng (Unix timestamp)'
    `);

    // Cập nhật giá trị updated_at cho các bản ghi hiện tại
    // <PERSON><PERSON> dụng created_at làm giá trị mặc định nếu có, hoặc thời gian hiện tại
    await queryRunner.query(`
      UPDATE "rule_contract" 
      SET "updated_at" = COALESCE("created_at", EXTRACT(EPOCH FROM NOW()) * 1000)
      WHERE "updated_at" IS NULL
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột updated_at
    await queryRunner.query(`
      ALTER TABLE "rule_contract" 
      DROP COLUMN IF EXISTS "updated_at"
    `);
  }
}
