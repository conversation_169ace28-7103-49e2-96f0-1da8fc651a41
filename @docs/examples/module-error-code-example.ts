import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Example (14000-14099)
 * Đ<PERSON>y là ví dụ về cách tổ chức mã lỗi cho một module
 */
export const EXAMPLE_MODULE_ERROR_CODES = {
  /**
   * Lỗi khi không tìm thấy tài nguyên
   */
  RESOURCE_NOT_FOUND: new ErrorCode(
    14000,
    'Không tìm thấy tài nguyên',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tài nguyên đã tồn tại
   */
  RESOURCE_ALREADY_EXISTS: new ErrorCode(
    14001,
    'Tài nguyên đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu không hợp lệ
   */
  INVALID_DATA: new ErrorCode(
    14002,
    'Dữ liệu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo tài nguyên thất bại
   */
  RESOURCE_CREATION_FAILED: new ErrorCode(
    14003,
    'Tạo tài nguyên thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật tài nguyên thất bại
   */
  RESOURCE_UPDATE_FAILED: new ErrorCode(
    14004,
    'Cập nhật tài nguyên thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa tài nguyên thất bại
   */
  RESOURCE_DELETION_FAILED: new ErrorCode(
    14005,
    'Xóa tài nguyên thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};

// Ví dụ về file index.ts trong thư mục exceptions
// export * from './example-module-error.code';

// Ví dụ về cách sử dụng trong service
/*
import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { EXAMPLE_MODULE_ERROR_CODES } from './exceptions';

@Injectable()
export class ExampleService {
  async findResource(id: number): Promise<Resource> {
    const resource = await this.resourceRepository.findOne({ where: { id } });

    if (!resource) {
      throw new AppException(
        EXAMPLE_MODULE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy tài nguyên với ID ${id}`
      );
    }

    return resource;
  }

  async createResource(data: CreateResourceDto): Promise<Resource> {
    try {
      // Kiểm tra tài nguyên đã tồn tại chưa
      const existingResource = await this.resourceRepository.findOne({
        where: { name: data.name }
      });

      if (existingResource) {
        throw new AppException(
          EXAMPLE_MODULE_ERROR_CODES.RESOURCE_ALREADY_EXISTS,
          `Tài nguyên với tên ${data.name} đã tồn tại`
        );
      }

      // Tạo tài nguyên mới
      const newResource = this.resourceRepository.create(data);
      return await this.resourceRepository.save(newResource);
    } catch (error) {
      // Nếu đã là AppException thì không cần wrap lại
      if (error instanceof AppException) {
        throw error;
      }

      // Wrap lỗi trong AppException
      throw new AppException(
        EXAMPLE_MODULE_ERROR_CODES.RESOURCE_CREATION_FAILED,
        'Không thể tạo tài nguyên',
        { originalError: error.message }
      );
    }
  }
}
*/
