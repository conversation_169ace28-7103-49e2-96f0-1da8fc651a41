# Kế hoạch Triển khai Frontend & Đấu nối <PERSON>end (1 Tuần)

**Dự án:** Xây dựng giao diện người dùng (Frontend) và tích hợp với Backend API.
**Thời gian:** 1 Tuần (7 ngày)
**<PERSON><PERSON><PERSON> bắt đầu (dự kiến):** [<PERSON><PERSON><PERSON> bắt đầu]
**<PERSON><PERSON><PERSON> kết thúc (dự kiến):** [Ng<PERSON><PERSON> kết thúc]

**LƯU Ý QUAN TRỌNG:** Khung thời gian **1 tuần** là **cực kỳ thách thức** đối với phạm vi công việc được yêu cầu. Kế hoạch này đòi hỏi hiệu suất cao, API backend ổn định và sẵn sàng, đồng thời có thể cần phải **ưu tiên các tính năng cốt lõi** và **sử dụng mạnh mẽ các thư viện UI có sẵn**.

---

## I. Điều kiện tiên quyết

* **Thiết kế UI/UX:** Đã có sẵn wireframe hoặc bản thiết kế chi tiết cho tất cả các trang và component.
* **API Backend:**
    * Các API endpoints đã được định nghĩa rõ ràng (contract).
    * API đã sẵn sàng để tích hợp hoặc đang được phát triển song song với đặc tả ổn định.
    * Có tài liệu API (Swagger hoặc tương tự).
* **Môi trường:** Đã cài đặt môi trường phát triển cần thiết (Node.js, npm/yarn, Git).
* **Framework/Library:** Đã quyết định framework/library chính (React, Vue, Angular...).
* **Nhân lực:** (Giả định ban đầu là 1 lập trình viên frontend, nhưng **khuyến nghị** có thêm người nếu muốn đảm bảo tiến độ).

---

## II. Các Component Dùng chung (Gợi ý Bổ sung)

Ngoài các component bạn đã yêu cầu (`Cart Overview`, `Table` nâng cao, `Biểu đồ cột`, `Biểu đồ tròn`), cần xem xét xây dựng/sử dụng thêm:

* `Button`: Các biến thể (primary, secondary, danger, link...).
* `Input Fields`: Text, password, select, checkbox, radio, date picker...
* `Modal/Dialog`: Hiển thị form, xác nhận, thông báo.
* `Loader/Spinner`: Chỉ báo trạng thái tải.
* `Notifications/Toast`: Thông báo trạng thái (success, error, warning).
* `Layout Structure`: `Header`, `Footer`, `Sidebar`, `Main Content Area`.
* `Form Wrapper`: Quản lý state, validation.
* `Pagination`: Component phân trang (có thể tích hợp trong Table).
* `Search/Filter Bar`: Thanh tìm kiếm/lọc chung.
* `Authentication Components`: `LoginForm` (đã có `ForgotPassword`).

**Khuyến nghị:** **Ưu tiên sử dụng một thư viện UI component có sẵn** (Material UI, Ant Design, Chakra UI, Bootstrap...) để tăng tốc độ phát triển các component này.

---

## III. Kế hoạch Chi tiết theo Ngày

### Ngày 1: Nền tảng & Components Cốt lõi

* **Mục tiêu:** Thiết lập dự án, layout chính, component phức tạp.
* **Nhiệm vụ:**
    * Khởi tạo dự án (`create-react-app`, `vue create`, `ng new`...).
    * Cài đặt thư viện: router, state management (Redux, Vuex, Zustand...), UI library, `axios`.
    * Xây dựng cấu trúc thư mục dự án.
    * Thiết lập routing cơ bản.
    * Xây dựng Layout chính (`Header`, `Footer`, `Sidebar`...).
    * Bắt đầu xây dựng **Component `Table`**: Tập trung hiển thị dữ liệu, sắp xếp, phân trang cơ bản.
    * Xây dựng **Component `Cart Overview`** (UI).
    * Thiết lập `axios instance` hoặc `Workspace wrapper` chung để gọi API.

### Ngày 2: Hoàn thiện Components & Trang Cơ bản

* **Mục tiêu:** Hoàn thiện components, xây dựng trang đơn giản, bắt đầu tích hợp API.
* **Nhiệm vụ:**
    * Hoàn thiện **Component `Table`** (thêm tùy chỉnh cột nếu kịp).
    * Xây dựng **Component Biểu đồ cột & tròn** (sử dụng thư viện).
    * Xây dựng các component cơ bản khác (`Button`, `Input`, `Modal`...).
    * Xây dựng **Trang Blog** (Danh sách, Chi tiết - UI).
    * Xây dựng **Trang Quên mật khẩu** (UI + Logic cơ bản).
    * **Đấu nối API:** Blog (lấy danh sách/chi tiết), Quên mật khẩu.

### Ngày 3: Xây dựng Trang User & Đấu nối API

* **Mục tiêu:** Tập trung vào các trang chức năng phía người dùng.
* **Nhiệm vụ:**
    * Xây dựng **Trang Marketing** (UI).
    * Xây dựng **Trang Tích hợp** (UI).
    * Xây dựng **Trang Thông tin tài khoản Affiliate** (UI).
    * **Đấu nối API:** Gọi API tương ứng cho các trang trên (nếu có).
    * Tích hợp các component dùng chung (`Table`, `Chart`...) nếu cần.

### Ngày 4: Trang Quản lý & Trang User phức tạp

* **Mục tiêu:** Xây dựng trang quản lý và các trang user nghiệp vụ phức tạp hơn.
* **Nhiệm vụ:**
    * Xây dựng **Trang Quản lý Blog (Admin)** (UI cho CRUD).
    * Xây dựng **Trang Quản lý Tài khoản (Admin)** (UI).
    * Xây dựng **Trang Quản lý Audience (User)** (UI).
    * Xây dựng **Trang Quản lý Segment (User)** (UI).
    * Xây dựng **Trang Quản lý Tag (User)** (UI).
    * **Đấu nối API:** CRUD Quản lý Blog, Quản lý Tài khoản.

### Ngày 5: Hoàn thiện Trang User/Admin (Audience, Segment, Tag) & Đấu nối

* **Mục tiêu:** Hoàn thiện các trang quản lý Audience, Segment, Tag (User & Admin).
* **Nhiệm vụ:**
    * Hoàn thiện UI còn lại cho các trang quản lý Audience, Segment, Tag (User & Admin).
    * **Đấu nối API:** CRUD cho Audience, Segment, Tag (User & Admin). Tích hợp `Table` component.

### Ngày 6: Trang Agent & Rà soát

* **Mục tiêu:** Xây dựng trang Agent, rà soát và sửa lỗi cơ bản.
* **Nhiệm vụ:**
    * Xây dựng **Trang Danh sách Agent** (UI + API).
    * Xây dựng **Trang Chi tiết Agent** (UI + API).
    * Rà soát các luồng hoạt động chính trên các trang đã xây dựng.
    * Kiểm tra tích hợp component dùng chung.
    * Fix các bug cơ bản.

### Ngày 7: Tích hợp cuối, Kiểm thử & Hoàn thiện

* **Mục tiêu:** Đảm bảo tích hợp API, kiểm thử luồng chính, fix bug, chuẩn bị bàn giao.
* **Nhiệm vụ:**
    * Kiểm tra lại toàn bộ việc đấu nối API, đảm bảo dữ liệu hoạt động đúng.
    * Kiểm thử thủ công (manual testing) các luồng người dùng quan trọng.
    * Fix các bug nghiêm trọng được phát hiện.
    * Tối ưu hóa cơ bản (nếu còn thời gian).
    * Build thử dự án (`npm run build` / `yarn build`).
    * Chuẩn bị bàn giao (code, tài liệu nếu có).