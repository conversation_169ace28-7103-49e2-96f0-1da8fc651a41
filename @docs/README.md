# Tài liệu hệ thống RedAI Backend

## Tổng quan

Hệ thống RedAI Backend là một nền tảng xây dựng trên NestJS, cung cấp các API và dịch vụ cho ứng dụng RedAI. Hệ thống được thiết kế theo kiến trúc module, với mỗi module đảm nhận một chức năng cụ thể của ứng dụng.

## Cấu trúc hệ thống

```
redai-v201-be-app/
├── src/
│   ├── modules/           # Các module chức năng
│   ├── common/            # Các thành phần dùng chung
│   ├── shared/            # Các dịch vụ và tiện ích dùng chung
│   ├── config/            # Cấu hình hệ thống
│   └── app.module.ts      # Module chính của ứng dụng
├── @docs/                 # Tài liệu tổng quan
└── docs/                  # Tài liệu API
```

## Các module ch<PERSON>h

<PERSON>ệ thống bao gồm các module chính sau:

1. [**Agent Module**](../src/modules/agent/README.md) - <PERSON><PERSON><PERSON><PERSON> lý các agent AI
2. [**Auth Module**](../src/modules/auth/README.md) - Xác thực và phân quyền
3. [**User Module**](../src/modules/user/README.md) - Quản lý người dùng
4. [**Integration Module**](../src/modules/integration/README.md) - Tích hợp với các dịch vụ bên ngoài
5. [**Subscription Module**](../src/modules/subscription/README.md) - Quản lý gói dịch vụ và đăng ký
6. [**Affiliate Module**](../src/modules/affiliate/README.md) - Quản lý tiếp thị liên kết
7. [**Marketing Module**](../src/modules/marketing/README.md) - Quản lý tiếp thị và quảng cáo
8. [**Marketplace Module**](../src/modules/marketplace/README.md) - Quản lý marketplace
9. [**Model Training Module**](../src/modules/model-training/README.md) - Quản lý huấn luyện mô hình
10. [**Strategy Module**](../src/modules/strategy/README.md) - Quản lý chiến lược
11. [**Data Module**](../src/modules/data/README.md) - Quản lý dữ liệu
12. [**Test Module**](../src/modules/test/README.md) - Module kiểm thử

## API Endpoints

Hệ thống cung cấp các API endpoint với tiền tố `/api/v1`. Tài liệu API chi tiết có thể được truy cập tại `/api/docs` khi chạy ứng dụng.

## Cài đặt và chạy

### Yêu cầu hệ thống

- Node.js (>= 18.x)
- npm (>= 9.x)
- PostgreSQL (>= 14.x)

### Cài đặt

```bash
# Cài đặt các gói phụ thuộc
npm install
```

### Cấu hình

Tạo file `.env` từ file `.env.example` và cấu hình các biến môi trường cần thiết.

### Chạy ứng dụng

```bash
# Chạy ở chế độ phát triển
npm run start:dev

# Chạy ở chế độ sản xuất
npm run build
npm run start:prod
```

## Kiểm thử

```bash
# Chạy kiểm thử đơn vị
npm run test

# Chạy kiểm thử tích hợp
npm run test:e2e

# Chạy kiểm thử với coverage
npm run test:cov
```

## Tài liệu chi tiết

### Tài liệu module

Mỗi module có tài liệu riêng mô tả chi tiết về chức năng, cấu trúc và cách sử dụng. Xem tài liệu của từng module trong thư mục tương ứng.

### Tài liệu API

Tài liệu API chi tiết được tạo tự động bằng Swagger và có thể truy cập tại `/api/docs` khi chạy ứng dụng.

### Tài liệu luồng xử lý

- [Luồng xử lý Blog](../src/modules/blog/docs/BLOG_FLOW.md)

## Đóng góp

Vui lòng đọc [CONTRIBUTING.md](../CONTRIBUTING.md) để biết chi tiết về quy trình đóng góp vào dự án.

## Giấy phép

Dự án này được cấp phép theo giấy phép UNLICENSED - xem file [LICENSE](../LICENSE) để biết thêm chi tiết.
