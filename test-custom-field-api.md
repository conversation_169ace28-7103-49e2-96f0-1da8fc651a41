# Test Custom Field API với định dạng Name|Value

**L<PERSON>u ý quan trọng**: Định dạng Name|Value chỉ áp dụng cho các field types: `select`, `object`, `array`. Các field types khác (`text`, `number`, `boolean`, `date`) vẫn sử dụng object validation như cũ.

## 1. Test POST /v1/user/custom-fields

### Test Case 1: Tạo text field với object validation (không thay đổi)
```json
{
  "config": {
    "id": "text-input-001",
    "label": "Họ và tên",
    "type": "text",
    "required": true,
    "validation": {
      "minLength": 3,
      "maxLength": 50,
      "pattern": "^[a-zA-Z\\s]*$"
    },
    "defaultValue": "",
    "placeholder": "Nhập họ và tên"
  }
}
```

### Test Case 2: Tạo number field với object validation (không thay đổi)
```json
{
  "config": {
    "id": "number-age-001",
    "label": "Tuổi",
    "type": "number",
    "required": true,
    "validation": {
      "min": 18,
      "max": 100
    },
    "defaultValue": 25,
    "placeholder": "Nhập tuổi"
  }
}
```

### Test Case 3: Tạo select field với định dạng Name|Value (MỚI)
```json
{
  "config": {
    "id": "select-size-001",
    "label": "Kích thước",
    "type": "select",
    "required": true,
    "validation": "XS|xs\nS|s\nM|m\nL|l\nXL|xl\nXXL|xxl",
    "defaultValue": "m",
    "placeholder": "Chọn kích thước"
  }
}
```

### Test Case 4: Tạo array field với định dạng Name|Value (MỚI)
```json
{
  "config": {
    "id": "array-tags-001",
    "label": "Tags",
    "type": "array",
    "required": false,
    "validation": "Hot|hot\nNew|new\nSale|sale\nTrending|trending",
    "defaultValue": [],
    "placeholder": "Chọn tags"
  }
}
```

### Test Case 5: Tạo object field với định dạng Name|Value (MỚI)
```json
{
  "config": {
    "id": "object-category-001",
    "label": "Danh mục",
    "type": "object",
    "required": false,
    "validation": "Điện tử|electronics\nThời trang|fashion\nGia dụng|household\nSách|books",
    "defaultValue": {},
    "placeholder": "Chọn danh mục"
  }
}
```

## 2. Test PUT /v1/user/custom-fields/:id

### Test Case 1: Cập nhật validation từ object sang Name|Value
```json
{
  "config": {
    "label": "Kích thước sản phẩm",
    "type": "select",
    "required": true,
    "validation": "Extra Small|xs\nSmall|s\nMedium|m\nLarge|l\nExtra Large|xl\nDouble XL|xxl",
    "placeholder": "Chọn kích thước sản phẩm"
  }
}
```

### Test Case 2: Cập nhật validation từ Name|Value sang object
```json
{
  "config": {
    "label": "Mô tả sản phẩm",
    "type": "textarea",
    "required": false,
    "validation": {
      "minLength": 10,
      "maxLength": 500
    },
    "placeholder": "Nhập mô tả chi tiết sản phẩm"
  }
}
```

## 3. Expected Response Format

Khi tạo/cập nhật thành công, API sẽ trả về:

```json
{
  "success": true,
  "message": "Tạo trường tùy chỉnh thành công",
  "data": {
    "id": 123,
    "configId": "select-size-001",
    "label": "Kích thước",
    "type": "select",
    "required": true,
    "configJson": {
      "validation": {
        "options": [
          {"label": "XS", "value": "xs"},
          {"label": "S", "value": "s"},
          {"label": "M", "value": "m"},
          {"label": "L", "value": "l"},
          {"label": "XL", "value": "xl"},
          {"label": "XXL", "value": "xxl"}
        ]
      },
      "defaultValue": "m",
      "placeholder": "Chọn kích thước"
    },
    "userId": 1001,
    "employeeId": null,
    "status": "PENDING",
    "createAt": 1641708800000
  }
}
```

## 4. Error Cases

### Test Case 1: Định dạng Name|Value không hợp lệ
```json
{
  "config": {
    "id": "invalid-format",
    "label": "Test",
    "type": "select",
    "validation": "XS\nS|s\nM|m"  // Thiếu | ở dòng đầu
  }
}
```

Expected Error:
```json
{
  "success": false,
  "message": "Định dạng không hợp lệ: \"XS\". Mỗi dòng phải có định dạng Name|Value"
}
```

### Test Case 2: Tên hoặc giá trị trống
```json
{
  "config": {
    "id": "empty-value",
    "label": "Test",
    "type": "select",
    "validation": "XS|xs\n|s\nM|m"  // Thiếu tên ở dòng thứ 2
  }
}
```

Expected Error:
```json
{
  "success": false,
  "message": "Tên và giá trị không được để trống trong dòng: \"|s\""
}
```

## 5. Swagger Documentation

API sẽ hiển thị trong Swagger với:
- Examples cho cả 2 định dạng validation
- Mô tả rõ ràng về cách sử dụng định dạng Name|Value
- Select options cho các field type hỗ trợ
