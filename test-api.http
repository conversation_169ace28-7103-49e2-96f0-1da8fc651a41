### Test API tạo email campaign với template

POST http://localhost:3000/api/v1/marketing/email-campaigns/with-template
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "title": "Test Campaign với Template",
  "description": "Đ<PERSON><PERSON> là campaign test sử dụng template",
  "templateEmailId": "1",
  "segmentId": "1", 
  "serverId": "1",
  "scheduledAt": 1735689600
}

### Test API tạo email campaign thông thường (để so sánh)

POST http://localhost:3000/api/v1/marketing/email-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "title": "Test Campaign thông thường",
  "description": "Đây là campaign test thông thường",
  "subject": "Test Subject",
  "content": "<h1>Test Content</h1>",
  "segmentId": 1,
  "server": {
    "host": "smtp.gmail.com",
    "port": 587,
    "secure": false,
    "user": "<EMAIL>",
    "password": "password",
    "from": "<EMAIL>"
  }
}

### L<PERSON><PERSON> danh sách email campaigns

GET http://localhost:3000/api/v1/marketing/email-campaigns
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Lấy trạng thái queue

GET http://localhost:3000/api/v1/marketing/email-campaigns/queue/status
Authorization: Bearer YOUR_JWT_TOKEN_HERE
