---
description: 
globs: 
alwaysApply: true
---
# ✅ DTO RULES

## 🎯 Objective
Used for input/output validation. Not for DB storage, not tied to ORM.

## 📁 Location
In `dtos/`, examples: `create-user.dto.ts`, `user-response.dto.ts`

## 💡 Coding Rules
- ✅ Use class + decorators (`class-validator`, `class-transformer`)
- ✅ Don't use interface/type
- ✅ Output is always DTO, don't reuse input DTO for output
- ✅ Extract base class if reuse is needed
- ✅ Query DTO must extend QueryDto class from `@common/dto/query.dto`
- ✅ Must include clear descriptions in Swagger for all fields, example:
  ```typescript
  @ApiPropertyOptional({
    description: 'Affiliate rank ID',
    example: 1
  })
  ```
- ✅ Must use all appropriate validation decorators from `class-validator`
- ✅ Use `class-transformer` when necessary for data conversion
- ✅ Standard NestJS file naming: `xxx.dto.ts`
- ✅ Avoid using `any` type, define specific interfaces instead

## 📋 DTO Classification
- **Create DTO**: Used for creating new resources, example: `create-user.dto.ts`
- **Update DTO**: Used for updating resources, example: `update-user.dto.ts`
- **Response DTO**: Used for returning data, example: `user-response.dto.ts`
- **Query DTO**: Used for searching and filtering data, example: `user-query.dto.ts`

## 🔍 Specific Examples

### Query DTO Example
```typescript
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, Min, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

export class UserQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Search by user name',
    example: 'John'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Filter by minimum age',
    example: 18
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minAge?: number;
}
```

### class-validator and class-transformer Example
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEmail, IsString, MinLength, IsOptional, IsNumber } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateUserDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'Password123'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe'
  })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  name: string;

  @ApiProperty({
    description: 'User age',
    example: 25
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  age?: number;
}
```

### Complex DTO Example with Nested Objects and Validation
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsObject, IsOptional, IsString, IsUUID, MaxLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ModelConfigDto } from './create-agent-base.dto';

/**
 * DTO for updating agent base
 */
export class UpdateAgentBaseDto {
  @ApiProperty({
    description: 'Agent base name',
    example: 'Base Assistant',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Avatar MIME type (example: image/jpeg, image/png)',
    example: 'image/jpeg',
    required: false,
    examples: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  })
  @IsOptional()
  @IsString()
  avatarMime?: string;

  @ApiProperty({
    description: 'Whether to remove current avatar',
    example: false,
    required: false,
    examples: [true, false]
  })
  @IsOptional()
  @IsBoolean()
  removeAvatar?: boolean;

  @ApiProperty({
    description: 'ID of the model to use',
    example: 'gpt-4',
    required: false,
    examples: ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'llama-3-70b']
  })
  @IsOptional()
  @IsString()
  modelId?: string;

  @ApiProperty({
    description: 'Model configuration',
    type: ModelConfigDto,
    required: false,
    example: {
      temperature: 0.7,
      topP: 0.95,
      maxTokens: 4000,
      presencePenalty: 0,
      frequencyPenalty: 0
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig?: ModelConfigDto;

  @ApiProperty({
    description: 'Instructions or system prompt for the agent',
    example: 'You are a basic AI assistant. Answer questions accurately and helpfully.',
    required: false,
    examples: [
      'You are a basic AI assistant. Answer questions accurately and helpfully.',
      'You are a marketing expert. Help users create effective marketing strategies.',
      'You are a programming assistant. Help users write and debug code.'
    ]
  })
  @IsOptional()
  @IsString()
  instruction?: string;

  @ApiProperty({
    description: 'ID of the vector store to use',
    example: 'vs-123e4567-e89b-12d3-a456-************',
    required: false,
    examples: [
      'vs-123e4567-e89b-12d3-a456-************',
      'vs-98765432-e89b-12d3-a456-************'
    ]
  })
  @IsOptional()
  @IsString()
  vectorStoreId?: string;

  @ApiProperty({
    description: 'List of role IDs to assign',
    example: ['role-123e4567-e89b-12d3-a456-************'],
    required: false,
    type: [String],
    examples: [
      ['role-123e4567-e89b-12d3-a456-************'],
      ['role-123e4567-e89b-12d3-a456-************', 'role-abcdef12-e89b-12d3-a456-************'],
      []
    ]
  })
  @IsOptional()
  @IsUUID(4, { each: true })
  roleIds?: string[];
}
```

### ModelConfigDto Example (Nested DTO)
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Max, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class ModelConfigDto {
  @ApiProperty({
    description: 'Model temperature (0-2)',
    example: 0.7,
    required: false,
    examples: [0, 0.5, 1, 1.5, 2]
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  @Type(() => Number)
  temperature?: number;

  @ApiProperty({
    description: 'Top-p sampling (0-1)',
    example: 0.95,
    required: false,
    examples: [0.5, 0.8, 0.9, 0.95, 1]
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  @Type(() => Number)
  topP?: number;

  @ApiProperty({
    description: 'Maximum tokens for response',
    example: 4000,
    required: false,
    examples: [1000, 2000, 4000, 8000, 16000]
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(32000)
  @Type(() => Number)
  maxTokens?: number;

  @ApiProperty({
    description: 'Presence penalty (-2 to 2)',
    example: 0,
    required: false,
    examples: [-2, -1, 0, 1, 2]
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  @Type(() => Number)
  presencePenalty?: number;

  @ApiProperty({
    description: 'Frequency penalty (-2 to 2)',
    example: 0,
    required: false,
    examples: [-2, -1, 0, 1, 2]
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  @Type(() => Number)
  frequencyPenalty?: number;
}
```

### Response DTO Example
```typescript
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { Exclude, Expose, Transform } from 'class-transformer';

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  id: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe'
  })
  name: string;

  @ApiProperty({
    description: 'Account creation date (timestamp)',
    example: *************
  })
  @Transform(({ value }) => value instanceof Date ? value.getTime() : value)
  createdAt: Date;

  @ApiProperty({
    description: 'Account update date (timestamp)',
    example: *************
  })
  @Transform(({ value }) => value instanceof Date ? value.getTime() : value)
  updatedAt: Date;

  // Exclude this field from response
  @Exclude()
  password: string;

  // Add computed field not present in database
  @Expose()
  @ApiProperty({
    description: 'Account status',
    example: 'active',
    enum: ['active', 'inactive', 'suspended']
  })
  get status(): string {
    // Logic to compute status based on other fields
    return this.isActive ? 'active' : 'inactive';
  }

  // Private field not appearing in response
  private isActive: boolean;

  constructor(partial: Partial<UserResponseDto>) {
    Object.assign(this, partial);
  }
}
```

### DTO Example with Enum and Array
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

// Define enum
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// Child DTO for use in array
export class AddressDto {
  @ApiProperty({
    description: 'Street address',
    example: '123 Main St'
  })
  @IsString()
  street: string;

  @ApiProperty({
    description: 'City',
    example: 'New York'
  })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'Zip code',
    example: '10001'
  })
  @IsString()
  zipCode: string;
}

export class CreateUserWithRoleDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>'
  })
  @IsString()
  email: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.USER,
    examples: [UserRole.ADMIN, UserRole.USER, UserRole.GUEST]
  })
  @IsEnum(UserRole)
  role: UserRole;

  @ApiProperty({
    description: 'List of addresses',
    type: [AddressDto],
    example: [
      {
        street: '123 Main St',
        city: 'New York',
        zipCode: '10001'
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AddressDto)
  addresses?: AddressDto[];
}
```

### DTO Inheritance Example
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

// Base DTO containing common fields
export class BaseUserDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe'
  })
  @IsNotEmpty()
  @IsString()
  name: string;
}

// Create DTO inherits from base and adds required fields
export class CreateUserDto extends BaseUserDto {
  @ApiProperty({
    description: 'User password',
    example: 'Password123'
  })
  @IsNotEmpty()
  @IsString()
  password: string;
}

// Update DTO implements Partial of base with all fields optional
export class UpdateUserDto implements Partial<BaseUserDto> {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'New user password',
    example: 'NewPassword123',
    required: false
  })
  @IsOptional()
  @IsString()
  password?: string;
}
```

## 🔄 Relationship with Entity
- DTOs are not dependent on Entities, but may have similar structure
- Don't use Entities for input validation or output responses
- Can use mappers to convert between DTOs and Entities
- Create separate mappers for converting between Entities and different DTO types

## 🛠️ Advanced Techniques
- **Serialization/Deserialization**: Use `@Exclude()`, `@Expose()`, `@Transform()` from class-transformer to control data conversion
- **Validation Groups**: Use validation groups to apply different validation rules to the same DTO
- **Custom Validators**: Create custom validators for complex validation logic
- **Interceptors**: Use interceptors to automatically convert responses to DTOs

## ✅ Checklist
- [ ] Complete validation decorators
- [ ] Clear input/output separation
- [ ] No ORM dependencies
- [ ] No reuse of input DTOs for output
- [ ] Query DTOs extend QueryDto
- [ ] Complete Swagger descriptions
- [ ] No 'any' types, use specific interfaces
- [ ] Standard NestJS file naming: `xxx.dto.ts`
- [ ] Use class-transformer when needed
- [ ] Use @Exclude/@Expose for serialization control
- [ ] Use @Transform for data conversion
- [ ] Use @ValidateNested for nested objects
- [ ] Use @Type(() => Class) for nested objects
- [ ] Use @IsEnum for enum fields

- [ ] Create base DTOs for code reuse when needed