---
description: 
globs: 
alwaysApply: true
---
# ✅ SERVICE RULES

## 🎯 Objective
Services are responsible for handling business logic, calling repositories, validating data, and handling errors.

## 📁 Structure
- Each module has 1 file `/services/xxx.service.ts`
- All business logic must be in services

## 💡 Main Rules

### 1. Structure and Dependency Injection
- ✅ Only inject repositories via constructor with this syntax:
  ```typescript
  constructor(
    private readonly userRepository: UserRepository,
    private readonly productRepository: ProductRepository,
  ) {}
  ```
  Don't use `@InjectRepository()` in services

### 2. Data Types and Documentation
- ✅ Add complete comments for all functions and complex logic
- ✅ Return types must be explicit: `Promise<DtoResponse>` or `Promise<PaginatedResult<DtoResponse>>`
- ✅ Don't use `any` or `unknown` types

### 3. Error Handling
- ✅ Throw business errors with module error codes:
  ```typescript
  if (!product) {
    throw new AppException(
      PRODUCT_ERROR_CODES.PRODUCT_NOT_FOUND,
      'Product does not exist'
    );
  }
  ```
- ✅ Place general try-catch after business validations:
  ```typescript
  // Business validation
  if (!entity) {
    throw new AppException(ERROR_CODES.NOT_FOUND);
  }

  try {
    // Main logic
  } catch (error) {
    // Server error handling
    throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, error.message);
  }
  ```
- ✅ Don't use framework exceptions (BadRequestException, NotFoundException...)

### 4. Database Interaction
- ✅ Use `@Transactional()` for all functions that add/modify/delete data
- ✅ Don't write queries directly in services, only call repository functions
- ✅ Optimize query count, avoid N+1 queries

### 5. Clean Code
- ✅ Clear function names, single responsibility
- ✅ Extract complex logic to helpers
- ✅ Use validation helpers for code reuse

## ✅ Evaluation Checklist
- [ ] No direct DB queries
- [ ] No controller/framework dependencies
- [ ] Proper error handling with AppException
- [ ] Single-responsibility, readable functions
- [ ] Correct repository injection
- [ ] Complete comments
- [ ] Clear return types
- [ ] Use @Transactional() for add/modify/delete operations
- [ ] Extract complex logic to helpers

## 📝 Examples

### Example 1: Business Error Handling and General Try-Catch
```typescript
/**
 * Update agent type information
 * @param id Agent type ID
 * @param updateDto Update data
 */
@Transactional()
async update(id: number, updateDto: UpdateTypeAgentDto): Promise<TypeAgentResponseDto> {
  // Check if agent type exists
  const typeAgent = await this.typeAgentRepository.findTypeAgentById(id);
  if (!typeAgent) {
    throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
  }

  // Check if name already exists
  if (updateDto.name && updateDto.name !== typeAgent.name) {
    const existingTypeAgent = await this.typeAgentRepository.findTypeAgentByName(updateDto.name);
    if (existingTypeAgent && existingTypeAgent.id !== id) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
    }
  }

  try {
    // Update information
    if (updateDto.name) typeAgent.name = updateDto.name;
    if (updateDto.description !== undefined) typeAgent.description = updateDto.description;

    // Save to database
    const updatedTypeAgent = await this.typeAgentRepository.save(typeAgent);
    return this.mapToDto(updatedTypeAgent);
  } catch (error) {
    this.logger.error(`Error updating agent type ${id}: ${error.message}`, error.stack);
    throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, error.message);
  }
}
```

### Example 2: Using Helpers and Validation
```typescript
/**
 * Submit product for approval
 * @param userId User ID
 * @param productId Product ID
 */
@Transactional()
async submitProductForApproval(userId: number, productId: number): Promise<ProductResponseDto> {

    // Get product information
    const product = await this.productRepository.findByIdAndUserId(productId, userId);
    if (!product) {
      throw new AppException(PRODUCT_ERROR_CODES.PRODUCT_NOT_FOUND, 'Product does not exist');
    }

    // Use helper for validation
    this.validationHelper.validateProductNotDeleted(product);
    this.validationHelper.validateProductOwnership(product, userId);
    this.validationHelper.validateProductIsDraft(product);
try {
    // Update status
    const updatedProduct = await this.productRepository.updateStatus(
      productId,
      ProductStatus.PENDING_APPROVAL
    );

    return this.productMapper.toResponseDto(updatedProduct);
  } catch (error) {
    if (error instanceof AppException) {
      throw error;
    }
    throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, error.message);
  }
}

```