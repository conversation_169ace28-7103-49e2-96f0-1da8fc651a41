import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

describe('Affiliate API (e2e)', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let configService: ConfigService;
  let accessToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);
    
    await app.init();

    // Create a test user token
    // Note: In a real e2e test, you would authenticate with real credentials
    // and get a real token. This is just a simplified version.
    const jwtSecret = configService.get<string>('JWT_SECRET');
    accessToken = jwtService.sign(
      { id: 1, email: '<EMAIL>', role: 'user' },
      { secret: jwtSecret, expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/user/affiliate/statistics (GET)', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/statistics')
        .expect(401);
    });

    it('should return statistics when authenticated', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/statistics')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          // Note: In a real e2e test, you would check for actual data
          // This test might fail if the test user doesn't have an affiliate account
        });
    });

    it('should accept query parameters', () => {
      const begin = Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60; // 30 days ago
      const end = Math.floor(Date.now() / 1000);

      return request(app.getHttpServer())
        .get(`/user/affiliate/statistics?begin=${begin}&end=${end}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.period).toBeDefined();
          expect(res.body.data.period.begin).toBe(begin);
          expect(res.body.data.period.end).toBe(end);
        });
    });
  });

  describe('/user/affiliate/account (GET)', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/account')
        .expect(401);
    });

    it('should return account information when authenticated', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/account')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.accountInfo).toBeDefined();
          expect(res.body.data.rankInfo).toBeDefined();
          expect(res.body.data.referralCode).toBeDefined();
          expect(res.body.data.referralLink).toBeDefined();
        });
    });
  });

  describe('/user/affiliate/orders (GET)', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/orders')
        .expect(401);
    });

    it('should return paginated orders when authenticated', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/orders')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.items).toBeDefined();
          expect(Array.isArray(res.body.data.items)).toBe(true);
          expect(res.body.data.meta).toBeDefined();
          expect(res.body.data.meta.totalItems).toBeDefined();
          expect(res.body.data.meta.currentPage).toBeDefined();
        });
    });

    it('should accept pagination parameters', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/orders?page=1&limit=5')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.meta.itemsPerPage).toBe(5);
          expect(res.body.data.meta.currentPage).toBe(1);
        });
    });

    it('should accept time range parameters', () => {
      const begin = Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60; // 30 days ago
      const end = Math.floor(Date.now() / 1000);

      return request(app.getHttpServer())
        .get(`/user/affiliate/orders?begin=${begin}&end=${end}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
        });
    });
  });

  describe('/user/affiliate/withdrawals (GET)', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/withdrawals')
        .expect(401);
    });

    it('should return paginated withdrawals when authenticated', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/withdrawals')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.items).toBeDefined();
          expect(Array.isArray(res.body.data.items)).toBe(true);
          expect(res.body.data.meta).toBeDefined();
        });
    });
  });

  describe('/user/affiliate/customers (GET)', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/customers')
        .expect(401);
    });

    it('should return paginated customers when authenticated', () => {
      return request(app.getHttpServer())
        .get('/user/affiliate/customers')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.items).toBeDefined();
          expect(Array.isArray(res.body.data.items)).toBe(true);
          expect(res.body.data.meta).toBeDefined();
        });
    });
  });
});
