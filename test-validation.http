### Test validation error với segment creation
POST http://localhost:3000/v1/marketing/segments
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "",
  "description": "Test segment",
  "criteria": {
    "groups": [
      {
        "logicalOperator": "INVALID_OPERATOR",
        "conditions": [
          {
            "field": "",
            "operator": "invalid_operator",
            "value": "test"
          }
        ]
      }
    ]
  }
}

### Test validation error với missing required fields
POST http://localhost:3000/v1/marketing/segments
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "description": "Test segment without name"
}

### Test validation error với invalid nested data
POST http://localhost:3000/v1/marketing/segments
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Valid Name",
  "criteria": {
    "groups": [
      {
        "logicalOperator": "AND",
        "conditions": [
          {
            "operator": "equals"
          }
        ]
      }
    ]
  }
}
