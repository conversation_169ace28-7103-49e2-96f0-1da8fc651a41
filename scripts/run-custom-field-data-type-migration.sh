#!/bin/bash

# Script để chạy migration cập nhật data_type từ varchar thành enum
# cho bảng audience_user_custom_fields và audience_admin_custom_fields

set -e

echo "🚀 Custom Field Data Type Migration Script"
echo "=========================================="
echo ""

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Check required environment variables
if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "❌ Missing required environment variables!"
    echo "Please ensure DB_HOST, DB_NAME, and DB_USER are set in your .env file"
    exit 1
fi

echo "📋 Thông tin kết nối database:"
echo "   Host: $DB_HOST"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo ""

# Run the migration
echo "Applying migration..."
psql -h "$DB_HOST" -d "$DB_NAME" -U "$DB_USER" -f src/database/migrations/update-custom-field-data-type-to-enum.sql

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully!"
    echo ""
    echo "Changes applied:"
    echo "- Created custom_field_data_type_enum with values: text, number, boolean, date, select, object"
    echo "- Updated data_type column in audience_user_custom_fields table to use enum"
    echo "- Updated data_type column in audience_admin_custom_fields table to use enum"
    echo "- Migrated existing data:"
    echo "  * string -> text"
    echo "  * integer -> number"
    echo "  * json -> object"
    echo ""
    echo "You can now use the updated CustomFieldDataType enum in your DTOs and entities."
else
    echo "❌ Migration failed!"
    echo ""
    echo "To rollback, run:"
    echo "psql -h \$DB_HOST -d \$DB_NAME -U \$DB_USER -f src/database/migrations/rollback-custom-field-data-type-enum.sql"
    exit 1
fi
