#!/bin/bash

# Script để chạy migration thêm country_code vào audience tables
# Author: AI Assistant
# Date: 2024-12-19

set -e

echo "🚀 Bắt đầu migration thêm country_code vào audience tables..."
echo ""

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | awk '/=/ {print $1}')
fi

# Check if required environment variables are set
if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "❌ Thiếu biến môi trường cần thiết:"
    echo "   DB_HOST, DB_NAME, DB_USER"
    echo ""
    echo "Vui lòng kiểm tra file .env hoặc set các biến môi trường:"
    echo "   export DB_HOST=localhost"
    echo "   export DB_NAME=your_database"
    echo "   export DB_USER=your_username"
    exit 1
fi

echo "📋 Thông tin kết nối database:"
echo "   Host: $DB_HOST"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo ""

# Run the migration
echo "Applying migration..."
psql -h "$DB_HOST" -d "$DB_NAME" -U "$DB_USER" -f src/database/migrations/add-country-code-to-audience-tables.sql

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully!"
    echo ""
    echo "Changes applied:"
    echo "- Added country_code column to user_audience table"
    echo "- Added country_code column to admin_audience table"
    echo "- Set default value to '+84' for both tables"
    echo "- Updated existing records with default value"
    echo ""
    echo "You can now use the countryCode field in your Audience DTOs and APIs."
else
    echo "❌ Migration failed!"
    echo ""
    echo "To rollback, run:"
    echo "psql -h \$DB_HOST -d \$DB_NAME -U \$DB_USER -c \"ALTER TABLE user_audience DROP COLUMN IF EXISTS country_code; ALTER TABLE admin_audience DROP COLUMN IF EXISTS country_code;\""
    exit 1
fi
