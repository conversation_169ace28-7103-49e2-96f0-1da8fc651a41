# Script để chạy migration thêm country_code vào audience tables
# Author: AI Assistant
# Date: 2024-12-19

Write-Host "🚀 Bắt đầu migration thêm country_code vào audience tables..." -ForegroundColor Green
Write-Host ""

# Load environment variables from .env file
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
}

# Check if required environment variables are set
if (-not $env:DB_HOST -or -not $env:DB_NAME -or -not $env:DB_USER) {
    Write-Host "❌ Thiếu biến môi trường cần thiết:" -ForegroundColor Red
    Write-Host "   DB_HOST, DB_NAME, DB_USER" -ForegroundColor Red
    Write-Host ""
    Write-Host "Vui lòng kiểm tra file .env hoặc set các biến môi trường:" -ForegroundColor Yellow
    Write-Host "   `$env:DB_HOST='localhost'" -ForegroundColor Yellow
    Write-Host "   `$env:DB_NAME='your_database'" -ForegroundColor Yellow
    Write-Host "   `$env:DB_USER='your_username'" -ForegroundColor Yellow
    exit 1
}

Write-Host "📋 Thông tin kết nối database:" -ForegroundColor Cyan
Write-Host "   Host: $env:DB_HOST" -ForegroundColor White
Write-Host "   Database: $env:DB_NAME" -ForegroundColor White
Write-Host "   User: $env:DB_USER" -ForegroundColor White
Write-Host ""

try {
    & psql -h $env:DB_HOST -d $env:DB_NAME -U $env:DB_USER -f "src/database/migrations/add-country-code-to-audience-tables.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Changes applied:" -ForegroundColor Cyan
        Write-Host "- Added country_code column to user_audience table" -ForegroundColor White
        Write-Host "- Added country_code column to admin_audience table" -ForegroundColor White
        Write-Host "- Set default value to '+84' for both tables" -ForegroundColor White
        Write-Host "- Updated existing records with default value" -ForegroundColor White
        Write-Host ""
        Write-Host "You can now use the countryCode field in your Audience DTOs and APIs." -ForegroundColor Green
    } else {
        throw "Migration command failed"
    }
} catch {
    Write-Host "❌ Migration failed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "To rollback, run:" -ForegroundColor Yellow
    Write-Host "psql -h `$env:DB_HOST -d `$env:DB_NAME -U `$env:DB_USER -c `"ALTER TABLE user_audience DROP COLUMN IF EXISTS country_code; ALTER TABLE admin_audience DROP COLUMN IF EXISTS country_code;`"" -ForegroundColor Yellow
    exit 1
}
