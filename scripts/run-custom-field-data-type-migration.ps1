# Script để chạy migration cập nhật data_type từ varchar thành enum
# cho bảng audience_user_custom_fields và audience_admin_custom_fields

Write-Host "🚀 Custom Field Data Type Migration Script" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

# Load environment variables from .env file
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2])
        }
    }
}

# Check required environment variables
if (-not $env:DB_HOST -or -not $env:DB_NAME -or -not $env:DB_USER) {
    Write-Host "❌ Missing required environment variables!" -ForegroundColor Red
    Write-Host "Please ensure DB_HOST, DB_NAME, and DB_USER are set in your .env file" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Thông tin kết nối database:" -ForegroundColor Cyan
Write-Host "   Host: $env:DB_HOST" -ForegroundColor White
Write-Host "   Database: $env:DB_NAME" -ForegroundColor White
Write-Host "   User: $env:DB_USER" -ForegroundColor White
Write-Host ""

try {
    & psql -h $env:DB_HOST -d $env:DB_NAME -U $env:DB_USER -f "src/database/migrations/update-custom-field-data-type-to-enum.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Changes applied:" -ForegroundColor Yellow
        Write-Host "- Created custom_field_data_type_enum with values: text, number, boolean, date, select, object" -ForegroundColor White
        Write-Host "- Updated data_type column in audience_user_custom_fields table to use enum" -ForegroundColor White
        Write-Host "- Updated data_type column in audience_admin_custom_fields table to use enum" -ForegroundColor White
        Write-Host "- Migrated existing data:" -ForegroundColor White
        Write-Host "  * string -> text" -ForegroundColor Gray
        Write-Host "  * integer -> number" -ForegroundColor Gray
        Write-Host "  * json -> object" -ForegroundColor Gray
        Write-Host ""
        Write-Host "You can now use the updated CustomFieldDataType enum in your DTOs and entities." -ForegroundColor Green
    } else {
        throw "Migration failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "❌ Migration failed!" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "To rollback, run:" -ForegroundColor Yellow
    Write-Host "psql -h `$env:DB_HOST -d `$env:DB_NAME -U `$env:DB_USER -f src/database/migrations/rollback-custom-field-data-type-enum.sql" -ForegroundColor White
    exit 1
}
