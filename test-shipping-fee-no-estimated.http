### Test shipping fee calculation without isEstimated field
POST http://localhost:3000/v1/user/orders/calculate-shipping-fee
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "shopId": 3,
  "products": [
    {
      "productId": 60,
      "quantity": 2
    },
    {
      "productId": 61,
      "quantity": 1
    }
  ],
  "deliveryAddress": {
    "addressId": 1
  },
  "preferredCarrier": "GHTK"
}

### Test with GHN carrier
POST http://localhost:3000/v1/user/orders/calculate-shipping-fee
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "shopId": 3,
  "products": [
    {
      "productId": 60,
      "quantity": 2
    },
    {
      "productId": 61,
      "quantity": 1
    }
  ],
  "deliveryAddress": {
    "addressId": 1
  },
  "preferredCarrier": "GHN"
}
