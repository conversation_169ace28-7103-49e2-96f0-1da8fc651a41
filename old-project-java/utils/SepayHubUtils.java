package com.redon_agency.chatbot.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.redon_agency.chatbot.dto.client.sepay.hub.*;
import com.redon_agency.chatbot.entity.BankInSepay;
import com.redon_agency.chatbot.entity.ElectronicPaymentGateway;
import com.redon_agency.chatbot.entity.User;
import com.redon_agency.chatbot.exception.AppException;
import com.redon_agency.chatbot.exception.ErrorCode;
import com.redon_agency.chatbot.mapper.SepayHubMapper;
import com.redon_agency.chatbot.repository.client.sepay.SePayBankHubClient;
import com.redon_agency.chatbot.service.other.RedisService;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class SepayHubUtils {
    private final SePayBankHubClient sePayBankHubClient;
    private final RedisService redisService;

    @Value("${sepay-hub.client.id}")
    private String clientId;

    @Value("${sepay-hub.client.secret}")
    private String clientSecret;

    @Value("${sepay-hub.api-key}")
    private String apiKey;

    private static final String TOKEN_KEY = "sepay_accessToken";
    private static final long TOKEN_EXPIRY = 90;

    public UUID generateUUID() {
        return UUID.randomUUID();
    }

    ;

    public Long totalBankAccounts() {
        String bearerToken = getToken();
        UUID uuid = UUID.randomUUID();
        BankAccountResponse totalAccount = sePayBankHubClient.getBankAccounts(bearerToken, uuid, null, null, null, null, null);
        return totalAccount.getMeta().getTotal();
    }

    public Long totalVAAccount() {
        String bearerToken = getToken();
        UUID uuid = UUID.randomUUID();
        VAsResponse response = sePayBankHubClient.getListOfVAOCB(bearerToken, uuid, null, null, null, null, null);
        return response.getMeta().getTotal();
    }

    public String getToken() {
        // Kiểm tra token từ Redis
        Object cachedToken = redisService.getData(TOKEN_KEY);
        if (cachedToken != null) {
            return cachedToken.toString();
        }

        // Mã hóa clientId và clientSecret theo chuẩn Base64
        String auth = clientId + ":" + clientSecret;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        String header = "Basic " + encodedAuth;

        // Gọi API lấy token
        try {
            AccessTokenResponse newToken = sePayBankHubClient.createAccessToken(header);
            if (newToken != null) {
                if (newToken.getData() != null) {
                    if (newToken.getData().getAccessToken() != null) {
                        // Lưu token vào Redis với thời gian sống là 90 phút
                        redisService.saveDataWithTTL(TOKEN_KEY, "Bearer " + newToken.getData().getAccessToken(), TOKEN_EXPIRY, TimeUnit.MINUTES);
                        return "Bearer " + newToken.getData().getAccessToken();
                    }
                }
            }
        } catch (Exception e) {
            log.error("SEPAY_HUB - Failed to create access token: ", e);
            throw new AppException(ErrorCode.ERROR_SYSTEM);
        }
        throw new AppException(ErrorCode.ERROR_SYSTEM);
    }

    public boolean validateApiKey(HttpServletRequest request) {
        // Lấy giá trị từ header "Authorization"
        String authorizationHeader = request.getHeader("Authorization");

        // Kiểm tra nếu header không tồn tại hoặc không đúng định dạng
        if (authorizationHeader == null || !authorizationHeader.startsWith("Apikey ")) {
            log.error("SEPAY_HUB - Missing or invalid Authorization header");
            throw new AppException(ErrorCode.UNAUTHORIZED, "Missing or invalid Authorization header");
        }

        // Lấy API Key từ Authorization header (bỏ tiền tố "Apikey ")
        String receivedApiKey = authorizationHeader.substring(7).trim();

        // So sánh API Key với giá trị được cấu hình
        if (!apiKey.equals(receivedApiKey)) {
            log.error("SEPAY_HUB - Invalid API key: {}", receivedApiKey);
            throw new AppException(ErrorCode.UNAUTHORIZED, "Invalid API key");
        }

        log.info("SEPAY_HUB - API key validated successfully");
        return true;
    }

    public Long totalTransaction() {
        String bearerToken = getToken();
        UUID uuid = UUID.randomUUID();

        try {
            CounterResponse response = sePayBankHubClient.getTransactionCounter(bearerToken, uuid, null);
            if (response != null && response.getData() != null && response.getData().getTotal() != null) {
                return Math.round(Double.parseDouble(response.getData().getTotal().getTransaction_in()));
            }
            return 0L;
        } catch (Exception e) {
            log.error("SEPAY_HUB - Failed to get transaction counter: ", e);
            return 0L;
        }
    }

    public void updateCompany(String companyId, User user) {
        String bearerToken = getToken();
        UUID uuid = UUID.randomUUID();

        CompanyUpdateRequest request = CompanyUpdateRequest.builder()
                .fullName(user.getName())
                .status("Active")
                .shortName(user.getUserId().toString())
                .build();
        CompanyUpdateResponse response = sePayBankHubClient.updateCompany(bearerToken, uuid, companyId, request);
        if (response == null || response.getCode() != 200) {
            throw new AppException(ErrorCode.ERROR_SYSTEM);
        }
    }

    public void updateCompanyConfiguration(String companyId) {
        String bearerToken = getToken();
        UUID uuid = UUID.randomUUID();

        UpdateCompanyConfigurationRequest request = UpdateCompanyConfigurationRequest.builder()
                .transaction_amount("Unlimited")
                .build();

        UpdateCompanyConfigurationResponse response = sePayBankHubClient.updateCompanyConfiguration(bearerToken, uuid, companyId, request);
        if (response == null || response.getCode() != 200) {
            throw new AppException(ErrorCode.ERROR_SYSTEM);
        }
    }

    public CreateVAResponse requestCreateVAOCB(CreateVARequest createVARequest) {
        UUID uuid = generateUUID();
        String token = this.getToken();
        CreateVAResponse createVAResponse;
        try {
            createVAResponse = sePayBankHubClient.requestCreateVAOCB(token, uuid, createVARequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                createVAResponse = mapper.readValue(responseBody, CreateVAResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }

        return createVAResponse;
    }

    public ConfirmCreateVAResponse confirmCreateVAOCB(
            String requestId,
            OtpRequest otpRequest
    ) {
        UUID uuid = generateUUID();
        String token = this.getToken();
        ConfirmCreateVAResponse confirmCreateVAResponse;

        try {
            confirmCreateVAResponse = sePayBankHubClient.confirmCreateVAOCB(token, uuid, requestId, otpRequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                confirmCreateVAResponse = mapper.readValue(responseBody, ConfirmCreateVAResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }

        return confirmCreateVAResponse;
    }

    public BankAccountDeleteRequestResponse requestDeleteApiConnection(BankCodeEnum bankCodeEnum, String accountId) {
        String token = getToken();
        UUID uuid = generateUUID();

        BankAccountDeleteRequestResponse bankAccountDeleteRequestResponse = new BankAccountDeleteRequestResponse();

        try {
            if (bankCodeEnum == BankCodeEnum.MB) {
                bankAccountDeleteRequestResponse = sePayBankHubClient.requestDeleteMB(token, uuid, accountId);
            } else if (bankCodeEnum == BankCodeEnum.ACB) {
                bankAccountDeleteRequestResponse = sePayBankHubClient.requestDeleteApiConnectionACB(token, uuid, accountId);
            }
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountDeleteRequestResponse = mapper.readValue(responseBody, BankAccountDeleteRequestResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountDeleteRequestResponse;
    }

    public BankAccountDeleteConfirmResponse confirmDeleteApiConnection(
            BankCodeEnum bankCodeEnum,
            String requestId,
            OtpRequest request
    ) {
        String token = getToken();
        UUID uuid = generateUUID();

        BankAccountDeleteConfirmResponse bankAccountDeleteConfirmResponse = new BankAccountDeleteConfirmResponse();
        try {
            if (bankCodeEnum == BankCodeEnum.MB) {
                bankAccountDeleteConfirmResponse = sePayBankHubClient.confirmDeleteMB(token, uuid, requestId, request);
            } else if (bankCodeEnum == BankCodeEnum.ACB) {
                bankAccountDeleteConfirmResponse = sePayBankHubClient.confirmDeleteApiConnectionACB(token, uuid, requestId, request);
            }
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountDeleteConfirmResponse = mapper.readValue(responseBody, BankAccountDeleteConfirmResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountDeleteConfirmResponse;
    }

    public BankAccountRequestResponse requestApiConnection(BankCodeEnum bankCodeEnum, String accountId) {
        String token = getToken();
        UUID uuid = generateUUID();

        BankAccountRequestResponse bankAccountRequestResponse = new BankAccountRequestResponse();

        try {
            if (bankCodeEnum == BankCodeEnum.MB) {
                bankAccountRequestResponse = sePayBankHubClient.requestApiConnectionMB(token, uuid, accountId);
            } else if (bankCodeEnum == BankCodeEnum.ACB) {
                bankAccountRequestResponse = sePayBankHubClient.requestApiConnectionACB(token, uuid, accountId);
            }
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountRequestResponse = mapper.readValue(responseBody, BankAccountRequestResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }

        return bankAccountRequestResponse;
    }

    public CreateVAResponse requestCreateVA(
            BankCodeEnum bankCodeEnum,
            CreateVARequest request
    ) {
        String token = getToken();
        UUID uuid = generateUUID();

        CreateVAResponse createVAResponse = new CreateVAResponse();

        try {
            if (bankCodeEnum == BankCodeEnum.OCB) {
                createVAResponse = sePayBankHubClient.requestCreateVAOCB(token, uuid, request);
            }
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                createVAResponse = mapper.readValue(responseBody, CreateVAResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }

        return createVAResponse;
    }

    public com.redon_agency.chatbot.dto.response.sepay_hub.BankAccountDetailResponse getBankAccountDetails(
            ElectronicPaymentGateway request,
            BankInSepay bank
    ) {
        String token = getToken();
        UUID uuid = generateUUID();
        com.redon_agency.chatbot.dto.response.sepay_hub.BankAccountDetailResponse response;

        if (Objects.equals(request.getIsVa(), true)) {
            VAResponse vaResponse = sePayBankHubClient.getVADetailsOCB(token, uuid, request.getVaId());
            response = SepayHubMapper.toVAAccountDetailResponse(vaResponse.getData(), request, bank);
        } else {
            BankAccountDetailResponse bankAccountDetailResponse = sePayBankHubClient.getBankAccountDetails(token, uuid, request.getAccountId());
            response = SepayHubMapper.toBankAccountDetailResponse(bankAccountDetailResponse.getData(), request, bank);
        }

        return response;
    }

    public BankAccountCreateResponse createBankAccountACB(BankAccountCreateRequest bankAccountCreateRequest) {
        String token = getToken();
        UUID uuid = generateUUID();
        BankAccountCreateResponse bankAccountCreateResponse;
        try {
            bankAccountCreateResponse = sePayBankHubClient.createBankAccountACB(token, uuid, bankAccountCreateRequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountCreateResponse = mapper.readValue(responseBody, BankAccountCreateResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountCreateResponse;
    }

    public BankAccountCreateResponse createBankAccountMB(BankAccountCreateRequest bankAccountCreateRequest) {
        String token = getToken();
        UUID uuid = generateUUID();

        BankAccountCreateResponse bankAccountCreateResponse;
        try {
            bankAccountCreateResponse = sePayBankHubClient.createBankAccountMB(token, uuid, bankAccountCreateRequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountCreateResponse = mapper.readValue(responseBody, BankAccountCreateResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountCreateResponse;
    }

    public BankAccountCreateResponse createBankAccountOCB(BankAccountCreateRequest bankAccountCreateRequest) {
        String token = getToken();
        UUID uuid = generateUUID();
        BankAccountCreateResponse bankAccountCreateResponse;
        try {
            bankAccountCreateResponse = sePayBankHubClient.createBankAccountOCB(token, uuid, bankAccountCreateRequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountCreateResponse = mapper.readValue(responseBody, BankAccountCreateResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountCreateResponse;
    }

    public BankAccountConfirmResponse confirmApiConnectionACB(String requestId, BankAccountConfirmRequest confirmRequest) {
        String token = getToken();
        UUID uuid = generateUUID();
        BankAccountConfirmResponse bankAccountConfirmResponse;
        try {
            bankAccountConfirmResponse = sePayBankHubClient.confirmApiConnectionACB(token, uuid, requestId, confirmRequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountConfirmResponse = mapper.readValue(responseBody, BankAccountConfirmResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountConfirmResponse;
    }

    public BankAccountConfirmResponse confirmBankAccountConnectionMB(String requestId, BankAccountConfirmRequest confirmRequest) {
        String token = getToken();
        UUID uuid = generateUUID();
        BankAccountConfirmResponse bankAccountConfirmResponse;
        try {
            bankAccountConfirmResponse = sePayBankHubClient.confirmBankAccountConnectionMB(token, uuid, requestId, confirmRequest);
        } catch (FeignException e) {
            String responseBody = e.contentUTF8();
            log.warn("Feign Error: {}", responseBody);

            // Parse JSON body về object nếu cần
            ObjectMapper mapper = new ObjectMapper();
            try {
                bankAccountConfirmResponse = mapper.readValue(responseBody, BankAccountConfirmResponse.class);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        }
        return bankAccountConfirmResponse;
    }

    public BankAccountDeleteConfirmResponse forceDeleteMB(String accountId) {
        String token = getToken();
        UUID uuid = generateUUID();

        BankAccountDeleteConfirmResponse response;
        try {
            response = sePayBankHubClient.forceDeleteMB(token, uuid, accountId);
            return response;
        } catch (Exception e) {
            log.warn("Feign Error: {}", e.getMessage());
            throw new AppException(ErrorCode.ERROR_SYSTEM);
        }
    }
}
