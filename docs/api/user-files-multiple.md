# API Tạo Nhiều File - POST /v1/user/files-multiple

## <PERSON><PERSON> tả
API này cho phép người dùng tạo nhiều file cùng lúc trong hệ thống. Mỗi file sẽ được tạo với presigned URL để upload và view URL để xem file.

## Endpoint
```
POST /v1/user/files-multiple
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Request Body

### Schema
```typescript
{
  warehouseId?: number;     // ID kho ảo (tùy chọn)
  folderId?: number;        // ID thư mục (tùy chọn, nếu không có sẽ tạo trong thư mục gốc)
  files: [                 // Danh sách file cần tạo (bắt buộc, ít nhất 1 file)
    {
      name: string;         // Tên file (bắt buộc, tối đa 255 ký tự)
      size: number;         // Kích thước file tính bằng byte (b<PERSON><PERSON> buộ<PERSON>, phải > 0)
    }
  ]
}
```

### Ví dụ Request
```json
{
  "warehouseId": 1,
  "folderId": 5,
  "files": [
    {
      "name": "Tài liệu hướng dẫn.pdf",
      "size": 1024000
    },
    {
      "name": "Báo cáo tháng 12.docx", 
      "size": 2048000
    },
    {
      "name": "Dữ liệu khách hàng.xlsx",
      "size": 512000
    }
  ]
}
```

## Response

### Success Response (201 Created)
```json
{
  "code": 201,
  "message": "Tạo 3 file thành công",
  "result": {
    "files": [
      {
        "id": 1,
        "name": "Tài liệu hướng dẫn.pdf",
        "uploadUrl": "https://storage.example.com/presigned-url-1?token=abc123",
        "storageKey": "files/2023/12/document-1625097600000-abcdef123456.pdf",
        "viewUrl": "https://cdn.example.com/files/2023/12/document-1625097600000-abcdef123456.pdf",
        "warehouseId": 1,
        "folderId": 5,
        "size": 1024000,
        "createdAt": 1625097600000,
        "updatedAt": 1625097600000
      },
      {
        "id": 2,
        "name": "Báo cáo tháng 12.docx",
        "uploadUrl": "https://storage.example.com/presigned-url-2?token=def456",
        "storageKey": "files/2023/12/document-1625097700000-ghijkl789012.docx",
        "viewUrl": "https://cdn.example.com/files/2023/12/document-1625097700000-ghijkl789012.docx",
        "warehouseId": 1,
        "folderId": 5,
        "size": 2048000,
        "createdAt": 1625097700000,
        "updatedAt": 1625097700000
      },
      {
        "id": 3,
        "name": "Dữ liệu khách hàng.xlsx",
        "uploadUrl": "https://storage.example.com/presigned-url-3?token=ghi789",
        "storageKey": "files/2023/12/document-1625097800000-mnopqr345678.xlsx",
        "viewUrl": "https://cdn.example.com/files/2023/12/document-1625097800000-mnopqr345678.xlsx",
        "warehouseId": 1,
        "folderId": 5,
        "size": 512000,
        "createdAt": 1625097800000,
        "updatedAt": 1625097800000
      }
    ],
    "successCount": 3,
    "totalCount": 3
  }
}
```

### Error Responses

#### 400 Bad Request - Validation Error
```json
{
  "code": 400,
  "message": "Dữ liệu không hợp lệ",
  "errors": [
    {
      "field": "files",
      "message": "Phải có ít nhất một file"
    }
  ]
}
```

#### 404 Not Found - Folder Not Found
```json
{
  "code": 404,
  "message": "Không tìm thấy thư mục với ID 999"
}
```

#### 500 Internal Server Error
```json
{
  "code": 500,
  "message": "Lỗi khi tạo nhiều file: Database connection failed"
}
```

## Lưu ý

1. **Thư mục mặc định**: Nếu không cung cấp `folderId`, hệ thống sẽ tự động tạo file trong thư mục gốc "Files" của người dùng.

2. **Warehouse**: Nếu không cung cấp `warehouseId`, hệ thống sẽ tìm warehouse đầu tiên của người dùng hoặc báo lỗi nếu không có.

3. **Upload URL**: Mỗi file sẽ có `uploadUrl` với thời gian hết hạn 10 phút để client upload file thực tế.

4. **View URL**: Mỗi file sẽ có `viewUrl` với thời gian hết hạn 1 ngày để xem file.

5. **File types**: Hệ thống hỗ trợ các loại file: PDF, DOC, DOCX, PPTX, JSON, HTML, TXT.

6. **Batch processing**: Tất cả file sẽ được tạo trong một transaction, nếu có lỗi thì toàn bộ quá trình sẽ được rollback.

## Ví dụ sử dụng với cURL

```bash
curl -X POST "https://api.example.com/v1/user/files-multiple" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "warehouseId": 1,
    "folderId": 5,
    "files": [
      {
        "name": "document1.pdf",
        "size": 1024000
      },
      {
        "name": "document2.docx",
        "size": 2048000
      }
    ]
  }'
```

## Ví dụ sử dụng với JavaScript

```javascript
const response = await fetch('/v1/user/files-multiple', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    warehouseId: 1,
    folderId: 5,
    files: [
      { name: 'document1.pdf', size: 1024000 },
      { name: 'document2.docx', size: 2048000 }
    ]
  })
});

const result = await response.json();
console.log('Created files:', result.result.files);
```
