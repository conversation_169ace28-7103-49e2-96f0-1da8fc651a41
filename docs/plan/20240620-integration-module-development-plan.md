# <PERSON>ế hoạch Phát triển Module Integration

## Báo cáo Tiến độ

### 2024-06-20
- <PERSON><PERSON> tạo các entity cho tích hợp WhatsApp: WhatsAppAccount, WhatsAppTemplate, WhatsAppMessage, WhatsAppWebhookLog
- Đã tạo các repository tương ứng cho các entity WhatsApp
- Đã tạo các DTO cho tích hợp WhatsApp: WhatsAppAccountQueryDto, CreateWhatsAppAccountDto, UpdateWhatsAppAccountDto, WhatsAppAccountResponseDto, ConnectWhatsAppAgentDto, CreateWhatsAppTemplateDto, WhatsAppTemplateResponseDto, SendWhatsAppMessageDto, WhatsAppMessageResponseDto
- Đã tạo service WhatsAppUserService với các phương thức: findWhatsAppAccounts, findWhatsAppAccountById, createWhatsAppAccount, updateWhatsAppAccount, deleteWhatsAppAccount, connectAgent, disconnectAgent, sendMessage
- Đã tạo controller WhatsAppUserController với các endpoint tương ứng
- Đã cập nhật file mã lỗi cho module integration
- Đã tạo file migration SQL cho các bảng WhatsApp
- Đã tạo tài liệu API cho tích hợp WhatsApp

## 1. Tổng quan

### 1.1. Mục tiêu

Mở rộng module Integration hiện tại để hỗ trợ thêm nhiều kênh giao tiếp và dịch vụ bên ngoài, giúp người dùng có thể tích hợp hệ thống với nhiều nền tảng phổ biến tại Việt Nam và quốc tế.

### 1.2. Hiện trạng

Module Integration hiện đã hỗ trợ các tích hợp sau:
- Nhà cung cấp AI (OpenAI, Claude, v.v.)
- Cấu hình máy chủ Email (SMTP)
- Cấu hình máy chủ SMS
- Cổng thanh toán
- Tích hợp Facebook Page
- Quản lý website
- Tích hợp Telegram Bot
- Tích hợp Google Ads

### 1.3. Phạm vi phát triển

Kế hoạch này tập trung vào việc phát triển 5 tính năng tích hợp mới và nâng cấp 2 tích hợp hiện có:

**Tích hợp mới:**
1. Tích hợp WhatsApp Business API
2. Tích hợp Zalo OA
3. Quản lý SMS Brandname và Chiến dịch
4. Hệ thống Quản lý Webhook
5. Marketplace Tích hợp

**Nâng cấp tích hợp hiện có:**
1. Nâng cao tính năng Bot Telegram
2. Nâng cao Quản lý Nhà cung cấp AI

## 2. Lộ trình Phát triển

### 2.1. Giai đoạn 1: Chuẩn bị và Thiết kế (2 tuần)

#### Tuần 1: Phân tích và Thiết kế
- Phân tích chi tiết yêu cầu cho từng tích hợp
- Thiết kế cấu trúc database cho các entity mới
- Thiết kế API endpoints và DTOs
- Xác định các thư viện và dependencies cần thiết

#### Tuần 2: Chuẩn bị Môi trường và Tài liệu
- Cài đặt và cấu hình các thư viện cần thiết
- Tạo các migration scripts cho database
- Viết tài liệu kỹ thuật chi tiết cho từng tích hợp
- Thiết lập môi trường phát triển và kiểm thử

### 2.2. Giai đoạn 2: Phát triển Tích hợp Cốt lõi (6 tuần)

#### Tuần 3-4: Tích hợp WhatsApp Business API
- Tạo các entity cần thiết (WhatsAppAccount, WhatsAppTemplate, WhatsAppMessage)
- Phát triển service kết nối với WhatsApp Business API
- Xây dựng controller và endpoints cho người dùng
- Phát triển tính năng kết nối WhatsApp với agent
- Viết unit tests và integration tests

#### Tuần 5-6: Tích hợp Zalo OA
- Tạo các entity cần thiết (ZaloOfficialAccount, ZaloTemplate, ZaloMessage)
- Phát triển service kết nối với Zalo Open API
- Xây dựng controller và endpoints cho người dùng
- Phát triển tính năng kết nối Zalo OA với agent
- Viết unit tests và integration tests

#### Tuần 7-8: Quản lý SMS Brandname và Chiến dịch
- Mở rộng entity SmsServerConfiguration hiện có
- Tạo entity mới (SmsBrandname, SmsTemplate, SmsCampaign)
- Phát triển service quản lý brandname và chiến dịch
- Xây dựng controller và endpoints cho người dùng
- Viết unit tests và integration tests

### 2.3. Giai đoạn 3: Phát triển Tính năng Nâng cao (4 tuần)

#### Tuần 9-10: Nâng cao Tích hợp hiện có
- Nâng cao tính năng Bot Telegram
  - Thêm hỗ trợ đa phương tiện và nút tương tác
  - Phát triển tính năng quản lý lệnh nâng cao
  - Thêm tính năng phân tích sử dụng bot
- Nâng cao Quản lý Nhà cung cấp AI
  - Thêm hỗ trợ cho các nhà cung cấp AI mới
  - Phát triển tính năng quản lý fine-tuning
  - Thêm tính năng theo dõi sử dụng và chi phí

#### Tuần 11-12: Hệ thống Quản lý Webhook và Marketplace
- Phát triển Hệ thống Quản lý Webhook
  - Tạo entity WebhookEndpoint và WebhookLog
  - Phát triển service quản lý webhook
  - Xây dựng controller và endpoints
- Phát triển Marketplace Tích hợp
  - Tạo entity IntegrationApp và IntegrationReview
  - Phát triển service quản lý marketplace
  - Xây dựng controller và endpoints

### 2.4. Giai đoạn 4: Kiểm thử và Tối ưu hóa (2 tuần)

#### Tuần 13: Kiểm thử Toàn diện
- Thực hiện kiểm thử end-to-end cho tất cả tích hợp
- Kiểm thử hiệu suất và khả năng mở rộng
- Kiểm thử bảo mật cho tất cả API endpoints
- Sửa lỗi và tối ưu hóa code

#### Tuần 14: Hoàn thiện và Tài liệu
- Hoàn thiện tài liệu API cho từng tích hợp
- Tạo hướng dẫn sử dụng cho người dùng
- Chuẩn bị tài liệu triển khai
- Đánh giá cuối cùng và sẵn sàng cho production

## 3. Chi tiết Kỹ thuật

### 3.1. Tích hợp WhatsApp Business API

#### 3.1.1. Entities
```typescript
@Entity('whatsapp_accounts')
export class WhatsAppAccount {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'phone_number_id', length: 255 })
  phoneNumberId: string;

  @Column({ name: 'phone_number', length: 20 })
  phoneNumber: string;

  @Column({ name: 'business_account_id', length: 255 })
  businessAccountId: string;

  @Column({ name: 'access_token', type: 'text' })
  accessToken: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string;

  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
```

#### 3.1.2. API Endpoints
- `POST /integration/user/whatsapp/connect` - Kết nối tài khoản WhatsApp Business
- `GET /integration/user/whatsapp/accounts` - Liệt kê tài khoản WhatsApp đã kết nối
- `POST /integration/user/whatsapp/templates` - Tạo mẫu tin nhắn
- `GET /integration/user/whatsapp/templates` - Liệt kê mẫu tin nhắn
- `POST /integration/user/whatsapp/send` - Gửi tin nhắn WhatsApp
- `POST /integration/user/whatsapp/connect-agent` - Kết nối WhatsApp với agent

### 3.2. Tích hợp Zalo OA

#### 3.2.1. Entities
```typescript
@Entity('zalo_official_accounts')
export class ZaloOfficialAccount {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id', length: 255 })
  oaId: string;

  @Column({ name: 'oa_name', length: 255 })
  oaName: string;

  @Column({ name: 'access_token', type: 'text' })
  accessToken: string;

  @Column({ name: 'refresh_token', type: 'text' })
  refreshToken: string;

  @Column({ name: 'token_expires_at', type: 'bigint' })
  tokenExpiresAt: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string;

  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
```

#### 3.2.2. API Endpoints
- `POST /integration/user/zalo/connect` - Kết nối Zalo Official Account
- `GET /integration/user/zalo/accounts` - Liệt kê tài khoản Zalo đã kết nối
- `POST /integration/user/zalo/templates` - Tạo mẫu tin nhắn
- `GET /integration/user/zalo/templates` - Liệt kê mẫu tin nhắn
- `POST /integration/user/zalo/send` - Gửi tin nhắn Zalo
- `POST /integration/user/zalo/connect-agent` - Kết nối Zalo với agent

### 3.3. Quản lý SMS Brandname và Chiến dịch

#### 3.3.1. Entities
```typescript
@Entity('sms_brandnames')
export class SmsBrandname {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'brandname', length: 11 })
  brandname: string;

  @Column({ name: 'provider_id' })
  providerId: number;

  @Column({ name: 'status', length: 20 })
  status: string;

  @Column({ name: 'approval_documents', type: 'jsonb', default: '[]' })
  approvalDocuments: any[];

  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
```

#### 3.3.2. API Endpoints
- `POST /integration/user/sms/brandname` - Đăng ký SMS brandname
- `GET /integration/user/sms/brandname` - Kiểm tra trạng thái brandname
- `POST /integration/user/sms/templates` - Tạo mẫu SMS
- `POST /integration/user/sms/campaigns` - Tạo chiến dịch SMS
- `GET /integration/user/sms/campaigns/analytics` - Lấy phân tích chiến dịch

### 3.4. Nâng cao Tính năng Bot Telegram

#### 3.4.1. Tính năng mới
- Hỗ trợ đa phương tiện (ảnh, video, tài liệu)
- Nút tương tác và bàn phím inline
- Quản lý lệnh nâng cao với phản hồi động
- Phân tích sử dụng bot

#### 3.4.2. API Endpoints
- `POST /integration/user/telegram/commands` - Tạo lệnh tùy chỉnh
- `GET /integration/user/telegram/commands` - Liệt kê lệnh tùy chỉnh
- `POST /integration/user/telegram/media/send` - Gửi tin nhắn đa phương tiện
- `POST /integration/user/telegram/keyboard` - Tạo bàn phím tương tác
- `GET /integration/user/telegram/analytics` - Lấy thống kê sử dụng bot

### 3.5. Hệ thống Quản lý Webhook

#### 3.5.1. Entities
```typescript
@Entity('webhook_endpoints')
export class WebhookEndpoint {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'name', length: 255 })
  name: string;

  @Column({ name: 'url', type: 'text' })
  url: string;

  @Column({ name: 'events', type: 'text', array: true })
  events: string[];

  @Column({ name: 'secret_key', length: 255, nullable: true })
  secretKey: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
```

#### 3.5.2. API Endpoints
- `POST /integration/user/webhooks` - Tạo endpoint webhook
- `GET /integration/user/webhooks` - Liệt kê endpoint webhook
- `POST /integration/user/webhooks/test` - Kiểm tra gửi webhook
- `GET /integration/user/webhooks/logs` - Lấy nhật ký gửi webhook

## 4. Yêu cầu Kỹ thuật

### 4.1. Công nghệ và Thư viện
- NestJS framework
- TypeORM cho tương tác database
- Axios cho HTTP requests
- Jest cho unit testing
- Swagger cho API documentation
- Các thư viện SDK chính thức:
  - WhatsApp Business API SDK
  - Zalo Open API SDK
  - Telegram Bot API SDK

### 4.2. Yêu cầu Bảo mật
- Mã hóa tất cả các token và khóa API trước khi lưu vào database
- Xác thực webhook bằng chữ ký và secret key
- Xác thực tất cả API endpoints bằng JWT
- Kiểm tra quyền sở hữu tài nguyên cho mọi request
- Tuân thủ các quy định về bảo mật dữ liệu của từng nền tảng

### 4.3. Yêu cầu Hiệu suất
- Xử lý webhook với độ trễ tối thiểu (<500ms)
- Hỗ trợ gửi tin nhắn hàng loạt (>1000 tin nhắn/phút)
- Tối ưu hóa truy vấn database để giảm thời gian phản hồi
- Caching cho các dữ liệu thường xuyên truy cập
- Xử lý bất đồng bộ cho các tác vụ nặng

## 5. Rủi ro và Giảm thiểu

| Rủi ro | Mức độ | Giảm thiểu |
|--------|--------|------------|
| Thay đổi API của bên thứ ba | Cao | Thiết kế adapter pattern để dễ dàng thay đổi, theo dõi thông báo từ nhà cung cấp |
| Vấn đề hiệu suất khi mở rộng | Trung bình | Thiết kế hệ thống có khả năng mở rộng, kiểm thử hiệu suất thường xuyên |
| Bảo mật dữ liệu nhạy cảm | Cao | Mã hóa dữ liệu, kiểm tra bảo mật thường xuyên, tuân thủ các tiêu chuẩn bảo mật |
| Độ tin cậy của dịch vụ bên thứ ba | Trung bình | Triển khai cơ chế thử lại, giám sát dịch vụ, cung cấp phương án dự phòng |
| Tuân thủ quy định pháp lý | Cao | Nghiên cứu kỹ các quy định, tham khảo ý kiến chuyên gia pháp lý khi cần thiết |

## 6. Tài nguyên và Phân công

### 6.1. Nhân sự
- 1 Tech Lead: Phụ trách thiết kế tổng thể và review code
- 2 Backend Developers: Phát triển API và services
- 1 QA Engineer: Kiểm thử và đảm bảo chất lượng
- 1 DevOps Engineer: Hỗ trợ triển khai và CI/CD

### 6.2. Công cụ và Môi trường
- Git cho quản lý mã nguồn
- JIRA cho quản lý dự án và theo dõi tiến độ
- Docker cho môi trường phát triển và kiểm thử
- Jenkins hoặc GitHub Actions cho CI/CD
- Postman cho kiểm thử API

## 7. Kết luận

Kế hoạch phát triển này đề xuất một lộ trình toàn diện để mở rộng module Integration với nhiều tích hợp mới và nâng cao các tích hợp hiện có. Việc triển khai thành công sẽ giúp hệ thống hỗ trợ nhiều kênh giao tiếp phổ biến tại Việt Nam và quốc tế, nâng cao giá trị sản phẩm cho người dùng.

Với thời gian phát triển dự kiến là 14 tuần, kế hoạch này được chia thành các giai đoạn rõ ràng với các mục tiêu cụ thể, giúp đảm bảo tiến độ và chất lượng của dự án. Các rủi ro đã được xác định và có biện pháp giảm thiểu phù hợp.
