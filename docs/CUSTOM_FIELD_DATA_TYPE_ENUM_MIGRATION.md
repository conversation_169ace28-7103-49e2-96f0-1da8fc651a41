# Custom Field Data Type Enum Migration

## Tổng quan

Migration này cập nhật cột `data_type` từ `VARCHAR(50)` thành `ENUM` trong các bảng:
- `audience_user_custom_fields`
- `audience_admin_custom_fields`

## Thay đổi

### Enum Values Mới
```sql
CREATE TYPE custom_field_data_type_enum AS ENUM (
    'text',     -- thay thế 'string'
    'number',   -- thay thế 'integer'
    'boolean',  -- giữ nguyên
    'date',     -- giữ nguyên
    'select',   -- mới
    'object'    -- thay thế 'json'
);
```

### Mapping Dữ liệu
| Giá trị cũ | Gi<PERSON> trị mới |
|------------|-------------|
| `string`   | `text`      |
| `integer`  | `number`    |
| `boolean`  | `boolean`   |
| `date`     | `date`      |
| `json`     | `object`    |
| -          | `select`    |

## C<PERSON>ch chạy Migration

### Sử dụng <PERSON> (Khuyến nghị)

#### Linux/macOS
```bash
chmod +x scripts/run-custom-field-data-type-migration.sh
./scripts/run-custom-field-data-type-migration.sh
```

#### Windows PowerShell
```powershell
.\scripts\run-custom-field-data-type-migration.ps1
```

### Manual SQL
```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f src/database/migrations/update-custom-field-data-type-to-enum.sql
```

### TypeORM Migration
```bash
npm run migration:run
```

## Rollback

Nếu cần rollback migration:

### Sử dụng SQL
```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f src/database/migrations/rollback-custom-field-data-type-enum.sql
```

### TypeORM Rollback
```bash
npm run migration:revert
```

## Kiểm tra Migration

### Kiểm tra Enum đã được tạo
```sql
SELECT 
    t.typname,
    e.enumlabel
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname = 'custom_field_data_type_enum'
ORDER BY e.enumsortorder;
```

### Kiểm tra cột đã được cập nhật
```sql
-- Kiểm tra audience_user_custom_fields
SELECT 
    column_name, 
    data_type, 
    udt_name,
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'audience_user_custom_fields' 
AND column_name = 'data_type';

-- Kiểm tra audience_admin_custom_fields
SELECT 
    column_name, 
    data_type, 
    udt_name,
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'audience_admin_custom_fields' 
AND column_name = 'data_type';
```

## Tác động đến Code

### Entity Changes
- `UserAudienceCustomFieldDefinition.dataType`: `string` → `CustomFieldDataType`
- `AdminAudienceCustomFieldDefinition.dataType`: `string` → `CustomFieldDataType`

### DTO Changes
- Tất cả DTOs đã được cập nhật để sử dụng enum `CustomFieldDataType`
- Examples trong API documentation đã được cập nhật

### Service Changes
- Loại bỏ type casting `as any` trong mapping functions

## Lưu ý quan trọng

1. **Backup Database**: Luôn backup database trước khi chạy migration
2. **Downtime**: Migration này có thể gây downtime ngắn
3. **Data Validation**: Migration sẽ tự động convert dữ liệu hiện có
4. **API Compatibility**: Các API endpoints sẽ chấp nhận enum values mới

## Troubleshooting

### Lỗi "enum value not found"
Nếu có dữ liệu với giá trị không hợp lệ, migration sẽ fail. Kiểm tra:
```sql
SELECT DISTINCT data_type 
FROM audience_user_custom_fields 
WHERE data_type NOT IN ('string', 'integer', 'boolean', 'date', 'json');

SELECT DISTINCT data_type 
FROM audience_admin_custom_fields 
WHERE data_type NOT IN ('string', 'integer', 'boolean', 'date', 'json');
```

### Rollback không thành công
Nếu rollback fail, có thể cần manual cleanup:
```sql
-- Xóa enum type nếu còn tồn tại
DROP TYPE IF EXISTS custom_field_data_type_enum CASCADE;
```
