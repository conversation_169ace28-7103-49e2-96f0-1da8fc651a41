# Audience Country Code Feature - Triển Khai Hoàn Thành

## Tổng Quan
Đã triển khai thành công tính năng thêm trường `countryCode` vào audience entities và APIs cho cả user và admin modules.

## Các Thay Đổi Đã Thực Hiện

### 1. Database Schema
- **Thêm cột `country_code`** vào bảng `user_audience` và `admin_audience`
- **Kiểu dữ liệu**: VARCHAR(10)
- **Giá trị mặc định**: '+84'
- **Nullable**: true

### 2. Entity Updates
- **`UserAudience`**: Thêm trường `countryCode: string`
- **`AdminAudience`**: Thêm trường `countryCode: string`

### 3. DTO Updates

#### User Module
- **`CreateAudienceDto`**: Thêm trường `countryCode?: string` với validation
- **`UpdateAudienceDto`**: Thêm trường `countryCode?: string` với validation
- **`AudienceResponseDto`**: Thêm trường `countryCode: string | null`

#### Admin Module
- **`CreateAudienceDto`**: Thêm trường `countryCode?: string` với validation
- **`UpdateAudienceDto`**: Thêm trường `countryCode?: string` với validation
- **`AudienceResponseDto`**: Thêm trường `countryCode: string | null`

#### Integration Module
- **`CreateIntegrationAudienceDto`**: Thêm trường `countryCode?: string` với validation
- **`IntegrationAudienceResponseDto`**: Thêm trường `countryCode: string`

### 4. Service Updates
- **`UserAudienceService`**: Cập nhật logic create, update và mapToDto
- **`AdminAudienceService`**: Cập nhật logic create, update và mapToDto
- **`IntegrationAudienceService`**: Cập nhật logic create và response mapping

### 5. Validation Rules
- **Pattern**: `/^\+\d{1,4}$/` (ví dụ: +84, +1, +86)
- **Optional**: Trường không bắt buộc
- **Default**: '+84' nếu không được cung cấp

## Migration Files

### SQL Migration
```bash
src/database/migrations/add-country-code-to-audience-tables.sql
```

### TypeORM Migration
```bash
src/database/migrations/1720000000005-AddCountryCodeToAudienceTables.ts
```

### Rollback Migration
```bash
src/database/migrations/rollback-country-code-from-audience-tables.sql
```

## Cách Chạy Migration

### Linux/macOS
```bash
chmod +x scripts/run-audience-country-code-migration.sh
./scripts/run-audience-country-code-migration.sh
```

### Windows PowerShell
```powershell
.\scripts\run-audience-country-code-migration.ps1
```

### Manual SQL
```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f src/database/migrations/add-country-code-to-audience-tables.sql
```

## API Usage Examples

### User Module - Tạo Audience
```bash
POST /user/marketing/audiences
```

```json
{
  "name": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "phone": "912345678",
  "countryCode": "+84",
  "customFields": [],
  "tagIds": [1, 2]
}
```

### Admin Module - Tạo Audience
```bash
POST /admin/marketing/audiences
```

```json
{
  "name": "Trần Thị B",
  "email": "<EMAIL>",
  "phone": "987654321",
  "countryCode": "+84",
  "customFields": [],
  "tagIds": [1, 2]
}
```

### Integration Module - Tạo Audience
```bash
POST /admin/integration/audiences
```

```json
{
  "name": "Lê Văn C",
  "email": "<EMAIL>",
  "phone": "123456789",
  "countryCode": "+84",
  "tagIds": [1, 2]
}
```

## Response Examples

### User/Admin Audience Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phone": "912345678",
    "countryCode": "+84",
    "customFields": [],
    "tags": [],
    "createdAt": 1640995200,
    "updatedAt": 1640995200
  },
  "message": "Audience đã được tạo thành công"
}
```

### Integration Audience Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Lê Văn C",
    "email": "<EMAIL>",
    "phone": "123456789",
    "countryCode": "+84",
    "tagIds": [1, 2],
    "createdAt": 1640995200,
    "updatedAt": 1640995200,
    "message": "Audience đã được tạo thành công thông qua integration admin"
  },
  "message": "Audience đã được tạo thành công thông qua integration admin"
}
```

## Validation Errors

### Invalid Country Code
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Mã quốc gia không hợp lệ (ví dụ: +84, +1)"
  }
}
```

## Rollback Instructions

Nếu cần rollback migration:

```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f src/database/migrations/rollback-country-code-from-audience-tables.sql
```

## Testing

Sau khi triển khai, hãy test các API sau:
1. Tạo audience với countryCode hợp lệ
2. Tạo audience không có countryCode (sử dụng default +84)
3. Tạo audience với countryCode không hợp lệ (kiểm tra validation)
4. Cập nhật audience với countryCode mới
5. Lấy danh sách audience và kiểm tra trường countryCode trong response

## Notes

- Trường `countryCode` là optional trong tất cả create/update DTOs
- Giá trị mặc định là '+84' nếu không được cung cấp
- Validation pattern đảm bảo format đúng: +[1-4 digits]
- Tất cả response DTOs đều bao gồm trường countryCode
