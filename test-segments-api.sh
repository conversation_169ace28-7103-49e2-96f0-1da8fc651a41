#!/bin/bash

# Script test API Segments
# Sử dụng: ./test-segments-api.sh [BASE_URL] [JWT_TOKEN]

BASE_URL=${1:-"http://localhost:3003"}
JWT_TOKEN=${2:-"YOUR_JWT_TOKEN_HERE"}

API_ENDPOINT="$BASE_URL/v1/marketing/segments"

echo "🚀 Testing Segments API"
echo "📍 Endpoint: $API_ENDPOINT"
echo "🔑 Token: ${JWT_TOKEN:0:20}..."
echo ""

# Test 1: L<PERSON>y danh sách segments
echo "📝 Test 1: Lấy danh sách segments"
curl -X GET "$API_ENDPOINT?page=1&limit=10" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 2: <PERSON><PERSON>y danh sách segments với search
echo "📝 Test 2: L<PERSON><PERSON> danh sách segments với search"
curl -X GET "$API_ENDPOINT?page=1&limit=10&search=Middle+Aged" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 3: Tạo segment mới
echo "📝 Test 3: Tạo segment mới"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Segment with Audience Count",
    "description": "Test segment để kiểm tra audience count",
    "criteria": {
      "conditionType": "and",
      "conditions": [
        {
          "field": "email",
          "operator": "contains",
          "value": "@gmail.com"
        }
      ]
    }
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n"
echo "✅ Test completed!"
echo ""
echo "📋 Expected results:"
echo "  - Test 1: 200 (Success) - Should return segments with audienceCount field"
echo "  - Test 2: 200 (Success) - Should return filtered segments with audienceCount field"
echo "  - Test 3: 201 (Success) - Should return created segment with audienceCount field"
