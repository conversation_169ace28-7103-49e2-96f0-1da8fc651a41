# Test Custom Field API với Select Type

## Test Case: Tạo select field với input từ FE

### Request Body từ FE:
```json
{
  "config": {
    "id": "gioitinh",
    "label": "Gioi tinh",
    "displayName": "Gioi tinh",
    "type": "select",
    "required": false,
    "validation": {},
    "options": [
      {
        "label": "a|1\nb|2",
        "value": "a|1_b|2"
      }
    ]
  }
}
```

### Expected Response (FIXED):
```json
{
  "code": 200,
  "message": "Tạo trường tùy chỉnh thành công",
  "result": {
    "id": 116,
    "configId": "gioitinh",
    "label": "Gioi tinh",
    "type": "select",
    "required": false,
    "configJson": {
      "id": "gioitinh",
      "label": "Gioi tinh",
      "type": "select",
      "validation": {
        "options": [
          {
            "label": "a",
            "value": "1"
          },
          {
            "label": "b",
            "value": "2"
          }
        ]
      },
      "displayName": "Gioi tinh",
      "options": [
        {
          "label": "a",
          "value": "1"
        },
        {
          "label": "b",
          "value": "2"
        }
      ]
    },
    "userId": 1,
    "employeeId": null,
    "status": "PENDING",
    "createAt": "1749634034145",
    "tags": []
  }
}
```

## Giải thích thay đổi:

1. **Thứ tự xử lý trong configJson**: 
   - Đầu tiên thêm các thuộc tính khác từ request
   - Sau đó thêm placeholder, defaultValue
   - Cuối cùng thêm validation đã được xử lý (sẽ ghi đè nếu có conflict)

2. **parseNameValueValidation**: 
   - Input: `"a|1\nb|2"`
   - Output: `{ options: [{ label: "a", value: "1" }, { label: "b", value: "2" }] }`

3. **displayName**: Tự động thêm từ label khi có options

## Test với nhiều options:

### Request:
```json
{
  "config": {
    "id": "kich-thuoc",
    "label": "Kích thước",
    "type": "select",
    "required": true,
    "validation": "XS|xs\nS|s\nM|m\nL|l\nXL|xl"
  }
}
```

### Expected Response:
```json
{
  "configJson": {
    "validation": {
      "options": [
        { "label": "XS", "value": "xs" },
        { "label": "S", "value": "s" },
        { "label": "M", "value": "m" },
        { "label": "L", "value": "l" },
        { "label": "XL", "value": "xl" }
      ]
    },
    "displayName": "Kích thước"
  }
}
```
