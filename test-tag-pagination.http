### Test User Tag API với phân trang

### 1. <PERSON><PERSON><PERSON> danh sách tag với phân trang (mặc định)
GET http://localhost:3000/v1/marketing/tags
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 2. <PERSON><PERSON><PERSON> danh sách tag với phân trang và tham số
GET http://localhost:3000/v1/marketing/tags?page=1&limit=5&sortBy=name&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 3. Tìm kiếm tag theo tên
GET http://localhost:3000/v1/marketing/tags?name=VIP&page=1&limit=10
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 4. L<PERSON>y tất cả tag (không phân trang) - deprecated
GET http://localhost:3000/v1/marketing/tags/all
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 5. Tạo tag mới để test
POST http://localhost:3000/v1/marketing/tags
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test Tag 1",
  "color": "#FF5733"
}

### 6. Tạo thêm tag để test phân trang
POST http://localhost:3000/v1/marketing/tags
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "VIP Customer",
  "color": "#FFD700"
}

### Test Admin Tag API với phân trang

### 7. Lấy danh sách tag admin với phân trang (mặc định)
GET http://localhost:3000/v1/admin/marketing/tags
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN_HERE

### 8. Lấy danh sách tag admin với phân trang và tham số
GET http://localhost:3000/v1/admin/marketing/tags?page=1&limit=5&sortBy=name&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN_HERE

### 9. Tìm kiếm tag admin theo tên
GET http://localhost:3000/v1/admin/marketing/tags?name=VIP&page=1&limit=10
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN_HERE

### 10. Tạo tag admin mới để test
POST http://localhost:3000/v1/admin/marketing/tags
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN_HERE

{
  "name": "Admin Test Tag"
}
