# Cập nhật API Segments - Thêm Audience Count

## Tổng quan

Đã cập nhật API `GET /v1/marketing/segments` để trả về thêm thông tin số lượng đối tượng (audience count) cho mỗi segment.

## Thay đổi thực hiện

### 1. Cập nhật DTO Response

**File:** `src/modules/marketing/user/dto/segment/segment-response.dto.ts`

Thêm trường `audienceCount` vào `SegmentResponseDto`:

```typescript
/**
 * Số lượng khách hàng trong segment
 * @example 150
 */
@ApiProperty({
  description: 'Số lượng khách hàng trong segment',
  example: 150,
})
audienceCount?: number;
```

### 2. Cập nhật Service Logic

**File:** `src/modules/marketing/user/services/user-segment.service.ts`

#### Thêm method đếm audience:
```typescript
private async countAudiencesInSegment(userId: number, segment: UserSegment): Promise<number>
```

#### Cập nhật mapToDto method:
```typescript
private mapToDto(segment: UserSegment, audienceCount?: number): SegmentResponseDto
```

#### Cập nhật các methods chính:
- `findAll()` - Tính audience count cho danh sách segments
- `findOne()` - Tính audience count cho segment đơn lẻ  
- `create()` - Tính audience count cho segment mới tạo
- `update()` - Tính audience count cho segment đã cập nhật

## API Response mới

### Trước khi cập nhật:
```json
{
  "id": 1,
  "name": "Middle Aged Customers", 
  "description": "Khách hàng trong độ tuổi trung niên",
  "criteria": {...},
  "createdAt": 1619171200,
  "updatedAt": 1619171200
}
```

### Sau khi cập nhật:
```json
{
  "id": 1,
  "name": "Middle Aged Customers",
  "description": "Khách hàng trong độ tuổi trung niên", 
  "criteria": {...},
  "createdAt": 1619171200,
  "updatedAt": 1619171200,
  "audienceCount": 150
}
```

## Cách test

### Sử dụng PowerShell (Windows):
```powershell
.\test-segments-api.ps1 -BaseUrl "http://localhost:3003" -JwtToken "YOUR_VALID_JWT_TOKEN"
```

### Sử dụng Bash (Linux/Mac):
```bash
./test-segments-api.sh "http://localhost:3003" "YOUR_VALID_JWT_TOKEN"
```

### Sử dụng cURL trực tiếp:
```bash
curl -X GET "http://localhost:3003/v1/marketing/segments?page=1&limit=10&search=Middle+Aged" \
  -H "Authorization: Bearer YOUR_VALID_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Lưu ý

1. **Backward Compatibility**: API vẫn tương thích ngược, chỉ thêm trường `audienceCount` mới
2. **Performance**: Việc tính audience count có thể ảnh hưởng đến performance nếu có nhiều segments và audiences
3. **Error Handling**: Nếu có lỗi khi tính audience count, giá trị sẽ là 0
4. **Authentication**: Cần JWT token hợp lệ để test API

## Files được tạo

- `test-segments-api.ps1` - Script test cho PowerShell
- `test-segments-api.sh` - Script test cho Bash
- `expected-response-example.json` - Ví dụ response mong đợi
- `SEGMENTS_API_AUDIENCE_COUNT_UPDATE.md` - Tài liệu này

## Kết quả mong đợi

Sau khi cập nhật, tất cả các API endpoints liên quan đến segments sẽ trả về thêm trường `audienceCount` cho biết số lượng đối tượng phù hợp với điều kiện của segment đó.
