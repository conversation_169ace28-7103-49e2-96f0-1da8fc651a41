# API Tạo Email Campaign với Template

## Tổng quan

API này cho phép tạo chiến dịch email marketing mới sử dụng template có sẵn và email server configuration đã được cấu hình trước.

## Endpoint

```
POST /api/v1/marketing/email-campaigns/with-template
```

## Authentication

- **Method**: JWT Bearer Token
- **Header**: `Authorization: Bearer <token>`
- **Guard**: `JwtUserGuard`

## Request Body

```typescript
{
  "title": string,              // Tiêu đề campaign (bắt buộc)
  "description"?: string,       // <PERSON><PERSON> tả campaign (tùy chọn)
  "templateEmailId": string,    // ID của email template (bắt buộc)
  "segmentId": string,          // ID của segment để gửi email (bắt buộc)
  "scheduledAt"?: number,       // Thời gian dự kiến gửi Unix timestamp (tùy chọn)
  "serverId": string           // ID của email server configuration (bắt buộc)
}
```

### Validation Rules

- `title`: Kh<PERSON>ng được để trống, phải là chuỗi
- `description`: Tùy chọn, phải là chuỗi nếu có
- `templateEmailId`: Không được để trống, phải là chuỗi
- `segmentId`: Không được để trống, phải là chuỗi  
- `scheduledAt`: Tùy chọn, phải là số và trong tương lai
- `serverId`: Không được để trống, phải là chuỗi

## Response

### Success (201)

```typescript
{
  "success": true,
  "message": "Email campaign với template đã được tạo thành công với {jobCount} jobs. Campaign sẽ được xử lý bởi worker.",
  "data": {
    "campaignId": number,
    "jobCount": number,
    "jobIds": string[],
    "scheduledAt"?: number,
    "status": string,
    "template": {
      "id": number,
      "name": string,
      "subject": string
    },
    "emailServer": {
      "id": number,
      "serverName": string,
      "host": string
    }
  }
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ hoặc thời gian gửi không hợp lệ"
}
```

#### 404 - Not Found
```json
{
  "success": false,
  "message": "Template email, segment hoặc email server configuration không tồn tại"
}
```

## Ví dụ sử dụng

### Request
```bash
curl -X POST http://localhost:3000/api/v1/marketing/email-campaigns/with-template \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "Khuyến mãi Black Friday 2024",
    "description": "Chiến dịch email marketing cho sự kiện Black Friday",
    "templateEmailId": "123",
    "segmentId": "456",
    "serverId": "789",
    "scheduledAt": 1703980800
  }'
```

### Response
```json
{
  "success": true,
  "message": "Email campaign với template đã được tạo thành công với 150 jobs. Campaign sẽ được xử lý bởi worker.",
  "data": {
    "campaignId": 1,
    "jobCount": 150,
    "jobIds": ["job_1", "job_2", "job_3"],
    "scheduledAt": 1703980800,
    "status": "SCHEDULED",
    "template": {
      "id": 123,
      "name": "Welcome Email Template",
      "subject": "Chào mừng bạn đến với RedAI"
    },
    "emailServer": {
      "id": 789,
      "serverName": "Gmail SMTP Server",
      "host": "smtp.gmail.com"
    }
  }
}
```

## Logic nghiệp vụ

1. **Validate template email**: Kiểm tra template tồn tại và thuộc về user
2. **Validate email server**: Kiểm tra email server configuration tồn tại và thuộc về user
3. **Validate segment**: Kiểm tra segment tồn tại
4. **Tạo campaign**: Lưu campaign vào database với thông tin từ template và server
5. **Lấy audience**: Lấy danh sách audience từ segment
6. **Tạo jobs**: Tạo jobs cho queue để worker xử lý gửi email
7. **Trả về kết quả**: Thông tin campaign và số lượng jobs đã tạo

## So sánh với API cũ

| Tính năng | API cũ | API mới |
|-----------|--------|---------|
| Template | Truyền content trực tiếp | Sử dụng templateEmailId |
| Server config | Truyền config trực tiếp | Sử dụng serverId |
| Validation | Ít hơn | Đầy đủ hơn |
| Response | Cơ bản | Chi tiết với thông tin template và server |

## Lưu ý

- API này yêu cầu template email và email server configuration đã được tạo trước
- Thời gian `scheduledAt` phải là Unix timestamp trong tương lai
- Campaign sẽ có status `SCHEDULED` nếu có `scheduledAt`, ngược lại là `SENDING`
- Jobs sẽ được đẩy vào queue để worker xử lý bất đồng bộ
