# Chi tiết Swagger Select Options cho Custom Field API

## 1. Field Type Select Options

Trong Swagger, field `type` sẽ hiển thị dropdown với các options:

```typescript
enum: ['text', 'number', 'boolean', 'date', 'select', 'object', 'array']
```

### Select Options Details:
- **text**: Text Field (sử dụng object validation)
- **number**: Number Field (sử dụng object validation)  
- **boolean**: Boolean Field (sử dụng object validation)
- **date**: Date Field (sử dụng object validation)
- **select**: Select Field (hỗ trợ Name|Value validation)
- **object**: Object Field (hỗ trợ Name|Value validation)
- **array**: Array Field (hỗ trợ Name|Value validation)

## 2. Examples cho POST /v1/user/custom-fields

### 2.1 Text Field Example
```json
{
  "config": {
    "id": "text-input-001",
    "label": "Họ và tên",
    "type": "text",
    "required": true,
    "validation": {
      "minLength": 3,
      "maxLength": 50,
      "pattern": "^[a-zA-Z\\s]*$"
    },
    "defaultValue": "",
    "placeholder": "Nhập họ và tên",
    "variant": "outlined",
    "size": "small",
    "fullWidth": true
  }
}
```

### 2.2 Number Field Example
```json
{
  "config": {
    "id": "number-age-001",
    "label": "Tuổi",
    "type": "number",
    "required": true,
    "validation": {
      "min": 18,
      "max": 100
    },
    "defaultValue": 25,
    "placeholder": "Nhập tuổi",
    "variant": "outlined",
    "size": "medium"
  }
}
```

### 2.3 Boolean Field Example
```json
{
  "config": {
    "id": "boolean-featured-001",
    "label": "Sản phẩm nổi bật",
    "type": "boolean",
    "required": false,
    "validation": {},
    "defaultValue": false,
    "placeholder": "Đánh dấu nổi bật"
  }
}
```

### 2.4 Date Field Example
```json
{
  "config": {
    "id": "date-expiry-001",
    "label": "Ngày hết hạn",
    "type": "date",
    "required": false,
    "validation": {
      "minDate": "2024-01-01",
      "maxDate": "2030-12-31"
    },
    "defaultValue": null,
    "placeholder": "Chọn ngày hết hạn"
  }
}
```

### 2.5 Select Field Example (Name|Value)
```json
{
  "config": {
    "id": "select-size-001",
    "label": "Kích thước",
    "type": "select",
    "required": true,
    "validation": "XS|xs\nS|s\nM|m\nL|l\nXL|xl\nXXL|xxl",
    "defaultValue": "m",
    "placeholder": "Chọn kích thước"
  }
}
```

### 2.6 Object Field Example (Name|Value)
```json
{
  "config": {
    "id": "object-category-001",
    "label": "Danh mục",
    "type": "object",
    "required": false,
    "validation": "Điện tử|electronics\nThời trang|fashion\nGia dụng|household\nSách|books\nThể thao|sports",
    "defaultValue": {},
    "placeholder": "Chọn danh mục"
  }
}
```

### 2.7 Array Field Example (Name|Value)
```json
{
  "config": {
    "id": "array-tags-001",
    "label": "Tags sản phẩm",
    "type": "array",
    "required": false,
    "validation": "Hot|hot\nNew|new\nSale|sale\nTrending|trending\nBest Seller|bestseller",
    "defaultValue": [],
    "placeholder": "Chọn tags"
  }
}
```

## 3. Examples cho PUT /v1/user/custom-fields/:id

### 3.1 Update Text Field
```json
{
  "config": {
    "label": "Họ và tên đầy đủ",
    "type": "text",
    "required": true,
    "validation": {
      "minLength": 5,
      "maxLength": 100,
      "pattern": "^[a-zA-Z\\s]*$"
    },
    "defaultValue": "",
    "placeholder": "Nhập họ và tên đầy đủ",
    "variant": "outlined",
    "size": "medium"
  }
}
```

### 3.2 Update Select Field
```json
{
  "config": {
    "label": "Kích thước sản phẩm",
    "type": "select",
    "required": true,
    "validation": "Extra Small|xs\nSmall|s\nMedium|m\nLarge|l\nExtra Large|xl\nDouble XL|xxl",
    "defaultValue": "l",
    "placeholder": "Chọn kích thước sản phẩm"
  }
}
```

## 4. Validation Rules

### 4.1 Object Validation (text, number, boolean, date)
- **text**: `minLength`, `maxLength`, `pattern`
- **number**: `min`, `max`
- **boolean**: không cần validation đặc biệt
- **date**: `minDate`, `maxDate`

### 4.2 Name|Value Validation (select, object, array)
- Format: `"Name1|value1\nName2|value2\nName3|value3"`
- Mỗi dòng phải có định dạng `Name|Value`
- Name và Value không được để trống
- Tự động convert thành `{options: [{label, value}]}`

## 5. Response Format

Khi tạo/cập nhật thành công, Name|Value sẽ được convert:

**Input:**
```json
"validation": "XS|xs\nS|s\nM|m"
```

**Output trong database:**
```json
"validation": {
  "options": [
    {"label": "XS", "value": "xs"},
    {"label": "S", "value": "s"},
    {"label": "M", "value": "m"}
  ]
}
```
