# Script test API Segments cho PowerShell
# Sử dụng: .\test-segments-api.ps1 -BaseUrl "http://localhost:3003" -JwtToken "YOUR_JWT_TOKEN_HERE"

param(
    [string]$BaseUrl = "http://localhost:3003",
    [string]$JwtToken = "YOUR_JWT_TOKEN_HERE"
)

$ApiEndpoint = "$BaseUrl/v1/marketing/segments"

Write-Host "🚀 Testing Segments API" -ForegroundColor Green
Write-Host "📍 Endpoint: $ApiEndpoint" -ForegroundColor Cyan
Write-Host "🔑 Token: $($JwtToken.Substring(0, [Math]::Min(20, $JwtToken.Length)))..." -ForegroundColor Yellow
Write-Host ""

$Headers = @{
    "Authorization" = "Bearer $JwtToken"
    "Content-Type" = "application/json"
}

# Test 1: Lấy danh sách segments
Write-Host "📝 Test 1: Lấy danh sách segments" -ForegroundColor Blue
try {
    $Response1 = Invoke-RestMethod -Uri "$ApiEndpoint?page=1&limit=10" -Method GET -Headers $Headers
    Write-Host "✅ Status: 200 (Success)" -ForegroundColor Green
    Write-Host "📊 Response:" -ForegroundColor Cyan
    $Response1 | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📊 Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}

Write-Host "`n" -NoNewline
Start-Sleep -Seconds 2

# Test 2: Lấy danh sách segments với search
Write-Host "📝 Test 2: Lấy danh sách segments với search" -ForegroundColor Blue
try {
    $Response2 = Invoke-RestMethod -Uri "$ApiEndpoint?page=1&limit=10&search=Middle+Aged" -Method GET -Headers $Headers
    Write-Host "✅ Status: 200 (Success)" -ForegroundColor Green
    Write-Host "📊 Response:" -ForegroundColor Cyan
    $Response2 | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📊 Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}

Write-Host "`n" -NoNewline
Start-Sleep -Seconds 2

# Test 3: Tạo segment mới
Write-Host "📝 Test 3: Tạo segment mới" -ForegroundColor Blue
$CreateSegmentBody = @{
    name = "Test Segment with Audience Count"
    description = "Test segment để kiểm tra audience count"
    criteria = @{
        conditionType = "and"
        conditions = @(
            @{
                field = "email"
                operator = "contains"
                value = "@gmail.com"
            }
        )
    }
} | ConvertTo-Json -Depth 10

try {
    $Response3 = Invoke-RestMethod -Uri $ApiEndpoint -Method POST -Headers $Headers -Body $CreateSegmentBody
    Write-Host "✅ Status: 201 (Success)" -ForegroundColor Green
    Write-Host "📊 Response:" -ForegroundColor Cyan
    $Response3 | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📊 Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}

Write-Host "`n"
Write-Host "✅ Test completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Expected results:" -ForegroundColor Yellow
Write-Host "  - Test 1: 200 (Success) - Should return segments with audienceCount field" -ForegroundColor White
Write-Host "  - Test 2: 200 (Success) - Should return filtered segments with audienceCount field" -ForegroundColor White
Write-Host "  - Test 3: 201 (Success) - Should return created segment with audienceCount field" -ForegroundColor White
