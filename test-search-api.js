// Test script để kiểm tra API search
const axios = require('axios');

const BASE_URL = 'http://localhost:3003';

async function testSearchAPI() {
  try {
    console.log('Testing search API...');

    // Test với search parameter - API user converts
    const searchResponse = await axios.get(`${BASE_URL}/v1/user/converts`, {
      params: {
        page: 1,
        limit: 10,
        search: 'đ' // Test với từ khóa từ lỗi ban đầu
      },
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE' // Cần thay thế bằng token thực
      }
    });

    console.log('✅ Search API works! Response:', JSON.stringify(searchResponse.data, null, 2));

    // Test thêm với search khác
    const searchResponse2 = await axios.get(`${BASE_URL}/v1/user/converts`, {
      params: {
        page: 1,
        limit: 10,
        search: 'khách hàng'
      },
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'
      }
    });

    console.log('✅ Search API 2 works! Response:', JSON.stringify(searchResponse2.data, null, 2));

  } catch (error) {
    console.error('Error testing API:', error.response?.data || error.message);
    console.error('Full error:', error.message);
  }
}

// Chạy test
testSearchAPI();
