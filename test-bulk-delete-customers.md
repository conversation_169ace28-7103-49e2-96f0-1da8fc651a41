# Test Bulk Delete Convert Customers API

## 🔍 Vấn đề đã được phát hiện:

**Nguyên nhân**: API bulk delete không thể xóa được khách hàng vì có **ràng buộc khóa ngoại** từ các bảng khác:

1. **user_orders** - có `user_convert_customer_id`
2. **user_converts** - có `convert_customer_id` 
3. **customer_facebook** - có `user_convert_customer_id`
4. **customer_web** - có `user_convert_customer_id`

## ✅ Giải pháp đã triển khai:

### 1. **Tạo method delete đúng cách trong Repository:**

```typescript
async deleteUserConvertCustomer(customerId: number): Promise<void> {
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Bước 1: <PERSON><PERSON><PERSON> các bảng con có khóa ngoại tham chiếu
    
    // Xóa user_orders
    await queryRunner.manager.delete('user_orders', { userConvertCustomerId: customerId });
    
    // Xóa user_converts  
    await queryRunner.manager.delete('user_converts', { convertCustomerId: customerId });
    
    // Xóa customer_facebook
    await queryRunner.manager.query(
      'DELETE FROM customer_facebook WHERE user_convert_customer_id = $1',
      [customerId]
    );
    
    // Xóa customer_web
    await queryRunner.manager.query(
      'DELETE FROM customer_web WHERE user_convert_customer_id = $1', 
      [customerId]
    );

    // Bước 2: Xóa khách hàng chuyển đổi chính
    await queryRunner.manager.delete(UserConvertCustomer, customerId);

    // Commit transaction
    await queryRunner.commitTransaction();

  } catch (error) {
    // Rollback transaction nếu có lỗi
    await queryRunner.rollbackTransaction();
    throw new Error(`Lỗi khi xóa khách hàng chuyển đổi: ${error.message}`);
  } finally {
    // Giải phóng connection
    await queryRunner.release();
  }
}
```

### 2. **Cập nhật Service để sử dụng method mới:**

```typescript
// Thay đổi từ:
await this.userConvertCustomerRepository.delete(customerId);

// Thành:
await this.userConvertCustomerRepository.deleteUserConvertCustomer(customerId);
```

## 🧪 Test Case:

### Request:
```http
DELETE https://v2.redai.vn/api/v1/user/convert-customers/bulk
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "customerIds": [20, 19]
}
```

### Expected Response (sau khi sửa):
```json
{
  "code": 200,
  "message": "Xóa thành công 2/2 khách hàng chuyển đổi",
  "result": {
    "totalRequested": 2,
    "successCount": 2,
    "failureCount": 0,
    "results": [
      {
        "customerId": 20,
        "status": "success",
        "message": "Xóa khách hàng chuyển đổi thành công"
      },
      {
        "customerId": 19,
        "status": "success", 
        "message": "Xóa khách hàng chuyển đổi thành công"
      }
    ]
  }
}
```

## 🔧 Thứ tự xóa dữ liệu:

1. **user_orders** (xóa đơn hàng của khách hàng)
2. **user_converts** (xóa lịch sử chuyển đổi)
3. **customer_facebook** (xóa thông tin Facebook)
4. **customer_web** (xóa thông tin truy cập web)
5. **user_convert_customers** (xóa khách hàng chính)

## 📝 Lưu ý:

- Sử dụng **Transaction** để đảm bảo tính toàn vẹn dữ liệu
- **Rollback** nếu có lỗi xảy ra ở bất kỳ bước nào
- **Hard delete** - xóa vĩnh viễn khỏi database
- Xử lý từng khách hàng một để có thể báo cáo chi tiết

**API bây giờ sẽ xóa được khách hàng thành công!** 🎯
